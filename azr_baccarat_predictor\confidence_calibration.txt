# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2126 à 2160
# Type: Méthode de la classe AZRConfig

    def confidence_calibration(self):
        return {
            # Facteur de calibration (λ dans C(y|x) = 1 - exp(-λ * max_prob))
            'min_calibration_factor': self.min_calibration_factor,     # Facteur minimum
            'max_calibration_factor': self.max_calibration_factor,     # Facteur maximum
            'default_calibration_factor': self.default_calibration_factor, # Facteur par défaut

            # Pondération des composants de confiance
            'calibrated_confidence_weight': self.calibrated_confidence_weight,    # 40% - Confiance calibrée AZR
            'risk_factors_weight': self.risk_factors_weight,            # 25% - Facteurs de risque TRR++
            'uncertainty_weight': self.uncertainty_weight,              # 20% - Incertitude épistémique
            'consensus_weight': self.consensus_weight,               # 15% - Consensus inter-rollouts

            # Ajustements conditionnels
            'exploitation_bonus': self.exploitation_bonus_factor,             # +8% si exploitation prête
            'high_alert_bonus': self.high_alert_bonus_factor,               # +12% pour alerte haute
            'low_alert_malus': self.low_alert_malus_factor,                # -8% pour alerte faible

            # Paramètres d'incertitude épistémique
            'optimal_sample_size': self.optimal_sample_size,             # Taille optimale échantillon
            'optimal_correlations': self.optimal_correlations,              # Nombre optimal corrélations
            'optimal_indices': self.optimal_indices,                   # Nombre optimal indices
            'max_uncertainty_impact': self.max_uncertainty_impact,          # Impact max incertitude (-30%)

            # Paramètres de facteurs de risque
            'variance_risk_multiplier': self.scale_factor_four,        # Multiplicateur risque variance
            'signal_risk_multiplier': self.normalization_base / 3.33,          # Multiplicateur risque signaux
            'optimal_confidence_zones': self.optimal_confidence_zones,          # Nombre optimal zones confiance

            # Pondération des facteurs de risque
            'variance_risk_weight': self.variance_risk_weight,            # 30% - Risque variance évaluations
            'analysis_risk_weight': self.analysis_risk_weight,            # 30% - Risque qualité analyse
            'signal_risk_weight': self.signal_risk_weight,             # 25% - Risque signaux contradictoires
            'zone_risk_weight': self.zone_risk_weight                # 15% - Risque zones insuffisantes
        }
MÉTHODE : _classify_combined_transition_type
LIGNE DÉBUT : 10291
SIGNATURE : def _classify_combined_transition_type(self, from_state: str, to_state: str) -> str:
================================================================================

    def _classify_combined_transition_type(self, from_state: str, to_state: str) -> str:
"""
    ADAPTATION BCT - _classify_combined_transition_type.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Classifie le type de transition entre deux états combinés

        Args:
            from_state: État de départ (ex: 'IMpair_4_sync, pair_6_sync')
            to_state: État d'arrivée (ex: 'pair_4_desync, pair_6_desync')

        Returns:
            Type de transition classifié
        """

        # Parser les états
        from_parts = from_state.split('_')
        to_parts = to_state.split('_')

        if len(from_parts) != 2 or len(to_parts) != 2:
            return 'UNKNOWN_TRANSITION'

        from_impair_pair = from_parts[0]  # IMPAIR ou PAIR
        from_sync = from_parts[1]         # SYNC ou DESYNC
        to_impair_pair = to_parts[0]      # IMPAIR ou PAIR
        to_sync = to_parts[1]             # SYNC ou DESYNC

        # Classifier le type de changement
        impair_pair_changed = from_impair_pair != to_impair_pair
        sync_changed = from_sync != to_sync

        if impair_pair_changed and sync_changed:
            return 'COMPLETE_CHANGE'      # Changement complet (ex: IMpair_4_sync, pair_6_sync → pair_4_desync, pair_6_desync)
        elif impair_pair_changed and not sync_changed:
            return 'IMPAIR_PAIR_CHANGE'   # Changement IMPAIR/PAIR seulement
        elif not impair_pair_changed and sync_changed:
            return 'SYNC_CHANGE'          # Changement SYNC/DESYNC seulement
        else:
            return 'NO_CHANGE'            # Pas de changement (ne devrait pas arriver)


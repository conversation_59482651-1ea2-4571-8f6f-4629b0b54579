MÉTHODE : _analyze_pair4_pair6_priority_2_autonomous
LIGNE DÉBUT : 641
SIGNATURE : def _analyze_pair4_pair6_priority_2_autonomous(self, hands_data: List, impair_bias_analysis: Dict) -> Dict:
================================================================================

    def _analyze_pair4_pair6_priority_2_autonomous(self, hands_data: List, impair_bias_analysis: Dict) -> Dict:
"""
    ADAPTATION BCT - _analyze_pair_priority_2_autonomous.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        PRIORITÉ 2 : Analyse AUTONOME des PAIRS en contexte des IMPAIRS

        LOGIQUE AUTONOME :
        - Analyse les PAIRS en relation avec les IMPAIRS détectés
        - Mesure l'impact des PAIRS sur la stabilité du système
        - Génère des signaux de contexte pour les PAIRS
        - COMPLÈTEMENT INDÉPENDANT (pas de dépendance externe)
        """
        import statistics

        pair_bias = {
            'pair_stability_analysis': {},
            'pair_context_after_impairs': {},
            'pair_sequence_patterns': [],
            'pair_exploitation_signals': [],
            'priority_2_confidence': self.config.zero_value,
            'pair_bias_strength': self.config.zero_value
        }

        # Extraction des positions PAIRS
        pair_positions = []
        impair_positions = impair_bias_analysis.get('isolated_impairs', [])
        consecutive_impair_sequences = impair_bias_analysis.get('consecutive_impair_sequences', [])

        # Identifier toutes les positions PAIRS
        for hand_number in range(self.config.one_value, len(hands_data) + self.config.one_value):
            if hand_number % self.config.two_value == self.config.zero_value:  # Position PAIR
                pair_positions.append(hand_number)

        # Analyse des PAIRS en contexte des IMPAIRS
        pairs_after_impairs = []
        pairs_before_impairs = []

        # PAIRS après IMPAIRS isolés
        for impair_pos in impair_positions:
            next_pair_pos = impair_pos + self.config.one_value
            if next_pair_pos in pair_positions and next_pair_pos <= len(hands_data):
                pairs_after_impairs.append(next_pair_pos)

        # PAIRS après séquences d'IMPAIRS
        for seq in consecutive_impair_sequences:
            last_impair_pos = max(seq)
            next_pair_pos = last_impair_pos + self.config.one_value
            if next_pair_pos in pair_positions and next_pair_pos <= len(hands_data):
                pairs_after_impairs.append(next_pair_pos)

        # PAIRS avant IMPAIRS (contexte prédictif)
        for impair_pos in impair_positions:
            prev_pair_pos = impair_pos - self.config.one_value
            if prev_pair_pos in pair_positions and prev_pair_pos > self.config.zero_value:
                pairs_before_impairs.append(prev_pair_pos)

        pair_bias['pair_context_after_impairs'] = {
            'pairs_after_isolated_impairs': len([p for p in pairs_after_impairs if p - self.config.one_value in impair_positions]),
            'pairs_after_impair_sequences': len(pairs_after_impairs) - len([p for p in pairs_after_impairs if p - self.config.one_value in impair_positions]),
            'pairs_before_impairs': len(pairs_before_impairs),
            'total_contextual_pairs': len(pairs_after_impairs) + len(pairs_before_impairs)
        }

        # Analyse de la stabilité des PAIRS (écart-type, pas moyennes)
        if len(pair_positions) > self.config.sample_size_minimum_3:
            # Mesurer les intervalles entre PAIRS
            pair_intervals = []
            for i in range(self.config.one_value, len(pair_positions)):
                interval = pair_positions[i] - pair_positions[i - self.config.one_value]
                pair_intervals.append(interval)

            if len(pair_intervals) > self.config.one_value:
                pair_stability = statistics.stdev(pair_intervals)
                pair_bias['pair_stability_analysis'] = {
                    'interval_std_dev': pair_stability,
                    'stability_score': max(self.config.zero_value, self.config.one_value - (pair_stability / self.config.normalization_factor_4)),
                    'total_pairs': len(pair_positions),
                    'average_interval': sum(pair_intervals) / len(pair_intervals)
                }
                pair_bias['pair_bias_strength'] = pair_stability / self.config.normalization_factor_4

        # Génération signaux d'exploitation pour PAIRS
        exploitation_signals = []

        # Signal basé sur le contexte IMPAIR → PAIR
        if len(pairs_after_impairs) > self.config.zero_value:
            signal_strength = min(self.config.one_value, len(pairs_after_impairs) / max(self.config.one_value, len(impair_positions)))
            exploitation_signals.append({
                'signal_type': 'pair_after_impair_context',
                'signal_strength': signal_strength,
                'confidence': signal_strength * self.config.confidence_multiplier_08,
                'exploitation_guidance': 'PAIRS stabilisateurs après IMPAIRS détectés'
            })

        # Signal basé sur la stabilité des PAIRS
        if pair_bias['pair_bias_strength'] > self.config.zero_value:
            stability_signal = min(self.config.one_value, pair_bias['pair_bias_strength'])
            exploitation_signals.append({
                'signal_type': 'pair_stability_pattern',
                'signal_strength': stability_signal,
                'confidence': stability_signal * self.config.confidence_multiplier_06,
                'exploitation_guidance': 'Pattern de stabilité PAIRS exploitable'
            })

        pair_bias['pair_exploitation_signals'] = exploitation_signals

        # Calcul confiance globale PRIORITÉ 2
        total_signal_strength = sum(signal.get('signal_strength', self.config.zero_value) for signal in exploitation_signals)
        context_bonus = len(pairs_after_impairs) / max(self.config.one_value, len(hands_data)) * self.config.context_bonus_factor

        pair_bias['priority_2_confidence'] = min(self.config.one_value, total_signal_strength + context_bonus)

        return pair_bias


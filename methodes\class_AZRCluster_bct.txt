# CLASSE ADAPTÉE POUR BCT.PY
# Source: class_AZRCluster.txt (AZR)
# Adaptation: Système de comptage BCT avec INDEX détaillés - Cluster 0 uniquement

class AZRClusterBCT:
    """
    Cluster AZR BCT - Unité de base du système adapté pour BCT
    
    ADAPTATION BCT - CLUSTER 0 UNIQUEMENT :
    - CONTIENT 3 rollouts spécialisés BCT (Analyseur, Générateur, Prédicteur)
    - EXÉCUTE pipeline séquentiel avec INDEX détaillés BCT
    - GÈRE mémoire partagée locale avec catégories BCT
    - COORDONNE timing optimal (170ms : 60+50+60)
    - COMMUNIQUE avec système BCT principal
    
    Architecture BCT validée :
    - 1 cluster unique (Cluster 0)
    - 3 rollouts spécialisés BCT par cluster
    - Pipeline séquentiel optimisé BCT
    - Communication shared memory locale BCT
    - Exploitation biais structurels BCT
    """
    
    def __init__(self, config: AZRConfig, predictor_instance=None):
        """
        Initialise le cluster BCT unique (Cluster 0)
        
        Args:
            config: Configuration AZR centralisée
            predictor_instance: Référence au prédicteur principal BCT
        """
        self.cluster_id = config.zero_value  # Cluster 0 uniquement
        self.config = config
        self.predictor = predictor_instance  # Référence au prédicteur principal BCT
        
        # Timing optimal par phase BCT (170ms total)
        self.phase_timings = {
            'analysis': self.config.cluster_analysis_time_ms,      # 0-60ms : Analyse biais BCT
            'generation': self.config.cluster_generation_time_ms,  # 60-110ms : Génération séquences BCT
            'prediction': self.config.cluster_prediction_time_ms   # 110-170ms : Prédiction finale BCT
        }
        
        # Communication intra-cluster BCT (shared memory)
        self.shared_memory = {
            'analyzer_report_bct': None,
            'generated_sequences_bct': None,
            'final_prediction_bct': None,
            'cluster_confidence_bct': self.config.zero_value,
            'bct_bias_exploitation': {},
            'bct_categories_analyzed': ['pair_4', 'impair_5', 'pair_6'],
            'bct_combined_states': [
                'pair_4_sync', 'pair_4_desync',
                'impair_5_sync', 'impair_5_desync',
                'pair_6_sync', 'pair_6_desync'
            ]
        }
        
        # Métriques cluster BCT
        self.cluster_metrics = {
            'total_executions': self.config.zero_value,
            'average_execution_time': self.config.zero_value,
            'bct_bias_success_rate': self.config.zero_value,
            'bct_prediction_accuracy': self.config.zero_value,
            'impair_5_exploitation_rate': self.config.zero_value,
            'pair_46_alternation_rate': self.config.zero_value
        }
        
        # Spécialisation BCT (exploitation biais structurels)
        self.bct_specialization = {
            'focus': 'structural_bias_exploitation',
            'primary_target': 'impair_5_consecutive',
            'secondary_target': 'pair_4_6_alternation',
            'tertiary_target': 'combined_rare_states',
            'bias_threshold': self.config.bct_bias_detection_threshold,
            'exploitation_confidence': self.config.bct_exploitation_base_confidence
        }
        
        logger.info(f"🎯 Cluster BCT {self.cluster_id} initialisé avec spécialisation biais structurels")
        logger.info(f"📊 Catégories BCT: {self.shared_memory['bct_categories_analyzed']}")
    
    def execute_cluster_pipeline_bct(self, standardized_sequence: Dict) -> Dict:
        """
        Pipeline principal du cluster BCT
        
        Exécute les 3 rollouts BCT en séquence avec timing optimal :
        1. Rollout Analyseur BCT (0-60ms) - Analyse biais structurels
        2. Rollout Générateur BCT (60-110ms) - Génération séquences candidates
        3. Rollout Prédicteur BCT (110-170ms) - Prédiction finale S/O
        
        Args:
            standardized_sequence: Séquence complète depuis brûlage avec INDEX BCT
            
        Returns:
            Dict: Prédiction finale du cluster BCT avec confiance
        """
        import time
        start_time = time.time()
        
        try:
            # ================================================================
            # PHASE 1 : ROLLOUT ANALYSEUR BCT (0-60ms)
            # ================================================================
            phase_start = time.time()
            
            analyzer_report_bct = self._rollout_analyzer_bct(standardized_sequence)
            self.shared_memory['analyzer_report_bct'] = analyzer_report_bct
            
            analysis_time = (time.time() - phase_start) * self.config.milliseconds_conversion_factor
            
            if analysis_time > self.phase_timings['analysis']:
                logger.warning(f"⚠️ Cluster BCT {self.cluster_id}: Analyse dépassée ({analysis_time:.1f}ms > {self.phase_timings['analysis']}ms)")
            
            # ================================================================
            # PHASE 2 : ROLLOUT GÉNÉRATEUR BCT (60-110ms)
            # ================================================================
            phase_start = time.time()
            
            generator_result_bct = self._rollout_generator_bct(analyzer_report_bct)
            self.shared_memory['generated_sequences_bct'] = generator_result_bct
            
            generation_time = (time.time() - phase_start) * self.config.milliseconds_conversion_factor
            
            if generation_time > self.phase_timings['generation']:
                logger.warning(f"⚠️ Cluster BCT {self.cluster_id}: Génération dépassée ({generation_time:.1f}ms > {self.phase_timings['generation']}ms)")
            
            # ================================================================
            # PHASE 3 : ROLLOUT PRÉDICTEUR BCT (110-170ms)
            # ================================================================
            phase_start = time.time()
            
            final_prediction_bct = self._rollout_predictor_bct(generator_result_bct, analyzer_report_bct)
            self.shared_memory['final_prediction_bct'] = final_prediction_bct
            
            prediction_time = (time.time() - phase_start) * self.config.milliseconds_conversion_factor
            
            if prediction_time > self.phase_timings['prediction']:
                logger.warning(f"⚠️ Cluster BCT {self.cluster_id}: Prédiction dépassée ({prediction_time:.1f}ms > {self.phase_timings['prediction']}ms)")
            
            # ================================================================
            # FINALISATION ET MÉTRIQUES BCT
            # ================================================================
            total_time = (time.time() - start_time) * self.config.milliseconds_conversion_factor
            
            # Mise à jour métriques cluster BCT
            self._update_cluster_metrics_bct(total_time, analyzer_report_bct, final_prediction_bct)
            
            # Résultat final du cluster BCT
            cluster_result_bct = {
                'cluster_id': self.cluster_id,
                'prediction': final_prediction_bct.get('next_hand_prediction', 'S'),
                'confidence': final_prediction_bct.get('cluster_confidence', self.config.zero_value),
                'sequence': final_prediction_bct.get('sequence', []),
                'strategy': final_prediction_bct.get('strategy', 'unknown_bct'),
                'justification': final_prediction_bct.get('justification', 'Prédiction générée BCT'),
                
                # Métadonnées BCT spécifiques
                'bct_metadata': {
                    'categories_analyzed': self.shared_memory['bct_categories_analyzed'],
                    'combined_states_used': self.shared_memory['bct_combined_states'],
                    'bias_exploitation': analyzer_report_bct.get('exploitation_metadata', {}),
                    'generation_strategy': generator_result_bct.get('generation_metadata', {}).get('generation_strategy', 'unknown'),
                    'bct_conformity': True
                },
                
                # Timing détaillé
                'timing': {
                    'analysis_time_ms': analysis_time,
                    'generation_time_ms': generation_time,
                    'prediction_time_ms': prediction_time,
                    'total_time_ms': total_time,
                    'within_budget': total_time <= self.config.cluster_max_execution_time_ms
                },
                
                # Métriques cluster
                'cluster_metrics': self.cluster_metrics.copy()
            }
            
            logger.info(f"✅ Cluster BCT {self.cluster_id} terminé: {total_time:.1f}ms - Prédiction: {cluster_result_bct['prediction']}")
            
            return cluster_result_bct
            
        except Exception as e:
            logger.error(f"❌ Erreur cluster BCT {self.cluster_id}: {e}")
            return {
                'cluster_id': self.cluster_id,
                'prediction': 'S',  # Fallback
                'confidence': self.config.zero_value,
                'error': str(e),
                'bct_adaptation': True
            }
    
    def _update_cluster_metrics_bct(self, execution_time: float, analyzer_report: Dict, prediction: Dict):
        """Met à jour les métriques du cluster BCT"""
        self.cluster_metrics['total_executions'] += self.config.one_value
        
        # Moyenne mobile du temps d'exécution
        if self.cluster_metrics['total_executions'] == self.config.one_value:
            self.cluster_metrics['average_execution_time'] = execution_time
        else:
            alpha = self.config.metrics_smoothing_factor
            self.cluster_metrics['average_execution_time'] = (
                alpha * execution_time + 
                (self.config.one_value - alpha) * self.cluster_metrics['average_execution_time']
            )
        
        # Taux d'exploitation des biais BCT
        bias_exploitation = analyzer_report.get('exploitation_metadata', {}).get('exploitation_confidence', self.config.zero_value)
        self.cluster_metrics['bct_bias_success_rate'] = bias_exploitation
        
        # Confiance de prédiction
        prediction_confidence = prediction.get('cluster_confidence', self.config.zero_value)
        self.cluster_metrics['bct_prediction_accuracy'] = prediction_confidence
        
        # Métriques spécifiques BCT
        structural_analysis = analyzer_report.get('structural_bias_analysis', {})
        impair5_bias = structural_analysis.get('impair5_consecutive_bias', {})
        self.cluster_metrics['impair_5_exploitation_rate'] = impair5_bias.get('exploitation_rate', self.config.zero_value)
        
        pair_bias = structural_analysis.get('pair46_alternation_bias', {})
        self.cluster_metrics['pair_46_alternation_rate'] = pair_bias.get('alternation_rate', self.config.zero_value)
    
    def get_cluster_status_bct(self) -> Dict:
        """Retourne le statut complet du cluster BCT"""
        return {
            'cluster_id': self.cluster_id,
            'specialization': self.bct_specialization,
            'shared_memory_status': {
                'analyzer_ready': self.shared_memory['analyzer_report_bct'] is not None,
                'generator_ready': self.shared_memory['generated_sequences_bct'] is not None,
                'predictor_ready': self.shared_memory['final_prediction_bct'] is not None
            },
            'metrics': self.cluster_metrics,
            'timing_budget': self.phase_timings,
            'bct_categories': self.shared_memory['bct_categories_analyzed'],
            'bct_combined_states': self.shared_memory['bct_combined_states']
        }

# ================================================================
# MÉTHODES DE ROLLOUTS BCT À IMPLÉMENTER
# ================================================================
# Ces méthodes sont définies dans les fichiers séparés :
# - _rollout_analyzer_bct() dans methodes/_rollout_analyzer_bct.txt
# - _rollout_generator_bct() dans methodes/_rollout_generator_bct.txt  
# - _rollout_predictor_bct() dans methodes/_rollout_predictor_bct.txt
# ================================================================

# ================================================================
# CONFIGURATION BCT CENTRALISÉE
# ================================================================
# Tous les paramètres doivent être dans AZRConfig :
# - Timing des phases (cluster_analysis_time_ms, cluster_generation_time_ms, etc.)
# - Seuils de biais (bct_bias_detection_threshold, bct_exploitation_base_confidence)
# - Facteurs de lissage (metrics_smoothing_factor)
# - Limites d'exécution (cluster_max_execution_time_ms)
# ================================================================

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 BCT - BACCARAT COUNTING TOOL
================================================================================

Outil de comptage et prédiction Baccarat basé sur l'architecture AZR
VERSION STRUCTURE DE BASE : Interface graphique + Système de comptage

COMPOSANTS OPÉRATIONNELS :
- AZRConfig bien structuré
- Système de comptage conforme à systeme_comptage_baccarat_complet.txt
- Interface graphique complètement fonctionnelle (prédictions S/O)
- Ossature des classes (sans méthodes complexes)

AUTEUR : AZR System
DATE : 2025
VERSION : 2.0.0 (BCT)
================================================================================
"""

import os
import sys
import json
import logging
import threading
import multiprocessing
from typing import Dict, List, Optional, Tuple, Any, Iterator
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import tkinter as tk
from tkinter import ttk, messagebox

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bct.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

class AZRConfig:
    """
    Configuration centralisée pour tous les paramètres AZR
    
    PRINCIPE : Aucune valeur codée en dur dans les méthodes
    Toutes les spécialisations des 8 clusters centralisées ici
    """
    
    def __init__(self):
        # ====================================================================
        # PARAMÈTRES SYSTÈME FONDAMENTAUX
        # ====================================================================
        
        # Architecture clusters
        self.nb_clusters = 8
        self.nb_rollouts_per_cluster = 3
        self.nb_cores = multiprocessing.cpu_count()  # 8 cœurs
        self.max_memory_gb = 28
        
        # Valeurs de base universelles
        self.zero_value = 0
        self.one_value = 1
        self.two_value = 2
        self.three_value = 3
        
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (5 INDEX)
        # ====================================================================
        
        # INDEX 1 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }
        
        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }
        
        # INDEX 2 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }
        
        # INDEX 3 : États combinés (INDEX 1 + INDEX 2)
        self.combined_states = [
            'pair_4_sync', 'pair_4_desync',
            'pair_6_sync', 'pair_6_desync', 
            'impair_5_sync', 'impair_5_desync'
        ]
        
        # INDEX 4 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs S/O
        
        # INDEX 5 : Conversions S/O
        self.so_conversions = ['S', 'O', '--']  # Same, Opposite, Première manche
        
        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================
        
        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE
        
        # ====================================================================
        # SPÉCIALISATIONS DES 8 CLUSTERS
        # ====================================================================
        
        self.cluster_specializations = {
            0: {  # CLUSTER RÉFÉRENCE (comportement par défaut)
                'name': 'Reference',
                'focus': 'balanced',
                'pattern_length': 'medium',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            1: {  # CLUSTER IDENTIQUE À C0 (redondance sécurité)
                'name': 'Reference_Backup',
                'focus': 'balanced',
                'pattern_length': 'medium', 
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            2: {  # CLUSTER PATTERNS COURTS
                'name': 'Short_Patterns',
                'focus': 'short_patterns',
                'pattern_length': 'short',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'high'
            },
            3: {  # CLUSTER PATTERNS MOYENS
                'name': 'Medium_Patterns', 
                'focus': 'medium_patterns',
                'pattern_length': 'medium',
                'confidence_threshold': 0.55,
                'risk_tolerance': 'medium'
            },
            4: {  # CLUSTER PATTERNS LONGS
                'name': 'Long_Patterns',
                'focus': 'long_patterns', 
                'pattern_length': 'long',
                'confidence_threshold': 0.45,
                'risk_tolerance': 'low'
            },
            5: {  # CLUSTER CORRÉLATIONS
                'name': 'Correlations',
                'focus': 'correlations',
                'pattern_length': 'variable',
                'confidence_threshold': 0.65,
                'risk_tolerance': 'medium'
            },
            6: {  # CLUSTER SYNC/DESYNC
                'name': 'Sync_Desync',
                'focus': 'sync_analysis',
                'pattern_length': 'medium',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'medium'
            },
            7: {  # CLUSTER ADAPTATIF
                'name': 'Adaptive',
                'focus': 'adaptive',
                'pattern_length': 'adaptive',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'adaptive'
            }
        }
    
    def get_cluster_params(self, cluster_id: int) -> Dict[str, Any]:
        """
        Retourne les paramètres spécialisés pour un cluster donné
        
        MÉTHODE UNIVERSELLE : Utilisée par toutes les méthodes pour obtenir
        leurs paramètres spécialisés sans conditions if cluster_id ==
        """
        if cluster_id not in self.cluster_specializations:
            logger.warning(f"Cluster {cluster_id} non défini, utilisation cluster 0")
            cluster_id = 0
            
        return self.cluster_specializations[cluster_id].copy()
    
    def get_system_limits(self) -> Dict[str, int]:
        """Retourne les limites système pour optimisation mémoire/CPU"""
        return {
            'nb_clusters': self.nb_clusters,
            'nb_rollouts_per_cluster': self.nb_rollouts_per_cluster,
            'total_rollouts': self.nb_clusters * self.nb_rollouts_per_cluster,
            'nb_cores': self.nb_cores,
            'max_memory_gb': self.max_memory_gb,
            'memory_per_cluster_mb': (self.max_memory_gb * 1024) // self.nb_clusters
        }

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE                      #
#                                                                              #
################################################################################

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int                    # Numéro de main (incluant TIE)
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'
    
    # INDEX 3 : État combiné (INDEX 1 + INDEX 2)
    combined_state: str                 # 'pair_4_sync', 'impair_5_desync', etc.
    
    # INDEX 4 : Résultat
    result: str                         # 'PLAYER', 'BANKER', 'TIE'
    
    # INDEX 5 : Conversion S/O (seulement pour P/B)
    so_conversion: str                  # 'S', 'O', '--'
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_pb_hand(self) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        return self.result in ['PLAYER', 'BANKER']
    
    def is_tie_hand(self) -> bool:
        """Vérifie si c'est un TIE"""
        return self.result == 'TIE'

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    so_conversions: int = 0             # Conversions S/O calculées
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)
        self.total_hands += 1
        
        if hand.is_pb_hand():
            self.pb_hands += 1
            if hand.so_conversion in ['S', 'O']:
                self.so_conversions += 1
        else:
            self.tie_hands += 1
    
    def is_complete(self, max_pb_hands: int = 60) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        return [hand.result for hand in self.hands if hand.is_pb_hand()]
    
    def get_so_sequence(self) -> List[str]:
        """Retourne la séquence S/O (sans '--')"""
        return [hand.so_conversion for hand in self.hands
                if hand.so_conversion in ['S', 'O']]

################################################################################
#                                                                              #
#  ⚙️ SECTION 3 : MOTEUR DE COMPTAGE OPÉRATIONNEL                              #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt

    Calcule les 5 INDEX de comptage pour chaque main
    OPÉRATIONNEL et TESTÉ
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")

    def calculate_cards_distributed(self, total_cards: int, cards_category: str = None) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie

        Args:
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée (optionnel)

        Returns:
            Tuple[total_cards, parity, category]
        """
        if total_cards % 2 == 0:
            parity = 'PAIR'
            if total_cards == 4:
                category = 'pair_4'
            elif total_cards == 6:
                category = 'pair_6'
            else:
                # Cas exceptionnels (ne devrait pas arriver pour les mains normales)
                category = f'pair_{total_cards}'
        else:
            parity = 'IMPAIR'
            if total_cards == 5:
                category = 'impair_5'
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'

        # Utiliser la catégorie pré-calculée si fournie
        if cards_category:
            category = cards_category

        return total_cards, parity, category

    def calculate_sync_state(self, current_sync_state: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC

        LOGIQUE :
        - Nombre PAIR de cartes → CONSERVE l'état
        - Nombre IMPAIR de cartes → CHANGE l'état
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return current_sync_state
        else:
            # IMPAIR change l'état
            return 'DESYNC' if current_sync_state == 'SYNC' else 'SYNC'

    def calculate_so_conversion(self, current_result: str, last_pb_result: Optional[str]) -> str:
        """
        Calcule INDEX 5 : conversion S/O

        LOGIQUE :
        - S (Same) : Même résultat que la dernière manche P/B
        - O (Opposite) : Résultat opposé à la dernière manche P/B
        - '--' : Première manche P/B ou TIE
        """
        if current_result == 'TIE':
            return '--'  # TIE n'a pas de conversion S/O

        if last_pb_result is None:
            return '--'  # Première manche P/B

        if current_result == last_pb_result:
            return 'S'   # Same
        else:
            return 'O'   # Opposite

    def process_hand(self, game: BaccaratGame, result: str,
                    total_cards: int, cards_category: str) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index

        MÉTHODE UNIVERSELLE : Utilisée par tous les clusters

        Args:
            game: Partie en cours
            result: 'PLAYER', 'BANKER', 'TIE'
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée ('pair_4', 'impair_5', 'pair_6')
        """
        # INDEX 1 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            total_cards, cards_category
        )

        # INDEX 2 : Nouvel état SYNC/DESYNC
        new_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )

        # INDEX 3 : État combiné
        combined_state = f"{cards_category}_{new_sync_state.lower()}"

        # INDEX 5 : Conversion S/O
        so_conversion = self.calculate_so_conversion(result, game.last_pb_result)

        # Numéro de manche P/B
        pb_hand_number = None
        if result in ['PLAYER', 'BANKER']:
            pb_hand_number = game.pb_hands + 1  # Prochaine manche P/B

        # Créer la main
        hand = BaccaratHand(
            hand_number=game.total_hands + 1,
            pb_hand_number=pb_hand_number,
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            sync_state=new_sync_state,
            combined_state=combined_state,
            result=result,
            so_conversion=so_conversion
        )

        # Mettre à jour l'état du jeu
        game.current_sync_state = new_sync_state
        if result in ['PLAYER', 'BANKER']:
            game.last_pb_result = result

        # Ajouter la main au jeu
        game.add_hand(hand)

        self.logger.debug(f"Main traitée: {hand}")

        return hand

################################################################################
#                                                                              #
#  🎯 SECTION 4 : OSSATURE ROLLOUTS (STRUCTURE SEULEMENT)                     #
#                                                                              #
################################################################################

class UniversalRollout:
    """
    Classe de base pour tous les rollouts

    OSSATURE SEULEMENT - Méthodes à implémenter plus tard
    """

    def __init__(self, cluster_id: int, rollout_id: int, config: AZRConfig):
        self.cluster_id = cluster_id
        self.rollout_id = rollout_id
        self.config = config
        self.cluster_params = config.get_cluster_params(cluster_id)
        self.logger = logging.getLogger(f"{__name__}.Cluster{cluster_id}.Rollout{rollout_id}")

    def get_rollout_name(self) -> str:
        """Retourne le nom du rollout pour identification"""
        cluster_name = self.cluster_params.get('name', f'Cluster{self.cluster_id}')
        return f"{cluster_name}_R{self.rollout_id}"

class AnalyzerRollout(UniversalRollout):
    """
    ROLLOUT 1 : ANALYSEUR UNIVERSEL

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 1, config)

    def analyze_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Analyse l'état du jeu
        """
        # Retour minimal pour l'instant
        return {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'status': 'not_implemented',
            'timestamp': datetime.now()
        }

class GeneratorRollout(UniversalRollout):
    """
    ROLLOUT 2 : GÉNÉRATEUR UNIVERSEL

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 2, config)

    def generate_sequences(self, analysis: Dict[str, Any], game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Génère des séquences candidates
        """
        # Retour minimal pour l'instant
        return {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'status': 'not_implemented',
            'sequences': [],
            'timestamp': datetime.now()
        }

class PredictorRollout(UniversalRollout):
    """
    ROLLOUT 3 : PRÉDICTEUR UNIVERSEL

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 3, config)

    def predict_next_hand(self, analysis: Dict[str, Any], generation: Dict[str, Any],
                         game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Prédit la prochaine conversion S/O

        IMPORTANT : Prédit S (Same) ou O (Opposite), PAS P/B/T
        """
        # Retour minimal pour l'instant - prédiction S/O
        return {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'status': 'not_implemented',
            'next_so_prediction': 'WAIT',  # S, O, ou WAIT
            'prediction_confidence': 0.0,
            'timestamp': datetime.now()
        }

################################################################################
#                                                                              #
#  🏗️ SECTION 5 : OSSATURE CLUSTERS (STRUCTURE SEULEMENT)                    #
#                                                                              #
################################################################################

class AZRCluster:
    """
    Cluster AZR contenant 3 rollouts universels

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        self.cluster_id = cluster_id
        self.config = config
        self.cluster_params = config.get_cluster_params(cluster_id)
        self.logger = logging.getLogger(f"{__name__}.Cluster{cluster_id}")

        # Initialiser les 3 rollouts universels
        self.analyzer = AnalyzerRollout(cluster_id, config)
        self.generator = GeneratorRollout(cluster_id, config)
        self.predictor = PredictorRollout(cluster_id, config)

        self.logger.info(f"Cluster {cluster_id} initialisé: {self.cluster_params.get('name', 'Unknown')}")

    def process_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Traite l'état du jeu avec les 3 rollouts
        """
        # Retour minimal pour l'instant - prédiction S/O
        return {
            'cluster_id': self.cluster_id,
            'cluster_name': self.cluster_params.get('name', f'Cluster{self.cluster_id}'),
            'status': 'not_implemented',
            'prediction': {
                'next_so_prediction': 'WAIT',  # S, O, ou WAIT
                'prediction_confidence': 0.0
            },
            'timestamp': datetime.now()
        }

class AZRClusterManager:
    """
    Gestionnaire des 8 clusters AZR

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.ClusterManager")

        # Initialiser les 8 clusters
        self.clusters = {}
        for cluster_id in range(config.nb_clusters):
            self.clusters[cluster_id] = AZRCluster(cluster_id, config)

        self.logger.info(f"ClusterManager initialisé: {config.nb_clusters} clusters")

    def process_all_clusters(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Traite l'état du jeu avec tous les clusters
        """
        # Retour minimal pour l'instant - consensus S/O
        return {
            'cluster_results': {},
            'consensus': {
                'consensus_so_prediction': 'WAIT',  # S, O, ou WAIT
                'consensus_confidence': 0.0,
                'participating_clusters': 0
            },
            'status': 'not_implemented',
            'timestamp': datetime.now()
        }

    def get_cluster_performance_summary(self) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Retourne un résumé des performances
        """
        return {
            'cluster_summaries': {},
            'global_stats': {
                'total_predictions': 0,
                'average_accuracy': 0.0,
                'active_clusters': self.config.nb_clusters
            },
            'status': 'not_implemented'
        }

    def shutdown(self):
        """Arrêt propre du gestionnaire de clusters"""
        self.logger.info("ClusterManager arrêté")

################################################################################
#                                                                              #
#  🎮 SECTION 6 : INTERFACE GRAPHIQUE OPÉRATIONNELLE                          #
#                                                                              #
################################################################################

class AZRBaccaratInterface:
    """
    Interface graphique avec 9 boutons (3 résultats × 3 nombres de cartes)

    COMPLÈTEMENT OPÉRATIONNELLE
    Conforme aux spécifications corrigées
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.Interface")

        # Composants système
        self.counting_engine = BaccaratCountingEngine(config)
        self.cluster_manager = AZRClusterManager(config)

        # État de l'interface
        self.current_game = None
        self.burn_initialized = False

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🧠 BCT - Baccarat Counting Tool")
        self.root.geometry("1200x800")

        # Variables d'affichage
        self.prediction_var = tk.StringVar(value="WAIT")
        self.confidence_var = tk.StringVar(value="0.0%")
        self.game_stats_var = tk.StringVar(value="Partie: 0/60")
        self.explanation_var = tk.StringVar(value="Initialisez le brûlage pour commencer")

        self._create_interface()

        self.logger.info("Interface graphique initialisée")

    def _create_interface(self):
        """Crée l'interface graphique complète"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Section initialisation brûlage
        self._create_burn_section(main_frame)

        # Section affichage prédictions
        self._create_prediction_display(main_frame)

        # Section 9 boutons (3×3)
        self._create_nine_buttons_section(main_frame)

        # Section contrôles
        self._create_controls_section(main_frame)

        # Section statistiques
        self._create_stats_section(main_frame)

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation du brûlage"""
        burn_frame = ttk.LabelFrame(parent, text="🔥 Initialisation", padding="10")
        burn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(burn_frame, text="Brûlage:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Seulement 2 boutons : PAIR et IMPAIR
        buttons_frame = ttk.Frame(burn_frame)
        buttons_frame.pack(side=tk.LEFT, padx=(20, 0))

        # Bouton PAIR - fond noir, police jaune, plus petit
        pair_btn = tk.Button(buttons_frame, text="PAIR",
                            font=("Arial", 10, "bold"),
                            bg="black", fg="yellow",
                            width=8, height=1,
                            command=lambda: self._initialize_burn('PAIR'))
        pair_btn.pack(side=tk.LEFT, padx=5)

        # Bouton IMPAIR - fond noir, police jaune, plus petit
        impair_btn = tk.Button(buttons_frame, text="IMPAIR",
                              font=("Arial", 10, "bold"),
                              bg="black", fg="yellow",
                              width=8, height=1,
                              command=lambda: self._initialize_burn('IMPAIR'))
        impair_btn.pack(side=tk.LEFT, padx=5)

    def _create_prediction_display(self, parent):
        """Crée la section d'affichage des prédictions S/O"""
        pred_frame = ttk.LabelFrame(parent, text="🎯 Prédictions S/O (Same/Opposite)", padding="10")
        pred_frame.pack(fill=tk.X, pady=(0, 10))

        # Prédiction principale S/O - centrée
        main_pred_frame = ttk.Frame(pred_frame)
        main_pred_frame.pack(fill=tk.X)

        ttk.Label(main_pred_frame, text="Prédiction S/O:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Frame pour centrer la prédiction
        center_frame = ttk.Frame(main_pred_frame)
        center_frame.pack(expand=True, fill=tk.X)

        ttk.Label(center_frame, textvariable=self.prediction_var,
                 font=("Arial", 16, "bold"), foreground="blue").pack(anchor=tk.CENTER)

        # Explication S/O
        expl_frame = ttk.Frame(pred_frame)
        expl_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Label(expl_frame, textvariable=self.explanation_var,
                 font=("Arial", 9), foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

        # Confiance
        conf_frame = ttk.Frame(pred_frame)
        conf_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(conf_frame, text="Confiance:", font=("Arial", 10)).pack(side=tk.LEFT)
        ttk.Label(conf_frame, textvariable=self.confidence_var,
                 font=("Arial", 10, "bold"), foreground="green").pack(side=tk.LEFT, padx=(10, 0))

        # Statistiques partie - avec formatage personnalisé
        stats_frame = ttk.Frame(pred_frame)
        stats_frame.pack(fill=tk.X, pady=(5, 0))

        # Frame pour les statistiques avec formatage mixte
        self.stats_display_frame = ttk.Frame(stats_frame)
        self.stats_display_frame.pack(side=tk.LEFT)

        # Labels séparés pour formatage différent
        self.manche_label = ttk.Label(self.stats_display_frame, text="Manche : ", font=("Arial", 10))
        self.manche_label.pack(side=tk.LEFT)

        self.manche_numbers = ttk.Label(self.stats_display_frame, text="0 / 60", font=("Arial", 10, "bold"))
        self.manche_numbers.pack(side=tk.LEFT)

        self.other_stats = ttk.Label(self.stats_display_frame, text="", font=("Arial", 10))
        self.other_stats.pack(side=tk.LEFT)

    def _create_nine_buttons_section(self, parent):
        """
        Crée la section des 9 boutons (3 résultats × 3 nombres de cartes)
        AMÉLIORATIONS INTERFACE :
        - Suppression des étiquettes headers inutiles
        - Réduction taille boutons par 2
        - Police plus claire pour meilleure lisibilité
        """
        buttons_frame = ttk.LabelFrame(parent, text="🎲 Saisie Manches (Résultat + Nombre de cartes)", padding="15")
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Configuration grid
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)

        # Boutons pour chaque combinaison (résultat + nombre de cartes)
        # SUPPRESSION DES HEADERS INUTILES - directement les boutons
        headers = ["PLAYER", "BANKER", "TIE"]
        colors = ["#1E3A8A", "#B91C1C", "#166534"]  # Bleu, Rouge, Vert
        card_counts = [
            (4, "4 cartes totales"),
            (5, "5 cartes totales"),
            (6, "6 cartes totales")
        ]

        for row, (total_cards, card_desc) in enumerate(card_counts, start=0):  # start=0 car plus de headers
            for col, (result, color) in enumerate(zip(headers, colors)):
                btn_text = f"{result} {total_cards}\n({card_desc})"

                btn = tk.Button(buttons_frame, text=btn_text,
                              font=("Arial", 8, "bold"),  # Police plus petite mais lisible
                              bg=color, fg="#F0F0F0",      # Couleur police plus claire
                              relief="raised", bd=2,
                              height=2, width=12,          # Taille réduite par 2
                              command=lambda r=result, c=total_cards: self._process_hand(r, c))
                btn.grid(row=row, column=col, sticky="ew", padx=2, pady=2)

    def _create_controls_section(self, parent):
        """Crée la section des contrôles"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Boutons de contrôle
        ttk.Button(controls_frame, text="💾 Sauvegarder",
                  command=self._save_game).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(controls_frame, text="🔄 Nouvelle Partie",
                  command=self._new_game).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="📊 Statistiques",
                  command=self._show_statistics).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="❌ Quitter",
                  command=self._quit_application).pack(side=tk.RIGHT)

    def _create_stats_section(self, parent):
        """Crée la section des statistiques en temps réel"""
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistiques Temps Réel", padding="10")
        stats_frame.pack(fill=tk.X)

        # Créer un Text widget pour affichage des stats
        self.stats_text = tk.Text(stats_frame, height=6, width=80,
                                 font=("Courier", 9), state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)

    def _initialize_burn(self, parity: str):
        """Initialise le brûlage avec la parité seulement"""
        if self.burn_initialized:
            messagebox.showwarning("Attention", "Brûlage déjà initialisé pour cette partie")
            return

        # Créer nouvelle partie avec parité seulement
        # Le nombre exact de cartes n'est pas nécessaire, seule la parité compte
        initial_sync_state = self.config.initial_sync_mapping[parity]

        self.current_game = BaccaratGame(
            game_number=1,
            burn_cards_count=0,  # Pas important, seule la parité compte
            burn_parity=parity,
            initial_sync_state=initial_sync_state,
            current_sync_state=initial_sync_state
        )

        self.burn_initialized = True

        # Mettre à jour affichage
        self._update_display()

        self.logger.info(f"Brûlage initialisé: parité {parity} -> {initial_sync_state}")
        messagebox.showinfo("Brûlage Initialisé",
                          f"Brûlage: parité {parity}\nÉtat initial: {initial_sync_state}\n\nVous pouvez maintenant saisir les manches.")

    def _process_hand(self, result: str, total_cards: int):
        """
        Traite une main avec résultat et nombre total de cartes

        Args:
            result: 'PLAYER', 'BANKER', 'TIE' (le gagnant de la main)
            total_cards: 4, 5, ou 6 (nombre total de cartes distribuées dans la main)
        """
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser le brûlage")
            return

        if self.current_game.is_complete():
            messagebox.showinfo("Partie Terminée",
                              f"Partie complète: {self.config.max_manches_per_game} manches P/B atteintes")
            return

        try:
            # Mapping automatique vers catégorie INDEX 1
            category_mapping = {
                4: 'pair_4',     # 4 cartes totales = pair_4
                5: 'impair_5',   # 5 cartes totales = impair_5
                6: 'pair_6'      # 6 cartes totales = pair_6
            }

            cards_category = category_mapping.get(total_cards)
            if not cards_category:
                raise ValueError(f"Nombre de cartes invalide: {total_cards}")

            # Traiter la main avec le moteur de comptage
            # Le moteur calcule automatiquement tous les INDEX
            hand = self.counting_engine.process_hand(
                self.current_game, result, total_cards, cards_category
            )

            # Obtenir prédictions de tous les clusters (pour l'instant retour minimal)
            cluster_results = self.cluster_manager.process_all_clusters(self.current_game)

            # Mettre à jour affichage
            self._update_display(cluster_results)

            # Log de la main traitée
            self.logger.info(f"Main traitée: {result} {total_cards} cartes -> {hand.combined_state} {hand.so_conversion}")

        except Exception as e:
            self.logger.error(f"Erreur traitement main: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du traitement: {e}")

    def _update_display(self, cluster_results: Dict = None):
        """Met à jour l'affichage de l'interface"""
        if not self.current_game:
            return

        # Mettre à jour statistiques de partie avec formatage personnalisé
        self.manche_numbers.config(text=f"{self.current_game.pb_hands} / {self.config.max_manches_per_game}")
        self.other_stats.config(text=f" | Total: {self.current_game.total_hands} | TIE: {self.current_game.tie_hands}")

        # Mettre à jour prédictions S/O
        if cluster_results and 'consensus' in cluster_results:
            consensus = cluster_results['consensus']
            so_prediction = consensus.get('consensus_so_prediction', 'WAIT')
            confidence = consensus.get('consensus_confidence', 0.0)

            self.prediction_var.set(so_prediction)
            self.confidence_var.set(f"{confidence:.1%}")

            # Explication de la prédiction S/O
            if so_prediction == 'S':
                last_pb = self.current_game.last_pb_result or "?"
                self.explanation_var.set(f"Same: répéter {last_pb}")
            elif so_prediction == 'O':
                last_pb = self.current_game.last_pb_result or "?"
                opposite = "BANKER" if last_pb == "PLAYER" else "PLAYER" if last_pb == "BANKER" else "?"
                self.explanation_var.set(f"Opposite: jouer {opposite}")
            else:
                self.explanation_var.set("Attendre plus de données")

            # Mettre à jour statistiques détaillées
            self._update_stats_display(cluster_results)
        else:
            self.prediction_var.set("WAIT")
            self.confidence_var.set("0.0%")
            self.explanation_var.set("En attente d'analyse...")

    def _update_stats_display(self, cluster_results: Dict):
        """Met à jour l'affichage des statistiques détaillées"""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)

        # Consensus S/O
        consensus = cluster_results.get('consensus', {})
        so_prediction = consensus.get('consensus_so_prediction', 'N/A')
        confidence = consensus.get('consensus_confidence', 0.0)
        self.stats_text.insert(tk.END, f"🎯 CONSENSUS S/O: {so_prediction} ({confidence:.1%})\n\n")

        # Affichage système de comptage
        self.stats_text.insert(tk.END, f"⚙️ SYSTÈME DE COMPTAGE:\n")
        self.stats_text.insert(tk.END, f"  • État SYNC/DESYNC: {self.current_game.current_sync_state}\n")
        self.stats_text.insert(tk.END, f"  • Dernière main P/B: {self.current_game.last_pb_result or 'Aucune'}\n")

        if self.current_game.hands:
            last_hand = self.current_game.hands[-1]
            self.stats_text.insert(tk.END, f"\nDernière main traitée:\n")
            self.stats_text.insert(tk.END, f"  • Résultat: {last_hand.result}\n")
            self.stats_text.insert(tk.END, f"  • Cartes: {last_hand.cards_distributed} ({last_hand.cards_category})\n")
            self.stats_text.insert(tk.END, f"  • État SYNC: {last_hand.sync_state}\n")
            self.stats_text.insert(tk.END, f"  • État combiné: {last_hand.combined_state}\n")
            self.stats_text.insert(tk.END, f"  • Conversion S/O: {last_hand.so_conversion}\n")

        self.stats_text.insert(tk.END, f"\n📊 SÉQUENCES:\n")
        pb_sequence = self.current_game.get_pb_sequence()
        so_sequence = self.current_game.get_so_sequence()
        self.stats_text.insert(tk.END, f"  • P/B: {' '.join(pb_sequence[-10:]) if pb_sequence else 'Aucune'}\n")
        self.stats_text.insert(tk.END, f"  • S/O: {' '.join(so_sequence[-10:]) if so_sequence else 'Aucune'}\n")

        self.stats_text.config(state=tk.DISABLED)
        self.stats_text.see(tk.END)

    def _save_game(self):
        """Sauvegarde la partie actuelle"""
        if not self.current_game:
            messagebox.showwarning("Attention", "Aucune partie en cours")
            return

        try:
            # Créer données de sauvegarde
            save_data = {
                'game': {
                    'game_number': self.current_game.game_number,
                    'burn_cards_count': self.current_game.burn_cards_count,
                    'burn_parity': self.current_game.burn_parity,
                    'initial_sync_state': self.current_game.initial_sync_state,
                    'hands': [
                        {
                            'hand_number': hand.hand_number,
                            'pb_hand_number': hand.pb_hand_number,
                            'cards_distributed': hand.cards_distributed,
                            'cards_parity': hand.cards_parity,
                            'cards_category': hand.cards_category,
                            'sync_state': hand.sync_state,
                            'combined_state': hand.combined_state,
                            'result': hand.result,
                            'so_conversion': hand.so_conversion,
                            'timestamp': hand.timestamp.isoformat()
                        }
                        for hand in self.current_game.hands
                    ]
                },
                'save_timestamp': datetime.now().isoformat()
            }

            # Sauvegarder dans fichier JSON
            filename = f"bct_game_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Sauvegarde", f"Partie sauvegardée: {filename}")
            self.logger.info(f"Partie sauvegardée: {filename}")

        except Exception as e:
            self.logger.error(f"Erreur sauvegarde: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _new_game(self):
        """Démarre une nouvelle partie"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Nouvelle Partie",
                                     "Voulez-vous vraiment démarrer une nouvelle partie ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        # Réinitialiser état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.prediction_var.set("WAIT")
        self.confidence_var.set("0.0%")
        self.manche_numbers.config(text="0 / 60")
        self.other_stats.config(text=" | Total: 0 | TIE: 0")
        self.explanation_var.set("Nouvelle partie - Initialisez le brûlage")

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "🆕 Nouvelle partie initialisée\n")
        self.stats_text.insert(tk.END, "Veuillez initialiser le brûlage pour commencer\n")
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Nouvelle partie initialisée")

    def _show_statistics(self):
        """Affiche les statistiques détaillées dans une fenêtre séparée"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Statistiques Détaillées BCT")
        stats_window.geometry("800x600")

        # Text widget avec scrollbar
        text_frame = ttk.Frame(stats_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        stats_text = tk.Text(text_frame, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)

        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        stats_text.config(yscrollcommand=scrollbar.set)

        # Générer statistiques complètes
        stats_content = "🧠 STATISTIQUES BCT (Baccarat Counting Tool)\n"
        stats_content += "=" * 60 + "\n\n"

        # Configuration système
        system_limits = self.config.get_system_limits()
        stats_content += "📊 CONFIGURATION SYSTÈME:\n"
        stats_content += f"  • Clusters: {system_limits['nb_clusters']}\n"
        stats_content += f"  • Rollouts par cluster: {system_limits['nb_rollouts_per_cluster']}\n"
        stats_content += f"  • Total rollouts: {system_limits['total_rollouts']}\n"
        stats_content += f"  • Cœurs CPU: {system_limits['nb_cores']}\n"
        stats_content += f"  • Mémoire max: {system_limits['max_memory_gb']}GB\n\n"

        # Partie actuelle
        if self.current_game:
            stats_content += f"🎲 PARTIE ACTUELLE:\n"
            stats_content += f"  • Numéro: {self.current_game.game_number}\n"
            stats_content += f"  • Brûlage: parité {self.current_game.burn_parity}\n"
            stats_content += f"  • État initial: {self.current_game.initial_sync_state}\n"
            stats_content += f"  • État actuel: {self.current_game.current_sync_state}\n"
            stats_content += f"  • Manches P/B: {self.current_game.pb_hands}/{self.config.max_manches_per_game}\n"
            stats_content += f"  • Total mains: {self.current_game.total_hands}\n"
            stats_content += f"  • TIE: {self.current_game.tie_hands}\n"
            stats_content += f"  • Conversions S/O: {self.current_game.so_conversions}\n\n"

            # Détails des mains
            if self.current_game.hands:
                stats_content += f"📋 DÉTAILS DES MAINS:\n"
                for hand in self.current_game.hands[-5:]:  # 5 dernières mains
                    stats_content += f"  Main {hand.hand_number}: {hand.result} {hand.cards_distributed}c → {hand.combined_state} ({hand.so_conversion})\n"
        else:
            stats_content += "🎲 AUCUNE PARTIE EN COURS\n"

        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)

    def _quit_application(self):
        """Quitte l'application proprement"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Quitter",
                                     "Voulez-vous vraiment quitter ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        self.logger.info("Fermeture de l'application")

        # Arrêt propre du cluster manager
        self.cluster_manager.shutdown()

        # Fermer interface
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Lance l'interface graphique"""
        self.logger.info("Démarrage de l'interface graphique")

        # Gestionnaire de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self._quit_application)

        # Démarrer boucle principale
        self.root.mainloop()

################################################################################
#                                                                              #
#  🚀 SECTION 7 : FONCTION PRINCIPALE                                         #
#                                                                              #
################################################################################

def main():
    """
    Fonction principale du programme BCT (Baccarat Counting Tool)

    Initialise tous les composants et lance l'interface graphique
    """
    print("🧠 BCT - BACCARAT COUNTING TOOL")
    print("=" * 50)
    print("Initialisation du système...")

    try:
        # Initialiser configuration
        config = AZRConfig()

        # Afficher informations système
        system_limits = config.get_system_limits()
        print(f"📊 Configuration système:")
        print(f"  • Clusters: {system_limits['nb_clusters']}")
        print(f"  • Rollouts par cluster: {system_limits['nb_rollouts_per_cluster']}")
        print(f"  • Total rollouts: {system_limits['total_rollouts']}")
        print(f"  • Cœurs CPU: {system_limits['nb_cores']}")
        print(f"  • Mémoire max: {system_limits['max_memory_gb']}GB")

        print(f"\n✅ Composants opérationnels:")
        print(f"  • AZRConfig bien structuré")
        print(f"  • Système de comptage conforme")
        print(f"  • Interface graphique complète")
        print(f"  • Ossature des classes établie")

        # Initialiser et lancer interface
        print("\n🎮 Lancement de l'interface graphique...")
        interface = AZRBaccaratInterface(config)
        interface.run()

    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}", exc_info=True)
    finally:
        print("\n👋 Arrêt du programme")

if __name__ == "__main__":
    main()

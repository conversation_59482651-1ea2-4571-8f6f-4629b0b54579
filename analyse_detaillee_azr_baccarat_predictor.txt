ANALYSE DÉTAILLÉE D'AZR_BACCARAT_PREDICTOR.PY
==============================================
Objectif: Extraire les mécanismes à adapter pour le système BCT ternaire
Date: 8 juin 2025

🎯 OBJECTIFS DE L'ANALYSE
========================

MÉCANISMES À IDENTIFIER :
1. Corrélations binaires PAIR/IMPAIR → INDEX 4,5
2. Gestion des sous-fenêtres (fixes/adaptatives/multiples)
3. Calculs d'influence temporelle sync/desync
4. Système de confiance et prédictions spécifiques
5. Architecture des rollouts et clusters

ADAPTATION NÉCESSAIRE :
- ANCIEN: PAIR/IMPAIR (binaire) → NOUVEAU: pair_4/impair_5/pair_6 (ternaire)
- PAIR ancien = pair_4 + pair_6 nouveau
- IMPAIR ancien = impair_5 nouveau

📊 STRUCTURE GÉNÉRALE DU FICHIER
===============================

ANALYSE EN COURS...

🔍 SECTION 1: ARCHITECTURE GÉNÉRALE
===================================

Taille du fichier: 6850 lignes
Classes principales identifiées:
1. AZRConfig (lignes 82-500) - Configuration centralisée
2. BaccaratHand (lignes 501-520) - Structure de données
3. AZRCluster (lignes 531-1242) - Unité de base système distribué
4. AZRMaster (lignes 1247-2087) - Coordination des clusters
5. AZRBaccaratPredictor (lignes 2089-6850) - Classe principale

🔍 SECTION 2: ANALYSE DE LA CONFIGURATION (AZRConfig)
====================================================

STRUCTURE DÉCOUVERTE (lignes 198-500+):
- Configuration centralisée massive avec 14 sections
- Architecture 8 clusters × 3 rollouts = 24 rollouts parallèles
- Timing optimisé: 170ms total (60+50+60ms par rollout)

SECTIONS IDENTIFIÉES:
📊 SECTION A - Valeurs de base (lignes 247-299)
🔍 SECTION R1 - Rollout 1 Analyseur (lignes 301-394)
🎯 SECTION R2 - Rollout 2 Générateur (lignes 396-500+)
🎲 SECTION R3 - Rollout 3 Prédicteur (à analyser)
🏗️ SECTIONS C0-C7 - Clusters spécialisés (à analyser)

MÉCANISMES CLÉS DÉCOUVERTS:

1. SYSTÈME BINAIRE PAIR/IMPAIR:
   - correlation_impair_value: 1.0 (ligne 287)
   - correlation_pair_value: 0.0 (ligne 288)
   - Seuils asymétriques: IMPAIR 30× plus rare que PAIR

2. SEUILS DE BIAIS STRUCTURELS (R1.4):
   - rollout1_impair_consecutive_rare: 0.6 (60%)
   - rollout1_pair_consecutive_common: 0.3 (30%)
   - Exploitation différenciée selon rareté

3. SYNCHRONISATION/DÉSYNCHRONISATION (R1.7):
   - rollout1_sync_high_threshold: 0.7 (70%)
   - rollout1_desync_high_threshold: 0.3 (30%)
   - Mécanisme de tracking état global

4. GÉNÉRATION DE SÉQUENCES (R2):
   - rollout2_fixed_length: 3 (séquences de 3)
   - rollout2_candidates_count: 4 (4 candidats)
   - Stratégies multiples avec fallback

ADAPTATION NÉCESSAIRE POUR BCT:
- PAIR ancien → pair_4 + pair_6 nouveau
- IMPAIR ancien → impair_5 nouveau
- Seuils à recalibrer pour système ternaire

🔍 SECTION 3: MÉCANISMES DE CORRÉLATION
=======================================

MÉCANISME PRINCIPAL DÉCOUVERT (_rollout_analyzer, lignes 2536-2648):

HIÉRARCHIE D'ANALYSE DES BIAIS:
1. PRIORITÉ 1: Impairs consécutifs (rareté extrême)
2. PRIORITÉ 2: Alternance sync/desync (3ème carte)
3. PRIORITÉ 3: Combinaisons rares (impair+desync)
4. CORRÉLATION: Impact sur P/B → S/O

LOGIQUE ANTI-MOYENNES (lignes 2540-2545):
- ÉLIMINE toutes les moyennes (piège mortel)
- UTILISE les écart-types pour déviations structurelles
- PRIORISE les impairs consécutifs (30× plus rares)
- EXPLOITE l'alternance sync/desync
- CORRÈLE biais structurels avec variations P/B → S/O

MÉTHODES DE CORRÉLATION IDENTIFIÉES:

1. _analyze_impair_consecutive_bias() (lignes 2654-2880):
   - Analyse COMPLÈTE des IMPAIRS (isolés + séquences)
   - Scores d'attention progressifs SANS seuils limitants
   - Corrélation avec TOUS les autres indices (2,3,4,5)
   - Génération signaux TOUJOURS exploitables

2. _analyze_pair_priority_2_autonomous() (lignes 2882-2992):
   - Analyse AUTONOME des PAIRS en contexte IMPAIRS
   - Mesure impact PAIRS sur stabilité système
   - Signaux de contexte pour PAIRS
   - COMPLÈTEMENT INDÉPENDANT

3. _analyze_sync_alternation_bias() (lignes 2994-3035+):
   - Détecte ruptures d'alternance attendue (3ème carte)
   - Mesure écart-type alternances (PAS moyenne)
   - Corrèle avec variations P/B
   - Signal de biais exploitable

EXTRACTION DES INDEX (lignes 2689-2714):
```python
# Index 1: PAIR/IMPAIR
position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'

# Index 4: P/B/T
pbt_result = hand_data.pbt_result

# Index 2: SYNC/DESYNC
sync_state = getattr(hand_data, 'sync_state', 'SYNC')

# Index 3: COMBINED
combined_state = f"{position_type}_{sync_state}"

# Index 5: S/O
so_result = getattr(hand_data, 'so_conversion', None)
```

CORRÉLATIONS SPÉCIFIQUES:
- _correlate_impair_with_sync() → INDEX 1 → INDEX 2
- _correlate_impair_with_combined() → INDEX 1 → INDEX 3
- _correlate_impair_with_pb() → INDEX 1 → INDEX 4
- _correlate_impair_with_so() → INDEX 1 → INDEX 5

ADAPTATION POUR BCT TERNAIRE:
- Remplacer logique binaire PAIR/IMPAIR
- Adapter pour pair_4/impair_5/pair_6
- Recalibrer seuils et corrélations

🔍 SECTION 4: GESTION DES SOUS-FENÊTRES
=======================================

SYSTÈME DE FENÊTRES MULTIPLES DÉCOUVERT:

1. FENÊTRES RÉCENTES SPÉCIALISÉES PAR CLUSTER (lignes 683-690):
   - cluster0_recent_window_size: 3 (Standard)
   - cluster1_recent_window_size: 3 (Standard)
   - cluster2_recent_window_size: 2 (Patterns courts - Réactivité max)
   - cluster3_recent_window_size: 4 (Patterns moyens - Vision élargie)
   - cluster4_recent_window_size: 6 (Patterns longs - Mémoire étendue)
   - cluster5_recent_window_size: 3 (Corrélations - Équilibré)
   - cluster6_recent_window_size: 3 (Sync/Desync - Équilibré)
   - cluster7_recent_window_size: 5 (Adaptatif - Variable étendue)

2. FENÊTRES SÉQUENCES SPÉCIALISÉES (lignes 696-703):
   - Adaptées aux spécialisations de chaque cluster
   - cluster2_sequence_window: 2 (séquences courtes)
   - cluster4_sequence_window: 7 (séquences longues)

3. FENÊTRES PRÉCISION SPÉCIALISÉES (lignes 709-716):
   - cluster2_accuracy_window: 5 (monitoring réactif)
   - cluster4_accuracy_window: 15 (monitoring stable)

MÉTHODES D'ACCÈS CENTRALISÉES (lignes 1666-1730):
- get_cluster_recent_window_size(cluster_id) → Fenêtre récente optimisée
- get_cluster_sequence_window(cluster_id) → Fenêtre séquence optimisée
- get_cluster_accuracy_window(cluster_id) → Fenêtre précision optimisée

UTILISATION DANS LES CORRÉLATIONS (ligne 2866):
```python
cluster_recent_window = self.config.get_cluster_recent_window_size(self.cluster_id)
recent_lengths = sequence_lengths[-cluster_recent_window:]
```

FENÊTRES FIXES IDENTIFIÉES:
- rollout_accuracy_recent_window: 10 (ligne 895)
- performance_window_size: 100 (ligne 1297)
- accuracy_window_size: 100 (ligne 1947)
- trend_analysis_window: 50 (ligne 1948)

FENÊTRES ADAPTATIVES DANS L'ANALYSE:
- recent_pb = pbt_sequence[-3:] (ligne 7376)
- recent_accuracy = accuracy_history[-20:] (ligne 15137)
- recent_so = hands_history[-5:] (ligne 15791)
- recent_states = sync_desync_sequence[-5:] (ligne 16021)

PRINCIPE ARCHITECTURAL:
- Chaque cluster a ses fenêtres optimisées selon sa spécialisation
- Fenêtres multiples simultanées (récente, séquence, précision)
- Adaptation dynamique selon le contexte d'analyse
- Évitement des moyennes globales par fenêtrage local

🔍 SECTION 5: SYSTÈME SYNC/DESYNC
=================================

MÉCANISME SYNC/DESYNC DÉCOUVERT (_analyze_sync_alternation_bias, lignes 2994-3148):

LOGIQUE ANTI-MOYENNES:
- Détecte ruptures d'alternance attendue (3ème carte distribuée)
- Mesure écart-type des alternances (PAS la moyenne)
- Corrèle avec variations P/B
- Génère signal de biais exploitable

CALCUL SYNCHRONISATION (lignes 3020-3035):
```python
expected_pattern = ['P', 'B']  # Pattern attendu de base
for i, hand in enumerate(hands_data):
    actual_outcome = hand.pbt_result
    expected = expected_pattern[i % len(expected_pattern)]

    if actual_outcome == expected:
        sync_status = 'SYNC'
    elif actual_outcome == 'T':  # Tie = forte désynchronisation
        sync_status = 'DESYNC_TIE'
    else:
        sync_status = 'DESYNC'
```

STRUCTURE DONNÉES SYNC:
- sync_alternation_breaks: [] (ruptures détectées)
- sync_deviation_strength: float (force déviation)
- pb_impact_after_sync_breaks: {} (impact sur P/B)
- alternation_pattern_deviation: float (déviation pattern)
- exploitation_confidence: float (confiance exploitation)
- bias_persistence_score: float (persistance biais)

SPÉCIALISATIONS PAR CLUSTER:
- C2 (patterns courts): Analyse variabilité récente états SYNC/DESYNC
- C3 (patterns moyens): Équilibre réactivité/continuité
- C6 (sync/desync): Spécialisation dédiée aux ruptures

CORRÉLATIONS SYNC DANS ROLLOUTS:
- _correlate_impair_with_sync() → INDEX 1 → INDEX 2
- Influence sur prédictions P/B et S/O
- Tracking état global du sabot (sync/desync)

UTILISATION DANS PRÉDICTIONS (ligne 16021):
```python
recent_states = sync_desync_sequence[-5:]
sync_count = recent_states.count('SYNC')
desync_count = recent_states.count('DESYNC')
current_state = sync_desync_sequence[-1]
```

ADAPTATION NÉCESSAIRE POUR BCT:
- Remplacer logique binaire PAIR/IMPAIR par ternaire pair_4/impair_5/pair_6
- Adapter calcul synchronisation pour nouveau système INDEX
- Maintenir principe détection ruptures alternance

🔍 SECTION 6: ROLLOUTS ET CLUSTERS
==================================

ARCHITECTURE 3 ROLLOUTS SÉQUENTIELS DÉCOUVERTE:

ROLLOUT 1 - ANALYSEUR (_rollout_analyzer, lignes 2536-2648):
- Analyse biais structurels exploitables
- Hiérarchie: IMPAIR → SYNC/DESYNC → COMBINÉS → CORRÉLATIONS P/B → S/O
- Génère signaux optimisés pour Rollout 2
- Évite toutes les moyennes (piège mortel)
- Utilise écart-types pour déviations structurelles

ROLLOUT 2 - GÉNÉRATEUR (_rollout_generator, lignes 5656-5761):
- Génère 4 séquences candidates basées sur analyse Rollout 1
- Exploite signaux optimisés (signals_summary, generation_guidance, quick_access)
- Stratégies distinctes avec fallback
- Enrichissement avec tous les indices
- Retourne dictionnaire structuré (sequences, metadata, stats)

ROLLOUT 3 - PRÉDICTEUR (_rollout_predictor, lignes 5772-5895):
- Sélectionne séquence optimale finale
- Évalue qualité avec 6 critères:
  * signal_alignment_score (alignement signaux Rollout 1)
  * consistency_score (cohérence interne)
  * risk_reward_ratio (ratio risque/récompense)
  * logic_validation_score (validation logique)
  * total_score (score pondéré)
  * confidence_factor (facteur confiance)
- Conversion P/B → S/O finale
- Prédiction next_hand_prediction

PIPELINE OPTIMISÉ 170MS:
- Rollout 1: 60ms (analyse biais)
- Rollout 2: 50ms (génération séquences)
- Rollout 3: 60ms (sélection finale)

SPÉCIALISATIONS CLUSTERS (8 clusters × 3 rollouts = 24 rollouts parallèles):
- C0: Standard (fenêtre 3)
- C1: Standard (fenêtre 3)
- C2: Patterns courts (fenêtre 2, réactivité max)
- C3: Patterns moyens (fenêtre 4, vision élargie)
- C4: Patterns longs (fenêtre 6, mémoire étendue)
- C5: Corrélations (fenêtre 3, équilibré)
- C6: Sync/Desync (fenêtre 3, spécialisé)
- C7: Adaptatif (fenêtre 5, variable)

MÉTHODES ÉVALUATION ROLLOUT 3:
- _evaluate_sequence_quality() → Évaluation complète
- _evaluate_signal_alignment() → Alignement avec Rollout 1
- _analyze_sequence_consistency() → Cohérence interne
- _assess_risk_reward_ratio() → Équilibre risque/récompense
- _validate_sequence_logic() → Validation logique
- _select_best_sequence() → Sélection optimale

CONVERSION P/B → S/O:
- _convert_pb_sequence_to_so() → Conversion finale
- Utilise corrélations découvertes par Rollout 1
- Génère séquence S/O simple ['S', 'O', 'S']

ADAPTATION POUR BCT:
- Maintenir architecture 3 rollouts séquentiels
- Adapter corrélations binaire → ternaire
- Conserver pipeline timing 170ms
- Utiliser seulement Cluster 0 (3 rollouts)

🔍 SECTION 7: ADAPTATIONS NÉCESSAIRES POUR BCT
===============================================

TRANSFORMATIONS CRITIQUES IDENTIFIÉES:

1. ADAPTATION INDEX BINAIRE → TERNAIRE:

ANCIEN SYSTÈME (azr_baccarat_predictor.py):
```python
# Index 1: PAIR/IMPAIR (binaire)
position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'
```

NOUVEAU SYSTÈME BCT (à implémenter):
```python
# Index 1: pair_4/impair_5/pair_6 (ternaire)
total_cards = get_total_cards_distributed(hand_data)
if total_cards == 4:
    position_type = 'pair_4'
elif total_cards == 5:
    position_type = 'impair_5'
elif total_cards == 6:
    position_type = 'pair_6'
```

2. MAPPING CORRÉLATIONS:

CORRESPONDANCE DÉCOUVERTE:
- PAIR ancien = pair_4 + pair_6 nouveau
- IMPAIR ancien = impair_5 nouveau

ADAPTATIONS MÉTHODES:
- _analyze_impair_consecutive_bias() → _analyze_impair5_consecutive_bias()
- _analyze_pair_priority_2_autonomous() → _analyze_pair46_priority_2_autonomous()
- Séparer logique PAIR en deux branches distinctes (pair_4 vs pair_6)

3. CONFIGURATION CENTRALISÉE:

PARAMÈTRES À ADAPTER:
- correlation_impair_value: 1.0 → correlation_impair5_value: 1.0
- correlation_pair_value: 0.0 → correlation_pair4_value: 0.0, correlation_pair6_value: 0.0
- Nouveaux seuils pour système ternaire

4. FENÊTRES SPÉCIALISÉES:

CONSERVER POUR BCT:
- get_cluster_recent_window_size() → Cluster 0 uniquement (fenêtre 3)
- Fenêtres multiples simultanées (récente, séquence, précision)
- Évitement moyennes par fenêtrage local

5. ROLLOUTS ADAPTATION:

ROLLOUT 1 - ANALYSEUR:
- Adapter _analyze_impair_consecutive_bias() pour impair_5 uniquement
- Créer _analyze_pair4_bias() et _analyze_pair6_bias() séparément
- Maintenir _analyze_sync_alternation_bias() (compatible)
- Adapter corrélations INDEX 1 → INDEX 4,5

ROLLOUT 2 - GÉNÉRATEUR:
- Adapter génération séquences pour système ternaire
- Maintenir 4 séquences candidates
- Adapter stratégies de génération

ROLLOUT 3 - PRÉDICTEUR:
- Adapter évaluation qualité pour corrélations ternaires
- Maintenir conversion P/B → S/O
- Adapter validation logique

6. INTERFACE UTILISATEUR:

MAINTENIR:
- 9 boutons existants (Player/Banker/Tie × 4/5/6 cartes)
- Prédictions S/O (pas P/B)
- Structure tableau validation

7. TIMING ET PERFORMANCE:

CONSERVER:
- Pipeline 170ms (60+50+60ms)
- Cluster 0 uniquement (3 rollouts)
- Configuration centralisée AZRConfig

📋 PLAN D'IMPLÉMENTATION PRIORITAIRE:

ÉTAPE 1: Adapter AZRConfig pour système ternaire
ÉTAPE 2: Modifier extraction INDEX 1 (pair_4/impair_5/pair_6)
ÉTAPE 3: Séparer méthodes PAIR en pair_4 et pair_6
ÉTAPE 4: Adapter corrélations dans les 3 rollouts
ÉTAPE 5: Tester pipeline complet avec données BCT
ÉTAPE 6: Optimiser performance et timing

🎯 OBJECTIF: Créer système BCT-AZR hybride exploitant:
- Précision système ternaire BCT
- Sophistication architecture AZR
- Évitement moyennes académiques
- Exploitation biais structurels 1.8×10^-14

=== ANALYSE COMPLÈTE TERMINÉE ===

MÉTHODE : _extract_temporal_evolution_strength
LIGNE DÉBUT : 8093
SIGNATURE : def _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float:
================================================================================

    def _extract_temporal_evolution_strength(self, temporal_impacts: Dict) -> float:
"""
    ADAPTATION BCT - _extract_temporal_evolution_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Extrait la force de l'évolution temporelle des corrélations (focus P/B et S/O)"""

        if not temporal_impacts:
            return 0.0

        # Chercher métriques dans temporal_strength_metrics
        if 'temporal_strength_metrics' in temporal_impacts:
            metrics = temporal_impacts['temporal_strength_metrics']
            evolution_strength = metrics.get('evolution_strength', 0.0)
            temporal_consistency = metrics.get('temporal_consistency', 0.0)
            temporal_predictability = metrics.get('temporal_predictability', 0.0)

            # Moyenne pondérée (évolution plus importante)
            return (evolution_strength * 2 + temporal_consistency + temporal_predictability) / 4

        return 0.0


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 3346 à 3455
# Type: Méthode de la classe AZRCluster

    def _analyze_impair_consecutive_bias_c2_specialized(self, hands_data: List, position_types: List,
                                                       sync_states: List, combined_states: List,
                                                       pb_outcomes: List, so_outcomes: List) -> Dict:
        """
        🎯 C2 SPÉCIALISÉ - ANALYSE IMPAIRS avec fenêtres récentes optimisées (2 manches)

        LOGIQUE DE BASE (IDENTIQUE C0) + SPÉCIALISATION C2 :
        - Utilise la fenêtre récente spécialisée C2 (2 manches)
        - Focus sur patterns courts et réactivité maximale
        - Détection ultra-rapide des micro-changements
        """
        import statistics

        # Utiliser la méthode de base avec les données pré-extraites
        impair_bias = self._analyze_impair_consecutive_bias(hands_data)

        # ================================================================
        # SPÉCIALISATION C2 : FENÊTRES RÉCENTES OPTIMISÉES
        # ================================================================

        # Récupérer la fenêtre récente spécialisée C2 (2 manches)
        cluster_recent_window = self.config.get_cluster_recent_window_size(self.cluster_id)  # 2 pour C2

        # Appliquer la fenêtre spécialisée aux séquences d'IMPAIRS
        consecutive_sequences = impair_bias.get('consecutive_impair_sequences', [])
        if consecutive_sequences:
            # Analyser les séquences récentes avec fenêtre C2
            recent_sequences = consecutive_sequences[-cluster_recent_window:] if len(consecutive_sequences) >= cluster_recent_window else consecutive_sequences

            # Bonus spécialisation pour patterns courts détectés récemment
            c2_short_pattern_bonus = self.config.zero_value
            for seq in recent_sequences:
                seq_length = len(seq)
                if self.config.c2_short_pattern_min_length <= seq_length <= self.config.c2_short_pattern_max_length:
                    c2_short_pattern_bonus += self.config.c2_micro_change_bonus * seq_length

            # Ajouter le bonus spécialisation C2
            impair_bias['c2_short_pattern_bonus'] = min(self.config.one_value, c2_short_pattern_bonus)
            impair_bias['c2_recent_window_applied'] = cluster_recent_window
            impair_bias['c2_recent_sequences_count'] = len(recent_sequences)

        # ================================================================
        # SPÉCIALISATION C2 : RÉACTIVITÉ MAXIMALE
        # ================================================================

        # Analyser les micro-changements récents (spécialisation C2)
        if len(position_types) >= cluster_recent_window:
            recent_positions = position_types[-cluster_recent_window:]
            micro_changes = []

            for i in range(1, len(recent_positions)):
                if recent_positions[i] != recent_positions[i-1]:
                    micro_changes.append(i)

            # Bonus réactivité pour micro-changements détectés
            if micro_changes:
                reactivity_ratio = len(micro_changes) / cluster_recent_window
                if reactivity_ratio >= self.config.c2_reactivity_threshold:
                    impair_bias['c2_reactivity_bonus'] = reactivity_ratio * self.config.c2_micro_change_bonus
                else:
                    impair_bias['c2_reactivity_bonus'] = reactivity_ratio * self.config.c2_micro_change_bonus * 0.5
                impair_bias['c2_micro_changes_detected'] = len(micro_changes)
            else:
                impair_bias['c2_reactivity_bonus'] = self.config.zero_value
                impair_bias['c2_micro_changes_detected'] = 0

        # ================================================================
        # SPÉCIALISATION C2 : CORRÉLATIONS AVEC FENÊTRES OPTIMISÉES
        # ================================================================

        # Corrélations spécialisées avec fenêtres C2
        isolated_impairs = impair_bias.get('isolated_impairs', [])

        # Corrélation IMPAIR → SYNC/DESYNC avec fenêtre C2
        if len(sync_states) >= cluster_recent_window:
            recent_sync = sync_states[-cluster_recent_window:]
            sync_changes = sum(1 for i in range(1, len(recent_sync)) if recent_sync[i] != recent_sync[i-1])
            impair_bias['c2_sync_correlation_recent'] = sync_changes / max(1, cluster_recent_window - 1)

        # Corrélation IMPAIR → P/B avec fenêtre C2
        if len(pb_outcomes) >= cluster_recent_window:
            recent_pb = pb_outcomes[-cluster_recent_window:]
            pb_changes = sum(1 for i in range(1, len(recent_pb)) if recent_pb[i] != recent_pb[i-1])
            impair_bias['c2_pb_correlation_recent'] = pb_changes / max(1, cluster_recent_window - 1)

        # Corrélation IMPAIR → S/O avec fenêtre C2
        if len(so_outcomes) >= cluster_recent_window:
            recent_so = so_outcomes[-cluster_recent_window:]
            so_changes = sum(1 for i in range(1, len(recent_so)) if recent_so[i] != recent_so[i-1])
            impair_bias['c2_so_correlation_recent'] = so_changes / max(1, cluster_recent_window - 1)

        # ================================================================
        # CONFIANCE FINALE AVEC BONUS SPÉCIALISATION C2
        # ================================================================

        # Confiance de base
        base_confidence = impair_bias.get('exploitation_confidence', self.config.zero_value)

        # Bonus spécialisation C2
        c2_bonus = (
            impair_bias.get('c2_short_pattern_bonus', self.config.zero_value) * self.config.confidence_multiplier_02 +
            impair_bias.get('c2_reactivity_bonus', self.config.zero_value) * self.config.confidence_multiplier_03
        )

        # Confiance finale avec spécialisation C2
        impair_bias['exploitation_confidence'] = min(self.config.one_value, base_confidence + c2_bonus)
        impair_bias['c2_specialization_applied'] = True
        impair_bias['c2_total_bonus'] = c2_bonus

        return impair_bias
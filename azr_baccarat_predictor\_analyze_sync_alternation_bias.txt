# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2994 à 3148
# Type: Méthode de la classe AZRCluster

    def _analyze_sync_alternation_bias(self, hands_data: List) -> Dict:
        """
        PRIORITÉ 2 : Analyse des biais d'alternance sync/desync (3ème carte)

        LOGIQUE ANTI-MOYENNES :
        - Détecte les ruptures d'alternance attendue (3ème carte distribuée)
        - Mesure l'écart-type des alternances (PAS la moyenne)
        - Corrèle avec les variations P/B
        - Génère un signal de biais exploitable
        """
        import statistics

        sync_bias = {
            'sync_alternation_breaks': [],
            'sync_deviation_strength': self.config.zero_value,
            'pb_impact_after_sync_breaks': {},
            'alternation_pattern_deviation': self.config.zero_value,
            'exploitation_confidence': self.config.zero_value,
            'bias_persistence_score': self.config.zero_value
        }

        # Pattern attendu de base (alternance P/B)
        expected_pattern = ['P', 'B']
        sync_sequence = []
        pb_outcomes = []

        # Calcul synchronisation pour chaque manche
        for i, hand in enumerate(hands_data):
            actual_outcome = hand.pbt_result
            expected = expected_pattern[i % len(expected_pattern)]

            # Mesure synchronisation
            if actual_outcome == expected:
                sync_status = 'SYNC'
            elif actual_outcome == 'T':  # Tie = forte désynchronisation
                sync_status = 'DESYNC_TIE'
            else:
                sync_status = 'DESYNC'

            sync_sequence.append(sync_status)

            # Extraire P/B seulement pour corrélation
            if actual_outcome in ['P', 'B']:
                pb_outcomes.append(actual_outcome)

        # Détection des ruptures d'alternance (focus 3ème carte)
        alternation_breaks = []
        current_break_length = 0

        for i, sync_status in enumerate(sync_sequence):
            if sync_status in ['DESYNC', 'DESYNC_TIE']:
                current_break_length += 1
            else:
                if current_break_length > 0:
                    alternation_breaks.append(current_break_length)
                    current_break_length = 0

        # Fermer la dernière rupture si nécessaire
        if current_break_length > 0:
            alternation_breaks.append(current_break_length)

        sync_bias['sync_alternation_breaks'] = alternation_breaks

        # SPÉCIALISATION CLUSTER sur les ruptures d'alternance
        if self.cluster_id in [2, 3, 4]:
            specialization = self.config.cluster_pattern_specializations[self.cluster_id]
            pattern_weights = specialization['weights']

            # Pondération des ruptures selon spécialisation
            weighted_breaks = []
            for break_length in alternation_breaks:
                weight = pattern_weights.get(break_length, pattern_weights['others'])
                weighted_breaks.append(break_length * weight)

            # Recalcul avec spécialisation
            if len(weighted_breaks) > 1:
                sync_bias['sync_deviation_strength'] = statistics.stdev(weighted_breaks)
            else:
                sync_bias['sync_deviation_strength'] = self.config.zero_value

            # Ajout métadonnées spécialisation
            sync_bias['cluster_specialization'] = {
                'type': specialization['name'],
                'focus_lengths': specialization['focus_lengths'],
                'specialization_applied': True
            }
        else:
            # Calcul de l'écart-type des ruptures standard (ANTI-MOYENNE)
            if len(alternation_breaks) > 1:
                sync_bias['sync_deviation_strength'] = statistics.stdev(alternation_breaks)
            else:
                sync_bias['sync_deviation_strength'] = self.config.zero_value

        # Mesure de la déviation du pattern d'alternance global
        sync_count = sync_sequence.count('SYNC')
        desync_count = len(sync_sequence) - sync_count
        if len(sync_sequence) > 0:
            # Écart par rapport à l'alternance parfaite (50/50)
            expected_sync_ratio = self.config.half_value
            actual_sync_ratio = sync_count / len(sync_sequence)
            sync_bias['alternation_pattern_deviation'] = abs(actual_sync_ratio - expected_sync_ratio)

        # Corrélation avec P/B après ruptures de sync
        pb_after_sync_breaks = {'P': 0, 'B': 0}

        # Identifier les positions après ruptures de sync
        break_end_positions = []
        current_pos = 0
        for break_length in alternation_breaks:
            current_pos += break_length
            if current_pos < len(pb_outcomes):
                break_end_positions.append(current_pos)

        # Compter P/B après ces positions
        for pos in break_end_positions:
            if pos < len(pb_outcomes):
                outcome = pb_outcomes[pos]
                pb_after_sync_breaks[outcome] += 1

        total_after_breaks = sum(pb_after_sync_breaks.values())
        if total_after_breaks > 0:
            # UTILISATION D'ÉCART-TYPE, PAS DE MOYENNES
            pb_deviation = abs(pb_after_sync_breaks['P'] - pb_after_sync_breaks['B']) / total_after_breaks
            sync_bias['pb_impact_after_sync_breaks'] = {
                'p_count': pb_after_sync_breaks['P'],
                'b_count': pb_after_sync_breaks['B'],
                'deviation_strength': pb_deviation,
                'total_samples': total_after_breaks
            }

        # Calcul de la confiance d'exploitation
        if sync_bias['sync_deviation_strength'] > self.config.zero_value and total_after_breaks > self.config.sample_size_minimum_2:
            # Confiance basée sur l'écart-type ET la déviation d'alternance
            deviation_confidence = min(self.config.one_value, sync_bias['sync_deviation_strength'] / self.config.normalization_factor_2)
            alternation_confidence = min(self.config.one_value, sync_bias['alternation_pattern_deviation'] * self.config.normalization_factor_2)
            sample_confidence = min(self.config.one_value, total_after_breaks / self.config.normalization_factor_8)
            sync_bias['exploitation_confidence'] = (deviation_confidence + alternation_confidence + sample_confidence) / self.config.normalization_factor_3
        else:
            sync_bias['exploitation_confidence'] = self.config.zero_value

        # Score de persistance du biais
        if len(alternation_breaks) > 3:
            # Mesurer la consistance des ruptures récentes
            recent_breaks = alternation_breaks[-3:]  # 3 dernières ruptures
            if len(recent_breaks) > 1:
                recent_std = statistics.stdev(recent_breaks)
                overall_std = sync_bias['sync_deviation_strength']
                # Persistance = stabilité récente vs globale
                sync_bias['bias_persistence_score'] = max(self.config.zero_value, self.config.one_value - (recent_std / max(self.config.variance_threshold_minimum, overall_std)))
            else:
                sync_bias['bias_persistence_score'] = self.config.half_value
        else:
            sync_bias['bias_persistence_score'] = self.config.zero_value

        return sync_bias
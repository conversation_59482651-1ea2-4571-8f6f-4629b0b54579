MÉTHODE : _generate_generation_guidance
LIGNE DÉBUT : 10496
SIGNATURE : def _generate_generation_guidance(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict:
================================================================================

    def _generate_generation_guidance(self, all_indices: Dict, synthesis: Dict, signals_summary: Dict) -> Dict:
"""
    ADAPTATION BCT - _generate_generation_guidance.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Génère les directives de génération pour le Rollout 2

        FOCUS : Guidance claire pour optimiser la génération de séquences
        """
        generation_guidance = {
            'primary_focus': 'conservative',
            'secondary_focus': 'balanced',
            'avoid_patterns': [],
            'optimal_sequence_length': 4,
            'confidence_thresholds': {
                'high': 0.65,
                'medium': 0.55,
                'low': 0.45
            },
            'exploitation_strategy': 'conservative',
            'risk_level': 'medium'
        }

        # Détermination du focus principal basé sur le meilleur signal
        top_signals = signals_summary.get('top_signals', [])
        if top_signals:
            best_signal = top_signals[0]

            # Focus basé sur le type de signal le plus fort
            if best_signal['signal_type'] == 'so_prediction':
                if 'pair_4_sync, pair_6_sync' in best_signal['signal_name']:
                    generation_guidance['primary_focus'] = 'pair_4_sync, pair_6_sync_exploitation'
                    generation_guidance['exploitation_strategy'] = 'aggressive'
                elif 'IMpair_4_sync, pair_6_sync' in best_signal['signal_name']:
                    generation_guidance['primary_focus'] = 'IMpair_4_sync, pair_6_sync_exploitation'
                    generation_guidance['exploitation_strategy'] = 'moderate'
                else:
                    generation_guidance['primary_focus'] = 'combined_index_exploitation'
                    generation_guidance['exploitation_strategy'] = 'moderate'
            elif best_signal['signal_type'] == 'pb_prediction':
                if 'impair_5' in best_signal['signal_name']:
                    generation_guidance['primary_focus'] = 'IMPAIR_patterns'
                    generation_guidance['exploitation_strategy'] = 'moderate'
                else:
                    generation_guidance['primary_focus'] = 'PAIR_patterns'
                    generation_guidance['exploitation_strategy'] = 'moderate'

        # Détermination du focus secondaire
        if len(top_signals) > 1:
            second_signal = top_signals[1]
            if second_signal['signal_type'] != best_signal['signal_type']:
                if second_signal['signal_type'] == 'so_prediction':
                    generation_guidance['secondary_focus'] = 'so_patterns'
                else:
                    generation_guidance['secondary_focus'] = 'pb_patterns'

        # Identification des patterns à éviter
        avoid_patterns = []

        # Éviter les états combinés faibles
        cross_impacts = synthesis.get('cross_index_impacts', {})
        combined_to_so = cross_impacts.get('combined_to_so', {})
        state_impacts = combined_to_so.get('state_impacts', {})

        for state, impact_data in state_impacts.items():
            if impact_data.get('total_occurrences', 0) >= 3:
                to_s_ratio = impact_data.get('to_s_ratio', 0.5)
                to_o_ratio = impact_data.get('to_o_ratio', 0.5)

                # Si les ratios sont très proches de 50/50, éviter ce pattern
                if abs(to_s_ratio - 0.5) < 0.05 and abs(to_o_ratio - 0.5) < 0.05:
                    avoid_patterns.append(state)

        generation_guidance['avoid_patterns'] = avoid_patterns

        # Ajustement des seuils de confiance basé sur la qualité globale
        analysis_quality = synthesis.get('analysis_quality', 0.5)
        if analysis_quality > self.config.rollout2_base_confidence_high:
            generation_guidance['confidence_thresholds'] = {
                'high': 0.70,
                'medium': 0.60,
                'low': 0.50
            }
        elif analysis_quality < self.config.rollout2_confidence_value_standard:
            generation_guidance['confidence_thresholds'] = {
                'high': 0.60,
                'medium': 0.50,
                'low': 0.40
            }

        # Détermination de la longueur optimale de séquence
        total_hands = len(all_indices.get('pbt', {}).get('pbt_sequence', []))
        if total_hands > 50:
            generation_guidance['optimal_sequence_length'] = 5
        elif total_hands < 20:
            generation_guidance['optimal_sequence_length'] = 3

        # Évaluation du niveau de risque
        if signals_summary.get('exploitation_ready', False):
            if signals_summary.get('overall_confidence', self.config.zero_value) > self.config.confidence_very_high_threshold:
                generation_guidance['risk_level'] = 'low'  # Confiance élevée = risque faible
            else:
                generation_guidance['risk_level'] = 'medium'
        else:
            generation_guidance['risk_level'] = 'high'

        return generation_guidance


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17908 à 18002
# Type: Méthode de la classe AZRBaccaratPredictor

    def hard_reset_intelligence(self):
        """
        🔥 HARD RESET COMPLET : Supprime toute l'intelligence acquise

        Supprime :
        - Tous les fichiers de persistance
        - Toute l'intelligence acquise
        - Tous les patterns découverts
        - Tous les baselines adaptatifs
        - Toutes les métriques de performance
        - Tous les backups

        Réinitialise :
        - Tous les attributs à leurs valeurs par défaut
        - Le système AZR Master complet
        """
        import os
        import shutil

        logger.warning("🔥 HARD RESET : Suppression de toute l'intelligence AZR...")

        # 1. SUPPRIMER TOUS LES FICHIERS DE PERSISTANCE
        files_to_delete = [
            self.config.azr_state_file,
            self.config.azr_discoveries_file,
            self.config.azr_baselines_file
        ]

        for file_path in files_to_delete:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.warning(f"🗑️ Fichier supprimé: {file_path}")
            except Exception as e:
                logger.error(f"❌ Erreur suppression {file_path}: {e}")

        # 2. SUPPRIMER LE DOSSIER DE BACKUPS COMPLET
        try:
            if os.path.exists(self.config.azr_backup_dir):
                shutil.rmtree(self.config.azr_backup_dir)
                logger.warning(f"🗑️ Dossier backups supprimé: {self.config.azr_backup_dir}")
        except Exception as e:
            logger.error(f"❌ Erreur suppression backups: {e}")

        # 3. RÉINITIALISER TOUTES LES VARIABLES D'INTELLIGENCE
        self.sequence_history.clear()
        self.hands_history.clear()
        self.predictions_history.clear()
        self.accuracy_history.clear()
        self.sync_transitions.clear()

        # Réinitialiser les métriques AZR
        self.baseline_propose = float(self.config.zero_value)
        self.baseline_solve = float(self.config.zero_value)
        self.learnability_scores.clear()
        self.diversity_scores.clear()

        # Réinitialiser les patterns découverts
        self.discovered_patterns.clear()
        self.pattern_success_rates.clear()

        # Réinitialiser les compteurs de performance
        self.total_predictions = self.config.zero_value
        self.correct_predictions = self.config.zero_value
        self.current_accuracy = float(self.config.zero_value)

        # Réinitialiser l'état du système
        self.current_sync_state = "SYNC"
        self.burn_parity_set = False
        self.predictions_since_save = self.config.zero_value

        # Réinitialiser l'analyse des intervalles
        self.interval_analysis = {
            'sync_intervals': [],
            'desync_intervals': [],
            'current_interval': None
        }

        # 4. RÉINITIALISER LE SYSTÈME AZR MASTER COMPLET
        try:
            self.azr_master = AZRMaster(self.config)
            logger.warning("🔥 Système AZR Master réinitialisé")
        except Exception as e:
            logger.error(f"❌ Erreur réinitialisation AZR Master: {e}")

        # 5. RÉINITIALISER LES COMPOSANTS
        try:
            self.generator = BaccaratGenerator(self.config)
            self.data_loader = BaccaratDataLoader()
            logger.warning("🔥 Composants réinitialisés")
        except Exception as e:
            logger.error(f"❌ Erreur réinitialisation composants: {e}")

        logger.warning("🔥 HARD RESET TERMINÉ : Toute l'intelligence AZR supprimée")
        logger.info("🆕 Système reparti de zéro - Prêt pour un nouvel apprentissage")
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17762 à 17833
# Type: Méthode de la classe AZRBaccaratPredictor

    def _apply_veto_system(self, priority_distribution: Dict) -> Dict:
        """
        Système de veto pour signaux exceptionnels

        Permet aux indices 2-3 de dominer si leurs signaux sont exceptionnellement forts,
        évitant ainsi l'aveuglement par la priorité 1.

        Args:
            priority_distribution: Distribution actuelle des priorités

        Returns:
            Dict avec information sur le veto appliqué
        """
        veto_result = {'veto_applied': False}

        # Veto 1 : Signal SYNC exceptionnel
        sync_strength = priority_distribution.get('priority_3_sync', {}).get('strength', 0.0)
        if sync_strength > self.config.rollout2_veto_sync_threshold:
            veto_result = {
                'veto_applied': True,
                'veto_reason': 'SYNC_EXCEPTIONAL',
                'dominant_priority': 'priority_3_sync',
                'signal_strength': sync_strength,
                'threshold_exceeded': 0.85
            }
            return veto_result

        # Veto 2 : Signal COMBINÉ ultra-rare
        combined_strength = priority_distribution.get('priority_4_combined', {}).get('strength', 0.0)
        if combined_strength > self.config.rollout2_veto_combined_threshold:
            veto_result = {
                'veto_applied': True,
                'veto_reason': 'COMBINED_ULTRA_RARE',
                'dominant_priority': 'priority_4_combined',
                'signal_strength': combined_strength,
                'threshold_exceeded': 0.90
            }
            return veto_result

        # Veto 3 : Signal PAIRS très fort ET IMPAIRS faibles (> 70% vs < 20%)
        pair_strength = priority_distribution.get('priority_2_pair', {}).get('strength', 0.0)
        impair_strength = priority_distribution.get('priority_1_impair', {}).get('strength', 0.0)

        if pair_strength > self.config.veto_pair_strength_threshold and impair_strength < self.config.veto_pair_impair_weakness:
            veto_result = {
                'veto_applied': True,
                'veto_reason': 'PAIRS_DOMINANT_OVER_WEAK_IMPAIRS',
                'dominant_priority': 'priority_2_pair',
                'signal_strength': pair_strength,
                'threshold_exceeded': 0.70,
                'impair_weakness': impair_strength
            }
            return veto_result

        # Veto 4 : Combinaison SYNC + COMBINÉ forte vs IMPAIRS faibles
        sync_combined_strength = (sync_strength + combined_strength) / 2
        if sync_combined_strength > 0.75 and impair_strength < 0.25:
            # Déterminer lequel des deux domine
            dominant = 'priority_3_sync' if sync_strength > combined_strength else 'priority_4_combined'

            veto_result = {
                'veto_applied': True,
                'veto_reason': 'SYNC_COMBINED_COALITION_VS_WEAK_IMPAIRS',
                'dominant_priority': dominant,
                'signal_strength': max(sync_strength, combined_strength),
                'coalition_strength': sync_combined_strength,
                'threshold_exceeded': 0.75,
                'impair_weakness': impair_strength
            }
            return veto_result

        return veto_result
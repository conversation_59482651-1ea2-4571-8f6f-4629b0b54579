# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 1688 à 1708
# Type: Méthode de la classe AZRConfig

    def get_cluster_sequence_window(self, cluster_id: int) -> int:
        """
        Récupère la taille de fenêtre séquence spécialisée pour un cluster donné.

        Args:
            cluster_id: ID du cluster (0-7)

        Returns:
            Taille de fenêtre séquence optimisée pour ce cluster
        """
        cluster_windows = {
            0: self.cluster0_sequence_window,     # C0: Standard (3)
            1: self.cluster1_sequence_window,     # C1: Standard (3)
            2: self.cluster2_sequence_window,     # C2: Patterns courts (2)
            3: self.cluster3_sequence_window,     # C3: <PERSON><PERSON><PERSON> moyens (4)
            4: self.cluster4_sequence_window,     # C4: Pattern<PERSON> longs (7)
            5: self.cluster5_sequence_window,     # C5: Corrélations (3)
            6: self.cluster6_sequence_window,     # C6: Sync/Desync (3)
            7: self.cluster7_sequence_window,     # C7: Adaptatif (5)
        }
        return cluster_windows.get(cluster_id, self.rollout_analyzer_sequence_window)
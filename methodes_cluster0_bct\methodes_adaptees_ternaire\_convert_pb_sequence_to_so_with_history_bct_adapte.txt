MÉTHODE : _convert_pb_sequence_to_so_with_history
LIGNE DÉBUT : 4833
SIGNATURE : def _convert_pb_sequence_to_so_with_history(self, pb_sequence: List[str], last_historical_pb: str) -> List[str]:
================================================================================

    def _convert_pb_sequence_to_so_with_history(self, pb_sequence: List[str], last_historical_pb: str) -> List[str]:
"""
    ADAPTATION BCT - _convert_pb_sequence_to_so_with_history.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Convertit une séquence P/B en S/O en utilisant le dernier résultat historique

        Args:
            pb_sequence: Séquence P/B de longueur 3
            last_historical_pb: Dernier résultat P/B historique

        Returns:
            List[str]: Séquence S/O de longueur 3
        """
        so_sequence = []

        # Premier élément : comparer avec le dernier historique
        if pb_sequence[0] == last_historical_pb:
            so_sequence.append('S')
        else:
            so_sequence.append('O')

        # Éléments suivants : comparer avec l'élément précédent de la séquence
        for i in range(1, len(pb_sequence)):
            if pb_sequence[i] == pb_sequence[i-1]:
                so_sequence.append('S')
            else:
                so_sequence.append('O')

        return so_sequence


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2263 à 2287
# Type: Méthode de la classe UtilitairesMathematiquesAZR

    def calculate_consecutive_length(sequence: List[str], target: str) -> int:
        """
        Calcule la longueur maximale de séquences consécutives d'un élément

        Args:
            sequence: Séquence à analyser
            target: Élément cible à rechercher

        Returns:
            int: Longueur maximale de séquences consécutives
        """
        if not sequence:
            return 0

        max_length = 0
        current_length = 0

        for element in sequence:
            if element == target:
                current_length += 1
                max_length = max(max_length, current_length)
            else:
                current_length = 0

        return max_length
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15044 à 15076
# Type: Méthode de la classe AZRBaccaratPredictor

    def _get_detailed_training_metrics(self) -> Dict:
        """
        📊 MÉTRIQUES D'ENTRAÎNEMENT DÉTAILLÉES

        Returns:
            Métriques complètes par rollout et cluster
        """
        return {
            'rollout_performance': {
                'rollout_1_accuracy': getattr(self, 'rollout_1_accuracy', 0.0),
                'rollout_2_accuracy': getattr(self, 'rollout_2_accuracy', 0.0),
                'rollout_3_accuracy': getattr(self, 'rollout_3_accuracy', 0.0)
            },
            'cluster_performance': {
                f'cluster_{i}': {
                    'accuracy': getattr(self, f'cluster_{i}_accuracy', 0.0),
                    'predictions': getattr(self, f'cluster_{i}_predictions', 0),
                    'specialization': getattr(self, f'cluster_{i}_specialization', 'default')
                } for i in range(1, 9)  # 8 clusters
            },
            'learning_progression': {
                'initial_accuracy': self.accuracy_history[0] if self.accuracy_history else 0.0,
                'current_accuracy': self.current_accuracy,
                'peak_accuracy': max(self.accuracy_history) if self.accuracy_history else 0.0,
                'improvement_rate': self._calculate_accuracy_trend(),
                'stability_score': self._calculate_stability_score()
            },
            'pattern_discovery': {
                'patterns_discovered_count': len(self.discovered_patterns),
                'avg_pattern_success_rate': np.mean(list(self.pattern_success_rates.values())) if self.pattern_success_rates else 0.0,
                'best_pattern_success_rate': max(self.pattern_success_rates.values()) if self.pattern_success_rates else 0.0
            }
        }
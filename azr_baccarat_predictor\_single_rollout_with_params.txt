# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15732 à 15741
# Type: Méthode de la classe AZRBaccaratPredictor

    def _single_rollout_with_params(self, params: Tuple[int, float, float]) -> Optional[Dict[str, Any]]:
        """
        🔧 MÉTHODE LEGACY - Wrapper pour rollout avec paramètres (rétrocompatibilité)
        ⚠️  ATTENTION : Cette méthode utilise encore l'ancienne approche avec duplication !

        Args:
            params: <PERSON><PERSON> (index, temperature, random_factor)
        """
        index, temperature, random_factor = params
        return self._single_rollout(temperature, random_factor)
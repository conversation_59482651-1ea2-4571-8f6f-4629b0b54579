# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 1710 à 1730
# Type: Méthode de la classe AZRConfig

    def get_cluster_accuracy_window(self, cluster_id: int) -> int:
        """
        Récupère la taille de fenêtre précision spécialisée pour un cluster donné.

        Args:
            cluster_id: ID du cluster (0-7)

        Returns:
            Taille de fenêtre précision optimisée pour ce cluster
        """
        cluster_windows = {
            0: self.cluster0_accuracy_window,     # C0: Standard (10)
            1: self.cluster1_accuracy_window,     # C1: Standard (10)
            2: self.cluster2_accuracy_window,     # C2: Patterns courts (5)
            3: self.cluster3_accuracy_window,     # C3: <PERSON><PERSON><PERSON> moyens (8)
            4: self.cluster4_accuracy_window,     # C4: <PERSON>tern<PERSON> longs (15)
            5: self.cluster5_accuracy_window,     # C5: Corrélations (10)
            6: self.cluster6_accuracy_window,     # C6: Sync/Desync (10)
            7: self.cluster7_accuracy_window,     # C7: Adaptatif (7)
        }
        return cluster_windows.get(cluster_id, self.rollout_accuracy_recent_window)
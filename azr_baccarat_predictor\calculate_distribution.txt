# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2240 à 2260
# Type: Méthode de la classe UtilitairesMathematiquesAZR

    def calculate_distribution(sequence: List[str]) -> Dict[str, float]:
        """
        Calcule la distribution des éléments dans une séquence

        Args:
            sequence: Séquence d'éléments

        Returns:
            Dict: Distribution des éléments (élément -> fréquence)
        """
        if not sequence:
            return {}

        total = len(sequence)
        distribution = {}

        for element in set(sequence):
            count = sequence.count(element)
            distribution[element] = count / total

        return distribution
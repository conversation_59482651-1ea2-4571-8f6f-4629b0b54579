MÉTHODE : _calculate_evolution_strength
LIGNE DÉBUT : 7748
SIGNATURE : def _calculate_evolution_strength(self, trends: Dict) -> float:
================================================================================

    def _calculate_evolution_strength(self, trends: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_evolution_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la force globale d'évolution des corrélations"""

        if not trends:
            return 0.0

        total_strength = 0.0
        valid_trends = 0

        for trend_data in trends.values():
            if 'trend_strength' in trend_data:
                total_strength += trend_data['trend_strength']
                valid_trends += 1

        return total_strength / valid_trends if valid_trends > 0 else 0.0


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7297 à 7323
# Type: Méthode de la classe AZRCluster

    def _convert_pb_sequence_to_so_with_history(self, pb_sequence: List[str], last_historical_pb: str) -> List[str]:
        """
        Convertit une séquence P/B en S/O en utilisant le dernier résultat historique

        Args:
            pb_sequence: Séquence P/B de longueur 3
            last_historical_pb: Dernier résultat P/B historique

        Returns:
            List[str]: Séquence S/O de longueur 3
        """
        so_sequence = []

        # Premier élément : comparer avec le dernier historique
        if pb_sequence[0] == last_historical_pb:
            so_sequence.append('S')
        else:
            so_sequence.append('O')

        # Éléments suivants : comparer avec l'élément précédent de la séquence
        for i in range(1, len(pb_sequence)):
            if pb_sequence[i] == pb_sequence[i-1]:
                so_sequence.append('S')
            else:
                so_sequence.append('O')

        return so_sequence
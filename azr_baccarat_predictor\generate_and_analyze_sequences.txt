# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 19922 à 20142
# Type: Méthode

def generate_and_analyze_sequences(predictor: AZRBaccaratPredictor, num_games: int):
    """
    Génère les données et analyse les séquences avec optimisation multi-cœurs

    Args:
        predictor: Instance du prédicteur AZR
        num_games: Nombre de parties à générer
    """
    try:
        print(f"\n🚀 Génération parallèle optimisée pour {num_games} parties")
        print(f"💻 Utilisation de {mp.cpu_count()} cœurs disponibles")
        print(f"💾 RAM disponible: {psutil.virtual_memory().available / (1024**3):.1f} GB")

        start_time = time.time()

        # Optimisation pour 8 cœurs et 28GB RAM
        max_workers = min(8, mp.cpu_count())

        # Calculer la taille des lots pour optimiser la mémoire
        # Avec 28GB RAM, on peut traiter ~4000 parties par lot
        batch_size = min(4000, max(500, num_games // max_workers))

        print(f"🔧 Configuration: {max_workers} processus, {batch_size} parties par lot")

        # Créer les lots de travail
        batches = []
        games_assigned = 0

        while games_assigned < num_games:
            remaining_games = num_games - games_assigned
            current_batch_size = min(batch_size, remaining_games)

            batches.append((games_assigned + 1, current_batch_size, 60))
            games_assigned += current_batch_size

        print(f"📦 {len(batches)} lots créés pour traitement parallèle")

        # Traitement parallèle
        all_games = []
        total_hands = 0
        total_pb_hands = 0
        total_so_conversions = 0

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre tous les lots
            future_to_batch = {executor.submit(generate_games_batch, batch): batch for batch in batches}

            # Collecter les résultats au fur et à mesure
            completed_batches = 0
            for future in as_completed(future_to_batch):
                batch_info = future_to_batch[future]

                try:
                    batch_games = future.result()
                    all_games.extend(batch_games)

                    # Calculer les statistiques
                    for game in batch_games:
                        total_hands += game['statistics']['total_hands']
                        total_pb_hands += game['statistics']['pb_hands']
                        total_so_conversions += game['statistics']['so_conversions']

                    completed_batches += 1
                    progress = (completed_batches / len(batches)) * 100
                    ram_usage = psutil.virtual_memory().percent

                    print(f"✅ Lot {completed_batches}/{len(batches)} terminé ({progress:.1f}%) - "
                          f"RAM: {ram_usage:.1f}% - Parties: {len(all_games)}")

                    # Libérer la mémoire
                    gc.collect()

                except Exception as e:
                    print(f"❌ Erreur dans le lot {batch_info}: {e}")

        generation_time = time.time() - start_time

        # Trier les parties par numéro
        all_games.sort(key=lambda x: x['game_number'])

        print(f"\n⏱️ Génération terminée en {generation_time:.1f} secondes")
        print(f"📊 {len(all_games)} parties générées avec {total_pb_hands} manches P/B")

        # Sauvegarder les données
        filename = f"azr_generated_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        training_data = {
            'metadata': {
                'total_games': len(all_games),
                'total_hands': total_hands,
                'total_pb_hands': total_pb_hands,
                'total_so_conversions': total_so_conversions,
                'hands_per_game': 60,
                'generation_time': generation_time,
                'parallel_workers': max_workers,
                'batch_size': batch_size,
                'timestamp': datetime.now().isoformat()
            },
            'games': all_games
        }

        print(f"\n💾 Sauvegarde des données...")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)

        print(f"✅ Données sauvegardées: {filename}")

        # Analyser les séquences sur un échantillon
        print(f"\n🔍 Analyse des séquences maximales...")
        analysis_start = time.time()

        # Analyser toutes les parties pour les séquences maximales
        print(f"📊 Analyse complète sur {len(all_games)} parties...")

        # Réinitialiser le prédicteur pour l'analyse
        predictor.reset_session()

        # Traiter les parties une par une pour l'analyse des intervalles
        games_processed = 0
        for game in all_games:
            # Réinitialiser pour chaque partie
            predictor.reset_session()

            # Initialiser le brûlage
            burn_parity = game['initialization']['burn_parity']
            predictor.set_burn_parity(burn_parity)

            # Traiter toutes les manches de la partie
            for hand_data in game['hands']:
                predictor.receive_hand_data(hand_data)

            games_processed += 1
            if games_processed % 10000 == 0:
                print(f"   Parties analysées: {games_processed}/{len(all_games)} ({games_processed/len(all_games)*100:.1f}%)")
                gc.collect()  # Libérer la mémoire

        # Analyser les séquences maximales
        analysis = predictor.analyze_maximum_sequences()

        analysis_time = time.time() - analysis_start
        print(f"\n⏱️ Analyse terminée en {analysis_time:.1f} secondes")

        # Afficher le rapport complet
        print(f"\n" + "="*80)
        print(f"🎯 RAPPORT D'ANALYSE DES SÉQUENCES ({len(all_games)} parties)")
        print(f"="*80)

        predictor.print_maximum_sequences_report(analysis)

        # Sauvegarder le rapport en format texte pour lecture détaillée
        report_filename = f"rapport_sequences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # Capturer le rapport dans un fichier texte
        import io
        import sys

        # Rediriger la sortie vers un buffer
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()

        # Générer le rapport complet dans le buffer
        print(f"RAPPORT D'ANALYSE DES SÉQUENCES SYNC/DESYNC")
        print(f"="*80)
        print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Parties analysées: {len(all_games):,}")
        print(f"Temps de génération: {generation_time:.1f} secondes")
        print(f"Temps d'analyse: {analysis_time:.1f} secondes")
        print(f"Processus parallèles: {max_workers}")
        print(f"="*80)

        # Afficher le rapport détaillé
        predictor.print_maximum_sequences_report(analysis)

        # Ajouter des statistiques supplémentaires
        print(f"\n" + "="*80)
        print(f"STATISTIQUES TECHNIQUES")
        print(f"="*80)
        print(f"Total manches générées: {total_hands:,}")
        print(f"Total manches P/B: {total_pb_hands:,}")
        print(f"Total conversions S/O: {total_so_conversions:,}")
        print(f"Ratio P/B: {total_pb_hands/total_hands:.3f}")
        print(f"Ratio conversions: {total_so_conversions/total_pb_hands:.3f}")

        # Récupérer le contenu du buffer
        report_content = buffer.getvalue()

        # Restaurer stdout
        sys.stdout = old_stdout

        # Sauvegarder le rapport texte
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"\n📄 Rapport texte sauvegardé: {report_filename}")

        # Sauvegarder les résultats d'analyse JSON
        analysis_filename = f"sequence_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_games_generated': len(all_games),
                    'games_analyzed': len(all_games),
                    'generation_time': generation_time,
                    'analysis_time': analysis_time,
                    'total_time': time.time() - start_time,
                    'parallel_workers': max_workers,
                    'timestamp': datetime.now().isoformat()
                },
                'analysis_results': analysis
            }, f, indent=2, ensure_ascii=False, default=str)

        print(f"💾 Données JSON sauvegardées: {analysis_filename}")

        total_time = time.time() - start_time
        print(f"\n🎉 PROCESSUS COMPLET TERMINÉ en {total_time:.1f} secondes")
        print(f"📈 Prêt pour l'exploitation des biais détectés !")

    except Exception as e:
        print(f"\n❌ Erreur lors de la génération/analyse: {e}")
        import traceback
        traceback.print_exc()
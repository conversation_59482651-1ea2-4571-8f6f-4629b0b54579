# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10195 à 10210
# Type: Méthode de la classe AZRCluster

    def _generate_temporal_recommendation(self, best_phase: str, stability: float, trends: Dict) -> str:
        """Génère une recommandation basée sur l'analyse temporelle"""

        if stability > self.config.stability_threshold_high:
            return f"Corrélations très stables - Confiance élevée en {best_phase}"
        elif stability > self.config.stability_threshold_low:
            return f"Corrélations modérément stables - Privilégier {best_phase}"
        else:
            # Analyser tendances pour recommandation
            strengthening_trends = sum(1 for trend in trends.values()
                                     if trend.get('trend_type') == 'STRENGTHENING')

            if strengthening_trends >= 2:
                return "Corrélations en renforcement - Confiance croissante en fin de partie"
            else:
                return "Corrélations instables - Utiliser avec prudence"
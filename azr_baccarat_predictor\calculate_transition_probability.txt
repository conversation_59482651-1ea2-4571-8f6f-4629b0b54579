# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2290 à 2316
# Type: Méthode de la classe UtilitairesMathematiquesAZR

    def calculate_transition_probability(sequence: List[str], from_state: str, to_state: str) -> float:
        """
        Calcule la probabilité de transition entre deux états

        Args:
            sequence: Séquence d'états
            from_state: État de départ
            to_state: État d'arrivée

        Returns:
            float: Probabilité de transition (0 à 1)
        """
        if len(sequence) < 2:
            config = AZRConfig()
            return config.default_probability_value

        transitions_from = 0
        transitions_to = 0

        for i in range(len(sequence) - 1):
            if sequence[i] == from_state:
                transitions_from += 1
                if sequence[i + 1] == to_state:
                    transitions_to += 1

        config = AZRConfig()
        return transitions_to / max(1, transitions_from) if transitions_from > 0 else config.default_probability_value
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15268 à 15310
# Type: Méthode de la classe AZRBaccaratPredictor

    def _predict_with_azr_master(self) -> str:
        """
        Prédiction utilisant le système AZR Master avec 8 clusters parallèles

        Chaque cluster exécute ses 3 rollouts (Analyseur → Générateur → Prédicteur)
        puis un consensus intelligent est construit à partir des 8 résultats.

        Returns:
            'S' ou 'O' basé sur le consensus des 8 clusters
        """
        try:
            # Préparation de la séquence standardisée pour les clusters
            standardized_sequence = self._prepare_sequence_for_clusters()

            # Exécution du système AZR Master (8 clusters en parallèle)
            master_result = self.azr_master.predict_next_sequence(standardized_sequence)

            # Extraction de la prédiction consensus
            consensus = master_result.get('consensus_prediction', {})
            final_prediction = consensus.get('consensus_so', 'S')
            consensus_confidence = consensus.get('consensus_confidence', self.config.probability_neutral)
            agreement_level = consensus.get('agreement_level', self.config.zero_value)

            # Logging détaillé du processus
            timing_metrics = master_result.get('timing_metrics', {})
            clusters_completed = timing_metrics.get('clusters_completed', self.config.zero_value)
            total_time = timing_metrics.get('total_system_time_ms', self.config.zero_value)

            logger.info(f"🎯 AZR Master: {clusters_completed}/{self.config.cluster_count} clusters → Consensus: {final_prediction} "
                       f"(confiance: {consensus_confidence:.1%}, accord: {agreement_level:.1%}, "
                       f"temps: {total_time:.1f}ms)")

            # Bonus si unanimité des clusters
            voting_details = consensus.get('voting_details', {})
            if voting_details.get('unanimous_agreement', False):
                logger.info(f"🚀 UNANIMITÉ des {self.config.cluster_count} clusters AZR → Confiance boostée !")

            return final_prediction

        except Exception as e:
            logger.error(f"❌ Erreur AZR Master: {e}")
            # Fallback sur prédiction par défaut
            return self._default_prediction()
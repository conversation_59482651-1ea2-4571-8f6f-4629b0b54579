MÉTHODE : _analyze_sequence_consistency
LIGNE DÉBUT : 3553
SIGNATURE : def _analyze_sequence_consistency(self, sequence: Dict) -> float:
================================================================================

    def _analyze_sequence_consistency(self, sequence: Dict) -> float:
"""
    ADAPTATION BCT - _analyze_sequence_consistency.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Analyse la cohérence interne d'une séquence

        FOCUS : Vérifier que la séquence a une logique interne cohérente
        """
        consistency_score = 0.5  # Score de base

        sequence_data = sequence.get('sequence_data', [])
        if not sequence_data:
            return 0.0

        # 1. Cohérence de longueur (séquences de 3-5 éléments sont optimales)
        length_score = 1.0
        if len(sequence_data) < 2:
            length_score = 0.3
        elif len(sequence_data) > 6:
            length_score = 0.7

        # 2. Cohérence de pattern (éviter les patterns trop chaotiques)
        pattern_score = 1.0
        if len(sequence_data) >= 3:
            # Vérifier s'il y a un pattern reconnaissable
            alternations = 0
            repetitions = 0

            for i in range(1, len(sequence_data)):
                if sequence_data[i] != sequence_data[i-1]:
                    alternations += 1
                else:
                    repetitions += 1

            total_transitions = len(sequence_data) - 1
            if total_transitions > 0:
                alternation_ratio = alternations / total_transitions
                # Pattern optimal : ni trop chaotique (>80% alternance) ni trop répétitif (<20% alternance)
                if 0.2 <= alternation_ratio <= 0.8:
                    pattern_score = 1.0
                elif alternation_ratio > self.config.correlation_very_high_sync_threshold:
                    pattern_score = self.config.priority_threshold_significant  # Trop chaotique
                else:
                    pattern_score = 0.7  # Trop répétitif

        # 3. Cohérence avec la probabilité estimée
        estimated_probability = sequence.get('estimated_probability', 0.5)
        probability_score = 1.0

        if estimated_probability > self.config.confidence_very_high_threshold:
            # Haute probabilité : séquence doit être simple et logique
            if pattern_score < self.config.rollout2_confidence_value_high:
                probability_score = 0.6
        elif estimated_probability < self.config.rollout3_quality_bonus_medium:
            # Faible probabilité : acceptable d'avoir des patterns plus complexes
            probability_score = 0.8

        # Score de cohérence final
        consistency_score = (length_score * self.config.rollout3_quality_bonus_small + pattern_score * self.config.rollout3_neutral_evaluation_value + probability_score * self.config.rollout2_consistency_weight)

        return min(1.0, consistency_score)


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14601 à 14609
# Type: Méthode de la classe AZRBaccaratInterface

    def _enable_all_buttons(self):
        """Réactive tous les boutons de l'interface"""
        # Réactiver les boutons de brûlage
        self.burn_pair_btn.config(state=tk.NORMAL)
        self.burn_impair_btn.config(state=tk.NORMAL)

        # Réactiver récursivement tous les boutons
        for widget in self.root.winfo_children():
            self._enable_buttons_in_widget(widget)
MÉTHODE : get_max_sequence_length
LIGNE DÉBUT : 10328
SIGNATURE : def get_max_sequence_length(self, mode: str = "real") -> int:
================================================================================

    def get_max_sequence_length(self, mode: str = "real") -> int:
"""
    ADAPTATION BCT - get_max_sequence_length.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Retourne la limite de séquence selon le mode

        Args:
            mode: "real" pour mode réel (60 manches P/B), "training" pour entraînement (cut card)

        Returns:
            Limite maximale de manches pour la partie
        """
        if mode == "training":
            return self.config.max_sequence_length_training  # Cut card position
        else:  # mode == "real"
            return self.config.max_sequence_length_real      # 60 manches P/B max


MÉTHODE : _extract_combined_state_changes_strength
LIGNE DÉBUT : 8072
SIGNATURE : def _extract_combined_state_changes_strength(self, combined_impacts: Dict) -> float:
================================================================================

    def _extract_combined_state_changes_strength(self, combined_impacts: Dict) -> float:
"""
    ADAPTATION BCT - _extract_combined_state_changes_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Extrait la force des impacts de changements d'états combinés (focus P/B et S/O)"""

        if not combined_impacts:
            return 0.0

        # Chercher métriques de force dans change_strength_metrics
        if 'change_strength_metrics' in combined_impacts:
            metrics = combined_impacts['change_strength_metrics']
            return metrics.get('average_transition_strength', 0.0)

        # Sinon analyser les types de transitions
        strength_metrics = []

        if 'transition_types_analysis' in combined_impacts:
            for transition_type, transition_data in combined_impacts['transition_types_analysis'].items():
                if 'type_strength' in transition_data:
                    strength_metrics.append(transition_data['type_strength'])

        return sum(strength_metrics) / len(strength_metrics) if strength_metrics else 0.0


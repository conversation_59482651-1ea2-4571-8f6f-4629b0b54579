# MÉTHODE ADAPTÉE POUR BCT.PY
# Source: _rollout_predictor.txt (AZR)
# Adaptation: Système de comptage BCT avec INDEX détaillés

def _rollout_predictor_bct(self, generator_result: Dict, analyzer_report: Dict) -> Dict:
    """
    Rollout 3 Prédicteur BCT - Sélection séquence optimale finale
    
    ADAPTATION BCT - PRÉDICTION AVEC INDEX DÉTAILLÉS :
    - ÉVALUE les séquences candidates enrichies avec INDEX BCT
    - SÉLECTIONNE la meilleure séquence basée sur critères S/O prioritaires
    - UTILISE l'intelligence de sélection adaptée aux catégories BCT
    - CONVERTIT P/B → S/O (logique identique, indépendante du système de comptage)
    - CALCULE la confiance basée sur l'exploitation des biais BCT
    
    Critères d'évaluation BCT :
    1. CRITÈRE 1 : Qualité S/O de la séquence (priorité absolue)
    2. CRITÈRE 2 : Coh<PERSON>rence avec biais BCT détectés (impair_5, pair_4/6)
    3. CRITÈRE 3 : Exploitation des états combinés rares BCT
    4. CRITÈRE 4 : Stabilité des patterns de cartes distribuées
    
    Args:
        generator_result: Résultat structuré du générateur BCT (dictionnaire)
        analyzer_report: Rapport original de l'analyseur BCT
        
    Returns:
        Dict: Prédiction finale BCT avec confiance et justification
    """
    try:
        # Extraire les séquences du résultat structuré BCT
        generated_sequences_bct = generator_result.get('sequences', [])
        generation_metadata_bct = generator_result.get('generation_metadata', {})
        generation_stats_bct = generator_result.get('generation_stats', {})
        
        if not generated_sequences_bct:
            return {
                'prediction': None,
                'confidence': self.config.rollout3_default_confidence,
                'error': 'Aucune séquence candidate BCT disponible',
                'generator_metadata': generation_metadata_bct,
                'bct_adaptation': True
            }
        
        # Évaluation détaillée de chaque séquence candidate BCT
        evaluated_sequences_bct = []
        
        for i, sequence_bct in enumerate(generated_sequences_bct):
            # Créer un wrapper dictionnaire pour les séquences BCT enrichies
            if isinstance(sequence_bct, dict) and 'bct_indexes' in sequence_bct:
                # Séquence déjà enrichie avec INDEX BCT
                sequence_wrapper_bct = {
                    'sequence_data': sequence_bct.get('sequence_data', []),
                    'bct_indexes': sequence_bct.get('bct_indexes', {}),
                    'enrichment_metadata': sequence_bct.get('enrichment_metadata', {}),
                    'strategy': f'bct_strategy_{i}',
                    'justification': f'Séquence enrichie BCT par cluster {self.config.zero_value}',
                    'estimated_probability': sequence_bct.get('enrichment_metadata', {}).get(
                        'sequence_quality_score', self.config.rollout3_fallback_probability
                    )
                }
            elif isinstance(sequence_bct, list):
                # Séquence simple à enrichir avec logique BCT
                actual_sequence_data = []
                
                for item in sequence_bct:
                    if isinstance(item, dict) and 'predicted_pbt' in item:
                        # Extraire seulement la chaîne P/B
                        pbt_value = item.get('predicted_pbt', 'B')
                        if isinstance(pbt_value, str) and pbt_value in ['P', 'B']:
                            actual_sequence_data.append(pbt_value)
                        else:
                            actual_sequence_data.append('B')  # Fallback
                    elif isinstance(item, str) and item in ['P', 'B']:
                        actual_sequence_data.append(item)
                    else:
                        actual_sequence_data.append('B')  # Fallback
                
                sequence_wrapper_bct = {
                    'sequence_data': actual_sequence_data,
                    'bct_indexes': {
                        'cards_category_sequence': [],  # À calculer
                        'sync_state_sequence': [],      # À calculer
                        'combined_state_sequence': [],  # À calculer
                        'result_sequence': actual_sequence_data,
                        'so_conversion_sequence': []    # À calculer
                    },
                    'strategy': f'bct_fallback_strategy_{i}',
                    'justification': f'Séquence BCT générée par cluster {self.config.zero_value}',
                    'estimated_probability': self.config.rollout3_fallback_probability
                }
            else:
                sequence_wrapper_bct = sequence_bct
            
            # Évaluation avec critères BCT spécifiques
            evaluation_bct = self._evaluate_sequence_quality_bct(sequence_wrapper_bct, analyzer_report)
            sequence_wrapper_bct['evaluation'] = evaluation_bct
            
            evaluated_sequences_bct.append(sequence_wrapper_bct)
        
        # Sélection de la meilleure séquence BCT
        best_sequence_bct = self._select_best_sequence_bct(evaluated_sequences_bct)
        
        # Calcul confiance finale du cluster BCT (méthode calibrée)
        cluster_confidence_bct = self._calculate_cluster_confidence_bct_calibrated(
            best_sequence_bct, analyzer_report
        )
        self.shared_memory['cluster_confidence'] = cluster_confidence_bct
        
        # Conversion de la séquence P/B en séquence S/O (logique identique)
        pb_sequence = best_sequence_bct.get('sequence_data', [])
        so_sequence = self._convert_pb_sequence_to_so_bct(pb_sequence, analyzer_report)
        
        # Prédiction finale BCT : séquence S/O simple
        final_prediction_bct = {
            'sequence': so_sequence,  # Séquence S/O simple ['S', 'O', 'S']
            'strategy': best_sequence_bct.get('strategy', 'unknown_bct'),
            'justification': best_sequence_bct.get('justification', 'Prédiction générée BCT'),
            'estimated_probability': best_sequence_bct.get(
                'estimated_probability', self.config.rollout3_fallback_probability
            ),
            'evaluation_score': best_sequence_bct.get('evaluation', {}).get(
                'total_score', self.config.probability_neutral
            ),
            'cluster_confidence': cluster_confidence_bct,
            'next_hand_prediction': so_sequence[self.config.zero_value] if so_sequence else 'S',
            
            # NOUVEAU BCT : Métadonnées du pipeline complet avec spécificités BCT
            'pipeline_metadata': {
                'generator_metadata': generation_metadata_bct,
                'generation_stats': generation_stats_bct,
                'sequences_evaluated': len(evaluated_sequences_bct),
                'best_sequence_index': (
                    evaluated_sequences_bct.index(best_sequence_bct) 
                    if best_sequence_bct in evaluated_sequences_bct else -self.config.one_value
                ),
                'original_pb_sequence': pb_sequence,
                'bct_adaptation': True,
                'bct_indexes_used': best_sequence_bct.get('bct_indexes', {}),
                'bct_bias_exploitation': best_sequence_bct.get('enrichment_metadata', {}).get(
                    'bias_exploitation_score', self.config.zero_value
                )
            }
        }
        
        return final_prediction_bct
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Erreur rollout predictor BCT cluster {self.config.zero_value}: {type(e).__name__}: {e}")
        logger.error(f"Détails erreur rollout predictor BCT: {error_details}")
        return {
            'prediction': None,
            'confidence': self.config.zero_value,
            'error': str(e),
            'bct_adaptation': True
        }

# ================================================================
# MÉTHODES DE SUPPORT BCT À IMPLÉMENTER
# ================================================================

def _evaluate_sequence_quality_bct(self, sequence_wrapper: Dict, analyzer_report: Dict) -> Dict:
    """Évalue la qualité d'une séquence avec critères BCT spécifiques"""
    evaluation = {
        'so_quality_score': self.config.zero_value,
        'bct_coherence_score': self.config.zero_value,
        'bias_exploitation_score': self.config.zero_value,
        'total_score': self.config.zero_value
    }
    
    # Critère 1 : Qualité S/O (priorité absolue)
    so_sequence = sequence_wrapper.get('bct_indexes', {}).get('so_conversion_sequence', [])
    if so_sequence:
        s_count = so_sequence.count('S')
        o_count = so_sequence.count('O')
        total_so = s_count + o_count
        if total_so > self.config.zero_value:
            # Éviter les séquences trop déséquilibrées
            balance_score = self.config.one_value - abs(s_count - o_count) / total_so
            evaluation['so_quality_score'] = balance_score * self.config.rollout3_so_quality_weight
    
    # Critère 2 : Cohérence avec biais BCT détectés
    bct_indexes = sequence_wrapper.get('bct_indexes', {})
    cards_category_sequence = bct_indexes.get('cards_category_sequence', [])
    
    # Bonus si exploitation des biais impair_5
    impair_5_count = cards_category_sequence.count('impair_5')
    if impair_5_count > self.config.zero_value:
        evaluation['bct_coherence_score'] += impair_5_count * self.config.rollout3_impair5_bonus
    
    # Bonus si alternance pair_4 ↔ pair_6
    pair_alternations = self._count_pair46_alternations_bct(cards_category_sequence)
    evaluation['bct_coherence_score'] += pair_alternations * self.config.rollout3_pair_alternation_bonus
    
    # Critère 3 : Exploitation des biais détectés
    bias_exploitation = sequence_wrapper.get('enrichment_metadata', {}).get('bias_exploitation_score', self.config.zero_value)
    evaluation['bias_exploitation_score'] = bias_exploitation * self.config.rollout3_bias_exploitation_weight
    
    # Score total pondéré BCT
    evaluation['total_score'] = (
        evaluation['so_quality_score'] * self.config.rollout3_so_priority_weight +
        evaluation['bct_coherence_score'] * self.config.rollout3_bct_coherence_weight +
        evaluation['bias_exploitation_score'] * self.config.rollout3_bias_weight
    )
    
    return evaluation

def _select_best_sequence_bct(self, evaluated_sequences: List[Dict]) -> Dict:
    """Sélectionne la meilleure séquence selon critères BCT"""
    if not evaluated_sequences:
        return {}
    
    # Tri par score total décroissant
    sorted_sequences = sorted(
        evaluated_sequences,
        key=lambda seq: seq.get('evaluation', {}).get('total_score', self.config.zero_value),
        reverse=True
    )
    
    return sorted_sequences[self.config.zero_value]  # Première = meilleure

def _calculate_cluster_confidence_bct_calibrated(self, best_sequence: Dict, analyzer_report: Dict) -> float:
    """Calcule la confiance du cluster calibrée pour BCT"""
    base_confidence = self.config.rollout3_base_confidence
    
    # Bonus basé sur l'évaluation de la séquence
    evaluation_score = best_sequence.get('evaluation', {}).get('total_score', self.config.zero_value)
    evaluation_bonus = evaluation_score * self.config.rollout3_evaluation_bonus_factor
    
    # Bonus basé sur l'exploitation des biais BCT
    bias_exploitation = best_sequence.get('enrichment_metadata', {}).get('bias_exploitation_score', self.config.zero_value)
    bias_bonus = bias_exploitation * self.config.rollout3_bias_confidence_factor
    
    # Confiance finale calibrée
    final_confidence = min(
        base_confidence + evaluation_bonus + bias_bonus,
        self.config.rollout3_max_confidence
    )
    
    return final_confidence

def _convert_pb_sequence_to_so_bct(self, pb_sequence: List[str], analyzer_report: Dict) -> List[str]:
    """Convertit une séquence P/B en séquence S/O (logique identique à AZR)"""
    # Cette méthode reste identique car la conversion P/B → S/O
    # est indépendante du système de comptage (BCT vs AZR)
    so_sequence = []
    
    for i in range(len(pb_sequence) - self.config.one_value):
        current_result = pb_sequence[i]
        next_result = pb_sequence[i + self.config.one_value]
        
        if current_result == next_result:
            so_sequence.append('S')  # Same
        else:
            so_sequence.append('O')  # Opposite
    
    return so_sequence

def _count_pair46_alternations_bct(self, cards_sequence: List[str]) -> int:
    """Compte les alternances pair_4 ↔ pair_6 dans une séquence"""
    alternations = self.config.zero_value
    
    for i in range(len(cards_sequence) - self.config.one_value):
        current = cards_sequence[i]
        next_card = cards_sequence[i + self.config.one_value]
        
        if (current == 'pair_4' and next_card == 'pair_6') or (current == 'pair_6' and next_card == 'pair_4'):
            alternations += self.config.one_value
    
    return alternations

# ================================================================
# CONFIGURATION BCT CENTRALISÉE
# ================================================================
# Tous les paramètres doivent être dans AZRConfig :
# - Poids d'évaluation (rollout3_so_quality_weight, rollout3_bct_coherence_weight)
# - Bonus spécifiques (rollout3_impair5_bonus, rollout3_pair_alternation_bonus)
# - Facteurs de confiance (rollout3_bias_confidence_factor, rollout3_max_confidence)
# - Seuils et priorités (rollout3_so_priority_weight, rollout3_bias_weight)
# ================================================================

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 1991 à 2051
# Type: Méthode de la classe AZRConfig

    def __post_init__(self):
        """Initialise les valeurs par défaut et règles découvertes"""

        # Initialiser les listes de cartes
        if self.card_suits is None:
            self.card_suits = ['♠', '♥', '♦', '♣']

        if self.card_values is None:
            self.card_values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

        if self.face_cards is None:
            self.face_cards = ['J', 'Q', 'K', '10']

        # Initialiser les spécialisations de clusters
        if self.cluster_pattern_specializations is None:
            self.cluster_pattern_specializations = {
                2: {  # PATTERNS_COURTS
                    'name': 'PATTERNS_COURTS',
                    'focus_lengths': [2, 3],
                    'weights': {2: self.pattern_short_weight, 3: self.pattern_short_weight, 'others': self.pattern_others_short_weight}
                },
                3: {  # PATTERNS_MOYENS
                    'name': 'PATTERNS_MOYENS',
                    'focus_lengths': [4, 5, 6, 7],
                    'weights': {4: self.pattern_medium_weight, 5: self.pattern_medium_weight, 6: self.pattern_medium_weight, 7: self.pattern_medium_weight, 'others': self.pattern_others_medium_weight}
                },
                4: {  # PATTERNS_LONGS
                    'name': 'PATTERNS_LONGS',
                    'focus_lengths': [8, 9, 10, 11, 12],
                    'weights': {8: self.pattern_long_weight, 9: self.pattern_long_weight, 10: self.pattern_long_weight, 11: self.pattern_long_weight, 12: self.pattern_long_weight, 'others': self.pattern_others_long_weight}
                }
            }

        # Initialiser les règles de prédiction découvertes
        if self.combined_prediction_rules is None:
            self.combined_prediction_rules = {
                'IMPAIR_SYNC': {
                    'S_rate': self.impair_sync_s_rate,  # 51.1% - OPTIMAL pour S
                    'O_rate': self.impair_sync_o_rate,  # 48.9%
                    'preferred_prediction': 'S',
                    'confidence_bonus': self.impair_sync_confidence_bonus  # Bonus de confiance
                },
                'PAIR_SYNC': {
                    'S_rate': self.pair_sync_s_rate,  # 38.8% - PIRE pour S
                    'O_rate': self.pair_sync_o_rate,  # 61.2% - OPTIMAL pour O
                    'preferred_prediction': 'O',
                    'confidence_bonus': self.pair_sync_confidence_bonus  # Plus fort bonus (meilleur signal)
                },
                'PAIR_DESYNC': {
                    'S_rate': self.pair_desync_s_rate,  # 46.8%
                    'O_rate': self.pair_desync_o_rate,  # 53.2%
                    'preferred_prediction': 'O',
                    'confidence_bonus': self.pair_desync_confidence_bonus  # Bonus modéré
                },
                'IMPAIR_DESYNC': {
                    'S_rate': self.impair_desync_s_rate,  # 49.6%
                    'O_rate': self.impair_desync_o_rate,  # 50.4%
                    'preferred_prediction': 'O',
                    'confidence_bonus': self.impair_desync_confidence_bonus  # Bonus faible (quasi-aléatoire)
                }
            }
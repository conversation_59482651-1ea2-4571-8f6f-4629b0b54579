MÉTHODE : _analyze_combined_to_so_impact
LIGNE DÉBUT : 5750
SIGNATURE : def _analyze_combined_to_so_impact(self, combined_seq: List[str], so_seq: List[str]) -> Dict:
================================================================================

    def _analyze_combined_to_so_impact(self, combined_seq: List[str], so_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _analyze_combined_to_so_impact.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Analyse impact COMBINÉ → S/O (amélioré)"""
        if len(so_seq) == 0:
            return {'no_data': True}

        # Alignement des séquences
        aligned_combined = combined_seq[1:len(so_seq)+1] if len(combined_seq) > len(so_seq) else combined_seq[:len(so_seq)]

        # États combinés possibles
        combined_states = ['IMpair_4_sync, pair_6_sync', 'IMpair_4_desync, pair_6_desync', 'pair_4_sync, pair_6_sync', 'pair_4_desync, pair_6_desync']

        impact_analysis = {}

        for state in combined_states:
            # Compter les occurrences de chaque état → S/O
            state_s_count = sum(1 for c, so in zip(aligned_combined, so_seq) if c == state and so == 'S')
            state_o_count = sum(1 for c, so in zip(aligned_combined, so_seq) if c == state and so == 'O')

            total_state = state_s_count + state_o_count

            if total_state > 0:
                impact_analysis[state] = {
                    'to_s_ratio': state_s_count / total_state,
                    'to_o_ratio': state_o_count / total_state,
                    'dominant_so': 'S' if state_s_count > state_o_count else 'O',
                    'total_occurrences': total_state
                }

        return {
            'state_impacts': impact_analysis,
            'strongest_pattern': max(impact_analysis.items(),
                                   key=lambda x: abs(x[1]['to_s_ratio'] - 0.5),
                                   default=('none', {}))[0],
            'overall_impact_strength': self._calculate_combined_so_impact_strength(impact_analysis)
        }


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6560 à 6608
# Type: Méthode de la classe AZRCluster

    def _convert_pb_sequence_to_so(self, pb_sequence: List[str], analyzer_report: Dict) -> List[str]:
        """
        Convertit une séquence P/B en séquence S/O (Same/Opposite)

        Args:
            pb_sequence: Séquence P/B ['P', 'B', 'P', 'B']
            analyzer_report: Rapport de l'analyseur pour contexte

        Returns:
            Séquence S/O de longueur 3 (4 P/B → 3 S/O selon spécifications AZR)
        """
        if not pb_sequence or len(pb_sequence) < self.config.rollout2_sequences_count:
            # Fallback : générer une séquence S/O de longueur 3 par défaut
            return ['S', 'O', 'S']

        so_sequence = []

        # Obtenir le dernier résultat P/B de l'historique pour référence
        last_historical_pb = self._get_last_historical_pb_result(analyzer_report)

        # Premier élément : comparer avec le dernier résultat historique
        if last_historical_pb:
            if pb_sequence[0] == last_historical_pb:
                so_sequence.append('S')  # Same
            else:
                so_sequence.append('O')  # Opposite
        else:
            # Pas d'historique, utiliser fallback
            so_sequence.append('S')

        # Éléments suivants : comparer avec l'élément précédent de la séquence
        for i in range(self.config.one_value, len(pb_sequence)):
            if pb_sequence[i] == pb_sequence[i-self.config.one_value]:
                so_sequence.append('S')  # Same
            else:
                so_sequence.append('O')  # Opposite

        # Validation : s'assurer que la séquence S/O a exactement 3 éléments
        if len(so_sequence) != self.config.rollout3_fixed_length:
            # Ajuster à la longueur requise
            if len(so_sequence) < self.config.rollout3_fixed_length:
                # Compléter avec des éléments par défaut
                while len(so_sequence) < self.config.rollout3_fixed_length:
                    so_sequence.append('S')
            else:
                # Tronquer à la longueur requise
                so_sequence = so_sequence[:self.config.rollout3_fixed_length]

        return so_sequence
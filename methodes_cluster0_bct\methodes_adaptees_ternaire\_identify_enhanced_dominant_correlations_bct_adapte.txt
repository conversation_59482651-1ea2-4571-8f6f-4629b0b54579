MÉTHODE : _identify_enhanced_dominant_correlations
LIGNE DÉBUT : 8268
SIGNATURE : def _identify_enhanced_dominant_correlations(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict]:
================================================================================

    def _identify_enhanced_dominant_correlations(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict]:
"""
    ADAPTATION BCT - _identify_enhanced_dominant_correlations.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Identifie les corrélations dominantes ENRICHIES en utilisant l'analyse complète des impacts croisés

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Version avancée qui utilise tous les impacts croisés pour identifier les patterns les plus forts

        Args:
            all_indices: Dictionnaire complet avec tous les indices analysés
            cross_index_impacts: Analyse complète des impacts croisés entre indices

        Returns:
            Liste des corrélations dominantes enrichies avec métriques avancées
        """

        enhanced_correlations = []

        # ================================================================
        # 1. CORRÉLATIONS IMPAIR/PAIR ENRICHIES
        # ================================================================

        # Corrélations IMPAIR/PAIR → P/B (sans TIE)
        if 'impair_pair_to_pbt' in cross_index_impacts:
            impair_pair_pbt = cross_index_impacts['impair_pair_to_pbt']

            # Analyser corrélations P/B (exclure TIE)
            impair_to_p = impair_pair_pbt.get('impair_to_player', 0)
            impair_to_b = impair_pair_pbt.get('impair_to_banker', 0)
            pair_to_p = impair_pair_pbt.get('pair_to_player', 0)
            pair_to_b = impair_pair_pbt.get('pair_to_banker', 0)

            # Normaliser P/B (exclure TIE)
            impair_pb_total = impair_to_p + impair_to_b
            pair_pb_total = pair_to_p + pair_to_b

            if impair_pb_total > 0:
                impair_p_ratio = impair_to_p / impair_pb_total
                impair_strength = abs(impair_p_ratio - 0.5)

                if impair_strength > self.config.rollout2_signal_strength_threshold:  # Seuil enrichi
                    enhanced_correlations.append({
                        'type': 'impair_to_pb_enhanced',
                        'strength': impair_strength,
                        'pattern': 'IMPAIR→P' if impair_p_ratio > 0.5 else 'IMPAIR→B',
                        'confidence': impair_strength * 2,  # Normalisation 0-1
                        'sample_size': impair_pb_total,
                        'category': 'impair_pair_correlations'
                    })

            if pair_pb_total > 0:
                pair_p_ratio = pair_to_p / pair_pb_total
                pair_strength = abs(pair_p_ratio - 0.5)

                if pair_strength > self.config.rollout2_signal_strength_threshold:
                    enhanced_correlations.append({
                        'type': 'pair_to_pb_enhanced',
                        'strength': pair_strength,
                        'pattern': 'PAIR→P' if pair_p_ratio > 0.5 else 'PAIR→B',
                        'confidence': pair_strength * 2,
                        'sample_size': pair_pb_total,
                        'category': 'impair_pair_correlations'
                    })

        # Corrélations IMPAIR/PAIR → S/O
        if 'impair_pair_to_so' in cross_index_impacts:
            impair_pair_so = cross_index_impacts['impair_pair_to_so']

            impair_s_ratio = impair_pair_so.get('impair_to_s_ratio', 0)
            pair_s_ratio = impair_pair_so.get('pair_to_s_ratio', 0)

            # Force corrélations S/O
            impair_so_strength = abs(impair_s_ratio - 0.5)
            pair_so_strength = abs(pair_s_ratio - 0.5)

            if impair_so_strength > self.config.rollout2_signal_strength_threshold:
                enhanced_correlations.append({
                    'type': 'impair_to_so_enhanced',
                    'strength': impair_so_strength,
                    'pattern': 'IMPAIR→S' if impair_s_ratio > 0.5 else 'IMPAIR→O',
                    'confidence': impair_so_strength * 2,
                    'sample_size': impair_pair_so.get('impact_strength', 0) * 100,  # Estimation
                    'category': 'impair_pair_correlations'
                })

            if pair_so_strength > self.config.rollout2_signal_strength_threshold:
                enhanced_correlations.append({
                    'type': 'pair_to_so_enhanced',
                    'strength': pair_so_strength,
                    'pattern': 'PAIR→S' if pair_s_ratio > 0.5 else 'PAIR→O',
                    'confidence': pair_so_strength * 2,
                    'sample_size': impair_pair_so.get('impact_strength', 0) * 100,
                    'category': 'impair_pair_correlations'
                })

        # ================================================================
        # 2. CORRÉLATIONS SYNC/DESYNC ENRICHIES
        # ================================================================

        # Corrélations SYNC/DESYNC → P/B (sans TIE)
        if 'desync_sync_to_pbt' in cross_index_impacts:
            sync_pbt = cross_index_impacts['desync_sync_to_pbt']

            # Analyser P/B (exclure TIE)
            sync_to_p = sync_pbt.get('sync_to_player', 0)
            sync_to_b = sync_pbt.get('sync_to_banker', 0)
            desync_to_p = sync_pbt.get('desync_to_player', 0)
            desync_to_b = sync_pbt.get('desync_to_banker', 0)

            # Normaliser P/B (sans TIE)
            sync_pb_total = sync_to_p + sync_to_b
            desync_pb_total = desync_to_p + desync_to_b

            if sync_pb_total > 0:
                sync_p_ratio = sync_to_p / sync_pb_total
                sync_strength = abs(sync_p_ratio - 0.5)

                if sync_strength > self.config.rollout2_adjustment_small:
                    enhanced_correlations.append({
                        'type': 'sync_to_pb_enhanced',
                        'strength': sync_strength,
                        'pattern': 'SYNC→P' if sync_p_ratio > 0.5 else 'SYNC→B',
                        'confidence': sync_strength * 2,
                        'sample_size': sync_pb_total,
                        'category': 'sync_desync_correlations'
                    })

            if desync_pb_total > 0:
                desync_p_ratio = desync_to_p / desync_pb_total
                desync_strength = abs(desync_p_ratio - 0.5)

                if desync_strength > self.config.rollout2_adjustment_small:
                    enhanced_correlations.append({
                        'type': 'desync_to_pb_enhanced',
                        'strength': desync_strength,
                        'pattern': 'DESYNC→P' if desync_p_ratio > 0.5 else 'DESYNC→B',
                        'confidence': desync_strength * 2,
                        'sample_size': desync_pb_total,
                        'category': 'sync_desync_correlations'
                    })

        # Corrélations SYNC/DESYNC → S/O
        if 'desync_sync_to_so' in cross_index_impacts:
            sync_so = cross_index_impacts['desync_sync_to_so']

            sync_s_ratio = sync_so.get('sync_to_s_ratio', 0)
            desync_s_ratio = sync_so.get('desync_to_s_ratio', 0)

            # Force corrélations S/O
            sync_so_strength = abs(sync_s_ratio - 0.5)
            desync_so_strength = abs(desync_s_ratio - 0.5)

            if sync_so_strength > self.config.rollout2_adjustment_small:
                enhanced_correlations.append({
                    'type': 'sync_to_so_enhanced',
                    'strength': sync_so_strength,
                    'pattern': 'SYNC→S' if sync_s_ratio > 0.5 else 'SYNC→O',
                    'confidence': sync_so_strength * 2,
                    'sample_size': sync_so.get('impact_strength', 0) * 100,
                    'category': 'sync_desync_correlations'
                })

            if desync_so_strength > self.config.rollout2_adjustment_small:
                enhanced_correlations.append({
                    'type': 'desync_to_so_enhanced',
                    'strength': desync_so_strength,
                    'pattern': 'DESYNC→S' if desync_s_ratio > 0.5 else 'DESYNC→O',
                    'confidence': desync_so_strength * 2,
                    'sample_size': sync_so.get('impact_strength', 0) * 100,
                    'category': 'sync_desync_correlations'
                })

        # ================================================================
        # 3. CORRÉLATIONS COMBINÉES ENRICHIES
        # ================================================================

        # Corrélations États Combinés → P/B (sans TIE)
        if 'combined_to_pbt' in cross_index_impacts:
            combined_pbt = cross_index_impacts['combined_to_pbt']

            if 'state_impacts' in combined_pbt:
                for state, impact_data in combined_pbt['state_impacts'].items():
                    # Analyser P/B (exclure TIE)
                    to_player = impact_data.get('to_player', 0)
                    to_banker = impact_data.get('to_banker', 0)
                    pb_total = to_player + to_banker

                    if pb_total > 0:
                        p_ratio = to_player / pb_total
                        strength = abs(p_ratio - 0.5)

                        if strength > self.config.rollout2_evaluation_weight_standard:  # Seuil plus élevé pour états combinés
                            enhanced_correlations.append({
                                'type': f'{state.lower()}_to_pb_enhanced',
                                'strength': strength,
                                'pattern': f'{state}→P' if p_ratio > 0.5 else f'{state}→B',
                                'confidence': strength * 2,
                                'sample_size': impact_data.get('total_occurrences', 0),
                                'category': 'combined_state_correlations'
                            })

        # Corrélations États Combinés → S/O
        if 'combined_to_so' in cross_index_impacts:
            combined_so = cross_index_impacts['combined_to_so']

            if 'state_impacts' in combined_so:
                for state, impact_data in combined_so['state_impacts'].items():
                    s_ratio = impact_data.get('to_s_ratio', 0)
                    strength = abs(s_ratio - 0.5)

                    if strength > self.config.rollout2_evaluation_weight_standard:
                        enhanced_correlations.append({
                            'type': f'{state.lower()}_to_so_enhanced',
                            'strength': strength,
                            'pattern': f'{state}→S' if s_ratio > 0.5 else f'{state}→O',
                            'confidence': strength * 2,
                            'sample_size': impact_data.get('total_occurrences', 0),
                            'category': 'combined_state_correlations'
                        })

        # ================================================================
        # 4. CORRÉLATIONS TRI-DIMENSIONNELLES
        # ================================================================

        if 'tri_dimensional_impacts' in cross_index_impacts:
            tri_impacts = cross_index_impacts['tri_dimensional_impacts']

            # Analyser IMPAIR+SYNC impacts
            if 'impair_sync_impacts' in tri_impacts:
                impair_sync = tri_impacts['impair_sync_impacts']

                # P/B distribution (sans TIE)
                if 'pbt_distribution' in impair_sync:
                    pbt_dist = impair_sync['pbt_distribution']
                    p_ratio = pbt_dist.get('P', 0)
                    b_ratio = pbt_dist.get('B', 0)
                    pb_total = p_ratio + b_ratio

                    if pb_total > 0:
                        normalized_p = p_ratio / pb_total
                        strength = abs(normalized_p - 0.5)

                        if strength > self.config.rollout2_adjustment_large:  # Seuil élevé pour tri-dimensionnel
                            enhanced_correlations.append({
                                'type': 'impair_sync_to_pb_tri',
                                'strength': strength,
                                'pattern': 'IMPAIR+SYNC→P' if normalized_p > 0.5 else 'IMPAIR+SYNC→B',
                                'confidence': strength * 2,
                                'sample_size': impair_sync.get('sample_size', 0),
                                'category': 'tri_dimensional_correlations'
                            })

                # S/O distribution
                if 'so_distribution' in impair_sync:
                    so_dist = impair_sync['so_distribution']
                    s_ratio = so_dist.get('S', 0)
                    strength = abs(s_ratio - 0.5)

                    if strength > self.config.rollout2_adjustment_large:
                        enhanced_correlations.append({
                            'type': 'impair_sync_to_so_tri',
                            'strength': strength,
                            'pattern': 'IMPAIR+SYNC→S' if s_ratio > 0.5 else 'IMPAIR+SYNC→O',
                            'confidence': strength * 2,
                            'sample_size': impair_sync.get('sample_size', 0),
                            'category': 'tri_dimensional_correlations'
                        })

        # ================================================================
        # 5. CLASSEMENT ET FILTRAGE FINAL
        # ================================================================

        # Trier par force décroissante
        enhanced_correlations.sort(key=lambda x: x['strength'], reverse=True)

        # Filtrer les corrélations significatives (top 10 max)
        significant_correlations = enhanced_correlations[:10]

        # Ajouter métriques globales
        if significant_correlations:
            avg_strength = sum(c['strength'] for c in significant_correlations) / len(significant_correlations)
            max_strength = max(c['strength'] for c in significant_correlations)

            # Ajouter métrique de synthèse
            significant_correlations.append({
                'type': 'enhanced_summary',
                'total_correlations': len(enhanced_correlations),
                'significant_correlations': len(significant_correlations) - 1,  # -1 pour exclure cette entrée
                'average_strength': avg_strength,
                'maximum_strength': max_strength,
                'dominant_category': max(
                    set(c['category'] for c in significant_correlations[:-1]),
                    key=lambda cat: len([c for c in significant_correlations[:-1] if c['category'] == cat])
                ) if len(significant_correlations) > 1 else 'none',
                'category': 'summary'
            })

        return significant_correlations


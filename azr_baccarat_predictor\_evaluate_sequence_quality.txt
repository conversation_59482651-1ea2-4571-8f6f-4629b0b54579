# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 5901 à 5935
# Type: Méthode de la classe AZRCluster

    def _evaluate_sequence_quality(self, sequence: Dict, analyzer_report: Dict) -> Dict:
        """
        Évalue la qualité d'une séquence candidate en utilisant le rapport du Rollout 1

        FOCUS : Sélection intelligente basée sur l'analyse complète du Rollout 1
        """
        evaluation = {
            'signal_alignment_score': 0.0,
            'consistency_score': 0.0,
            'risk_reward_ratio': 0.0,
            'logic_validation_score': 0.0,
            'total_score': 0.0,
            'confidence_factor': 0.0
        }

        # 1. Score d'alignement avec les signaux du Rollout 1
        evaluation['signal_alignment_score'] = self._evaluate_signal_alignment(sequence, analyzer_report)

        # 2. Score de cohérence interne de la séquence
        evaluation['consistency_score'] = self._analyze_sequence_consistency(sequence)

        # 3. Ratio risque/récompense
        evaluation['risk_reward_ratio'] = self._assess_risk_reward_ratio(sequence, analyzer_report)

        # 4. Validation de la logique de la séquence
        evaluation['logic_validation_score'] = self._validate_sequence_logic(sequence, analyzer_report)

        # 5. Score total pondéré
        evaluation['total_score'] = self._calculate_sequence_score(evaluation)

        # 6. Facteur de confiance basé sur le Rollout 1
        synthesis = analyzer_report.get('synthesis', {})
        evaluation['confidence_factor'] = synthesis.get('analysis_quality', 0.5)

        return evaluation
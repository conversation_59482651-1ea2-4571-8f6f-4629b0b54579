# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10575 à 10587
# Type: Méthode de la classe AZRCluster

    def _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str:
        """Calcule le niveau de confiance basé sur la force globale et le nombre de types valides"""

        if global_strength >= 0.8 and valid_strengths >= 4:
            return 'VERY_HIGH'
        elif global_strength >= 0.6 and valid_strengths >= 3:
            return 'HIGH'
        elif global_strength >= 0.4 and valid_strengths >= 2:
            return 'MEDIUM'
        elif global_strength >= 0.2:
            return 'LOW'
        else:
            return 'VERY_LOW'
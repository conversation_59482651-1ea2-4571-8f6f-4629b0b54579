# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 16189 à 16206
# Type: Méthode de la classe AZRBaccaratPredictor

    def _calculate_learnability(self, hypothesis: Dict[str, Any]) -> float:
        """
        Calcule la récompense de learnability selon les formules mathématiques AZR

        Formule AZR : r_e^propose = 1 - r̄_solve si r̄_solve ∉ {0,1}, sinon 0
        Utilise les paramètres centralisés de la configuration
        """
        confidence = hypothesis.get('confidence', self.config.zero_value)

        # Simulation du taux de réussite basé sur la confiance
        success_rate = confidence

        # Application de la formule AZR de learnability avec paramètres centralisés
        if success_rate <= self.config.learnability_min_threshold or success_rate >= self.config.learnability_max_threshold:
            return self.config.learnability_min_threshold  # Tâche impossible ou triviale
        else:
            # Zone de développement proximal optimale
            return self.config.learnability_zone_proximal - success_rate
2025-06-07 23:02:25,856 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:02:25,857 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:02:25,858 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:02:25,863 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:02:25,864 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:02:25,864 - INFO - 🧠 <PERSON><PERSON><PERSON>le AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:02:25,864 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:02:26,265 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:02:26,266 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:02:28,482 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:02:28,483 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:02:28,484 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:02:31,600 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,601 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:02:31,675 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,676 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,677 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,677 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,678 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,679 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,680 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,680 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,681 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,681 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,682 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:02:31,682 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:02:31,683 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:02:31,684 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:02:31,684 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:02:31,685 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: O
2025-06-07 23:03:27,407 - INFO - ✅ Validation: Prédit=O, Réel=S, Correct=False, Précision=0.000
2025-06-07 23:03:27,409 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,410 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:27,491 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,492 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,492 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,493 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,494 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,495 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,496 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,496 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,497 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,498 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,499 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:27,499 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:27,500 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:27,501 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:27,501 - INFO - 📊 Manche #1.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:27,502 - INFO - ✅ Manche traitée: P/B#1.0 BANKER PAIR DESYNC S → Prédiction: O
2025-06-07 23:03:32,273 - INFO - ✅ Validation: Prédit=O, Réel=O, Correct=True, Précision=0.500
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,274 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'sample_size_minimum_3'
2025-06-07 23:03:32,279 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,279 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,280 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,281 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,282 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,282 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,283 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,283 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,284 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,284 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,285 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'
2025-06-07 23:03:32,287 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5700, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7118, in _generate_fallback_sequences
    seq2 = self._generate_sync_based_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 11829, in _generate_sync_based_sequence
    if maintain_sync > self.config.rollout2_confidence_value_high:
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_high'

2025-06-07 23:03:32,287 - ERROR - Erreur système AZR Master: 'AZRConfig' object has no attribute 'cluster_consensus_agreement_threshold'
2025-06-07 23:03:32,288 - ERROR - ❌ Erreur AZR Master: 'NoneType' object has no attribute 'get'
2025-06-07 23:03:32,288 - INFO - 📊 Manche #2.0: PLAYER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:03:32,288 - INFO - ✅ Manche traitée: P/B#2.0 PLAYER PAIR DESYNC O → Prédiction: O
2025-06-07 23:05:47,560 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:05:47,560 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:05:47,561 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:05:47,562 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:05:47,562 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:05:47,563 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:05:48,225 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:05:48,226 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:05:51,016 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:05:51,016 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:05:51,017 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:05:52,468 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'context_bonus_factor'
2025-06-07 23:05:52,468 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'context_bonus_factor'
2025-06-07 23:05:52,547 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,547 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,548 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,549 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,549 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,551 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,552 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,552 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,552 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,553 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,553 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'
2025-06-07 23:05:52,554 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5707, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7134, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12060, in _generate_combined_index_sequence
    base_confidence = self.config.rollout2_base_confidence_medium
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_base_confidence_medium'

2025-06-07 23:05:52,555 - ERROR - ❌ Erreur AZR Master: 'AZRConfig' object has no attribute 'cluster_count'
2025-06-07 23:05:52,555 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: O
2025-06-07 23:05:52,556 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: O
2025-06-07 23:08:46,544 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:08:46,544 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:08:46,545 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:08:46,545 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:08:46,545 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:08:46,976 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:08:46,976 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:08:48,529 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:08:48,529 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:08:48,530 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:08:49,665 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_1'
2025-06-07 23:08:49,665 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_1'
2025-06-07 23:08:49,741 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,742 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,748 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,748 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,751 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,752 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,753 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,753 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,754 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,754 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,755 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'
2025-06-07 23:08:49,755 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5714, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7141, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12198, in _generate_combined_index_sequence
    pb_confidence = self.config.cluster_weight_factor_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'cluster_weight_factor_high'

2025-06-07 23:08:49,756 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 99.6ms)
2025-06-07 23:08:49,757 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-07 23:08:49,757 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-07 23:08:49,758 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: S
2025-06-07 23:14:04,216 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:14:04,216 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:14:04,217 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:14:04,217 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:14:04,217 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:14:04,217 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:14:04,218 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:14:04,218 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:14:04,218 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:14:04,218 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:14:04,219 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:14:04,219 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:14:04,647 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:14:04,647 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:14:05,884 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:14:05,885 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:14:05,886 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:14:07,429 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_2'
2025-06-07 23:14:07,429 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_2'
2025-06-07 23:14:07,502 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,502 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,507 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,507 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,508 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,509 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,510 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,510 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,511 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,512 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,512 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'
2025-06-07 23:14:07,513 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5716, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7143, in _generate_fallback_sequences
    seq3 = self._generate_combined_index_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12202, in _generate_combined_index_sequence
    pb_confidence = self.config.rollout2_confidence_value_medium_high
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_medium_high'

2025-06-07 23:14:07,514 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 95.0ms)
2025-06-07 23:14:07,514 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-07 23:14:07,514 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-07 23:14:07,515 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: S
2025-06-07 23:17:20,080 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-07 23:17:20,080 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-07 23:17:20,081 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-07 23:17:20,081 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-07 23:17:20,081 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-07 23:17:20,081 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-07 23:17:20,082 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-07 23:17:20,082 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-07 23:17:20,082 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-07 23:17:20,082 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-07 23:17:20,083 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-07 23:17:20,083 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-07 23:17:20,507 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-07 23:17:20,507 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-07 23:17:22,363 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-07 23:17:22,363 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-07 23:17:22,365 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-07 23:17:23,744 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-07 23:17:23,745 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-07 23:17:23,803 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,804 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,810 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,811 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,820 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,821 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,821 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,822 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,825 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,825 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,826 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-07 23:17:23,826 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\base\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-07 23:17:23,827 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 92.2ms)
2025-06-07 23:17:23,829 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-07 23:17:23,829 - INFO - 📊 Manche #0.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-07 23:17:23,830 - INFO - ✅ Manche traitée: P/B#0.0 BANKER PAIR DESYNC -- → Prédiction: S
2025-06-08 14:18:00,660 - WARNING - Module d'optimisation non trouvé - utilisation configuration standard
2025-06-08 14:18:00,666 - INFO - Cluster 0 initialisé avec configuration standard
2025-06-08 14:18:00,667 - INFO - Cluster 1 initialisé avec configuration standard
2025-06-08 14:18:00,667 - INFO - Cluster 2 initialisé avec configuration standard
2025-06-08 14:18:00,667 - INFO - Cluster 3 initialisé avec configuration standard
2025-06-08 14:18:00,668 - INFO - Cluster 4 initialisé avec configuration standard
2025-06-08 14:18:00,668 - INFO - Cluster 5 initialisé avec configuration standard
2025-06-08 14:18:00,668 - INFO - Cluster 6 initialisé avec configuration standard
2025-06-08 14:18:00,668 - INFO - Cluster 7 initialisé avec configuration standard
2025-06-08 14:18:00,669 - INFO - 🧠 Intelligence AZR restaurée avec succès - Continuité assurée !
2025-06-08 14:18:00,669 - INFO - 🧠 Modèle AZR Baccarat initialisé avec persistance intelligente
2025-06-08 14:18:00,669 - INFO - 🎯 Système AZR Master activé: 8 clusters parallèles
2025-06-08 14:18:01,195 - INFO - 🎮 Interface graphique AZR initialisée
2025-06-08 14:18:01,196 - INFO - 🚀 Lancement de l'interface graphique AZR ultra-simplifiée
2025-06-08 14:18:02,804 - INFO - 🔥 Brûlage initialisé: IMPAIR → État initial: DESYNC
2025-06-08 14:18:02,810 - INFO - 📊 Séquence complète initialisée: ['IMPAIR']
2025-06-08 14:18:02,810 - INFO - 🔥 Cartes brûlées initialisées: IMPAIR → DESYNC
2025-06-08 14:18:04,592 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:04,592 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:04,676 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,676 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,677 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,677 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,678 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,678 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,680 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,680 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,681 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:04,681 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,682 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,684 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:04,690 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 122.4ms)
2025-06-08 14:18:04,691 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:04,691 - INFO - 📊 Manche #0.0: TIE PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-08 14:18:04,692 - INFO - ✅ Manche traitée: P/B#0.0 TIE PAIR DESYNC -- → Prédiction: S
2025-06-08 14:18:05,155 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:05,156 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:05,162 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,162 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,163 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,164 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,165 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,166 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,167 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,169 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,170 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,176 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,179 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,180 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,183 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.5ms)
2025-06-08 14:18:05,184 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:05,184 - INFO - 📊 Manche #0.0: BANKER IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-08 14:18:05,185 - INFO - ✅ Manche traitée: P/B#0.0 BANKER IMPAIR SYNC -- → Prédiction: S
2025-06-08 14:18:05,854 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.000
2025-06-08 14:18:05,855 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:05,856 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:05,864 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,865 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,866 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,867 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,868 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,868 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,869 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,870 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,872 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:05,882 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,882 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,883 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:05,888 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 33.2ms)
2025-06-08 14:18:05,888 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:05,889 - INFO - 📊 Manche #1.0: PLAYER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-08 14:18:05,889 - INFO - ✅ Manche traitée: P/B#1.0 PLAYER PAIR SYNC O → Prédiction: S
2025-06-08 14:18:06,584 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.500
2025-06-08 14:18:06,646 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:06,647 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:06,652 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,653 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,653 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,654 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,655 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,655 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,656 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,657 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,658 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:06,659 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,660 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,660 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:06,664 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 35.5ms)
2025-06-08 14:18:06,665 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:06,665 - INFO - 📊 Manche #2.0: PLAYER IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: S
2025-06-08 14:18:06,665 - INFO - ✅ Manche traitée: P/B#2.0 PLAYER IMPAIR DESYNC S → Prédiction: S
2025-06-08 14:18:07,344 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.333
2025-06-08 14:18:07,345 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:07,346 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:07,352 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,353 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,354 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,354 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,355 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,356 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,357 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,358 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,360 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:07,360 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,369 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,370 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:07,376 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 31.1ms)
2025-06-08 14:18:07,378 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:07,379 - INFO - 📊 Manche #3.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-08 14:18:07,380 - INFO - ✅ Manche traitée: P/B#3.0 BANKER PAIR DESYNC O → Prédiction: S
2025-06-08 14:18:08,622 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:08,623 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:08,629 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,629 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,630 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,632 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,632 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,632 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,633 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,635 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,636 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:08,645 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,645 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,646 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:08,650 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.3ms)
2025-06-08 14:18:08,651 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:08,651 - INFO - 📊 Manche #4.0: TIE IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-08 14:18:08,652 - INFO - ✅ Manche traitée: P/B#4.0 TIE IMPAIR SYNC -- → Prédiction: S
2025-06-08 14:18:09,483 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.500
2025-06-08 14:18:09,484 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:09,485 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:09,491 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,491 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,492 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,493 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,494 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,495 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,496 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,498 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:09,501 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,503 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,505 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,506 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:09,512 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.3ms)
2025-06-08 14:18:09,513 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:09,513 - INFO - 📊 Manche #4.0: BANKER IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: S
2025-06-08 14:18:09,514 - INFO - ✅ Manche traitée: P/B#4.0 BANKER IMPAIR DESYNC S → Prédiction: S
2025-06-08 14:18:10,041 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.600
2025-06-08 14:18:10,043 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:10,043 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:10,049 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,050 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,051 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,051 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,052 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,054 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,055 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,057 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,062 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,063 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,063 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,063 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,068 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 26.1ms)
2025-06-08 14:18:10,068 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:10,069 - INFO - 📊 Manche #5.0: BANKER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-08 14:18:10,069 - INFO - ✅ Manche traitée: P/B#5.0 BANKER PAIR DESYNC S → Prédiction: S
2025-06-08 14:18:10,832 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.500
2025-06-08 14:18:10,833 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:10,834 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:10,840 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,840 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,841 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,842 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,843 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,844 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,845 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,846 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,847 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:10,849 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,856 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,856 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:10,870 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 37.6ms)
2025-06-08 14:18:10,871 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:10,871 - INFO - 📊 Manche #6.0: PLAYER PAIR DESYNC PAIR_DESYNC → Prédiction: S
2025-06-08 14:18:10,872 - INFO - ✅ Manche traitée: P/B#6.0 PLAYER PAIR DESYNC O → Prédiction: S
2025-06-08 14:18:11,389 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.571
2025-06-08 14:18:11,390 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:11,390 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:11,396 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,397 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,397 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,398 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,399 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,399 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,400 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,402 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,404 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:11,412 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,412 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,412 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:11,416 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 26.9ms)
2025-06-08 14:18:11,417 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:11,419 - ERROR - ❌ Erreur sauvegarde intelligence AZR: 'AZRBaccaratPredictor' object has no attribute 'cluster_id'
2025-06-08 14:18:11,419 - INFO - 📊 Manche #7.0: PLAYER IMPAIR SYNC IMPAIR_SYNC → Prédiction: S
2025-06-08 14:18:11,420 - INFO - ✅ Manche traitée: P/B#7.0 PLAYER IMPAIR SYNC S → Prédiction: S
2025-06-08 14:18:12,059 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.500
2025-06-08 14:18:12,060 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:12,060 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:12,067 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,067 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,068 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,070 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,070 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,070 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,071 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,072 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,073 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,082 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,082 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,083 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,086 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 26.6ms)
2025-06-08 14:18:12,086 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:12,087 - INFO - 📊 Manche #8.0: BANKER PAIR SYNC PAIR_SYNC → Prédiction: S
2025-06-08 14:18:12,087 - INFO - ✅ Manche traitée: P/B#8.0 BANKER PAIR SYNC O → Prédiction: S
2025-06-08 14:18:12,575 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.556
2025-06-08 14:18:12,576 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:12,576 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:12,582 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,583 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,583 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,584 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,585 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,586 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,587 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,589 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:12,598 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,599 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,599 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,600 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:12,605 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 30.1ms)
2025-06-08 14:18:12,606 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:12,606 - INFO - 📊 Manche #9.0: BANKER IMPAIR DESYNC IMPAIR_DESYNC → Prédiction: S
2025-06-08 14:18:12,607 - INFO - ✅ Manche traitée: P/B#9.0 BANKER IMPAIR DESYNC S → Prédiction: S
2025-06-08 14:18:13,305 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.600
2025-06-08 14:18:13,305 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:13,306 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:13,312 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,312 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,313 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,314 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,314 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,316 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,316 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,317 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,319 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,326 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,328 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,328 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,332 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 26.7ms)
2025-06-08 14:18:13,332 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:13,589 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.636
2025-06-08 14:18:13,591 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:13,592 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:13,598 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,599 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,600 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,601 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,602 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,602 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,603 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,604 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,606 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:13,615 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,615 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,615 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:13,619 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.9ms)
2025-06-08 14:18:13,620 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:14,186 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.583
2025-06-08 14:18:14,187 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:14,188 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:14,194 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,194 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,195 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,196 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,197 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,197 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,198 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,199 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,200 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,210 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,210 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,210 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,223 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 36.2ms)
2025-06-08 14:18:14,226 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:14,886 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.615
2025-06-08 14:18:14,887 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:14,887 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:14,894 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,894 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,895 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,896 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,897 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,898 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,900 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,901 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:14,904 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,908 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,908 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,908 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:14,913 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 26.1ms)
2025-06-08 14:18:14,913 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:22,370 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:22,370 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:22,377 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,377 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,378 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,379 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,380 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,380 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,381 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,383 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,384 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,393 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,393 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,394 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,397 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.0ms)
2025-06-08 14:18:22,398 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:22,907 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:22,907 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:22,913 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,914 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,915 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,916 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,917 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,918 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,920 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,921 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:22,922 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,924 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,925 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,927 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:22,934 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 27.7ms)
2025-06-08 14:18:22,934 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:23,514 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.571
2025-06-08 14:18:23,515 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:23,516 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:23,522 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,523 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,523 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,525 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,525 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,525 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,526 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,528 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,529 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:23,538 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,538 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,539 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:23,542 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 27.6ms)
2025-06-08 14:18:23,543 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:24,042 - INFO - ✅ Validation: Prédit=S, Réel=S, Correct=True, Précision=0.600
2025-06-08 14:18:24,043 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:24,044 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:24,050 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,050 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,051 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,052 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,053 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,054 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,055 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,056 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,057 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:24,060 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,066 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,067 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:24,070 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 27.2ms)
2025-06-08 14:18:24,070 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:25,420 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.562
2025-06-08 14:18:25,421 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:25,422 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:25,429 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,429 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,430 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,431 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,432 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,433 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,433 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,435 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,436 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,445 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,448 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,448 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,452 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 31.0ms)
2025-06-08 14:18:25,452 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:25,816 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.529
2025-06-08 14:18:25,817 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:25,818 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:25,825 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,825 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,826 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,827 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,828 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,828 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,828 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,830 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,831 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:25,833 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,840 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,841 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:25,855 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 37.8ms)
2025-06-08 14:18:25,855 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:26,322 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.500
2025-06-08 14:18:26,324 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:26,325 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:26,331 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,331 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,332 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,333 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,334 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,334 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,335 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,337 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,338 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,349 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,350 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,350 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,355 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 31.5ms)
2025-06-08 14:18:26,356 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !
2025-06-08 14:18:26,820 - INFO - ✅ Validation: Prédit=S, Réel=O, Correct=False, Précision=0.474
2025-06-08 14:18:26,821 - ERROR - Erreur rollout analyzer cluster 0: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:26,822 - ERROR - Erreur rollout analyzer cluster 1: 'AZRConfig' object has no attribute 'rollout2_priority_weight_3'
2025-06-08 14:18:26,828 - ERROR - Erreur rollout generator cluster 2: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,829 - ERROR - Détails erreur cluster 2: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,830 - ERROR - Erreur rollout generator cluster 3: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,831 - ERROR - Erreur rollout generator cluster 4: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,831 - ERROR - Détails erreur cluster 3: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,832 - ERROR - Détails erreur cluster 4: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,833 - ERROR - Erreur rollout generator cluster 6: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,834 - ERROR - Erreur rollout generator cluster 5: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,836 - ERROR - Erreur rollout generator cluster 7: AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'
2025-06-08 14:18:26,844 - ERROR - Détails erreur cluster 6: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,845 - ERROR - Détails erreur cluster 5: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,845 - ERROR - Détails erreur cluster 7: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 5718, in _rollout_generator
    candidates = self._generate_fallback_sequences(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 7154, in _generate_fallback_sequences
    seq4 = self._generate_so_pattern_sequence(generation_space)
  File "C:\Users\<USER>\Desktop\A\baseder\azr_baccarat_predictor.py", line 12312, in _generate_so_pattern_sequence
    base_so_confidence = self.config.rollout2_confidence_value_standard
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AZRConfig' object has no attribute 'rollout2_confidence_value_standard'

2025-06-08 14:18:26,849 - INFO - 🎯 AZR Master: 8/8 clusters → Consensus: S (confiance: 95.0%, accord: 100.0%, temps: 28.4ms)
2025-06-08 14:18:26,849 - INFO - 🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !

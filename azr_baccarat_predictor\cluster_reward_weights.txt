# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2112 à 2117
# Type: Méthode de la classe AZRConfig

    def cluster_reward_weights(self):
        return {
            'rollout1_weight': self.cluster_rollout1_weight,    # Neutre (pas de bonus/malus)
            'rollout2_weight': self.cluster_rollout2_weight,    # 40% - Responsabilité intermédiaire
            'rollout3_weight': self.cluster_rollout3_weight     # 60% - Responsabilité maximale
        }
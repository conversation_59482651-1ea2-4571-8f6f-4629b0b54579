MÉTHODE : _calculate_temporal_consistency
LIGNE DÉBUT : 7764
SIGNATURE : def _calculate_temporal_consistency(self, phase_strengths: Dict) -> float:
================================================================================

    def _calculate_temporal_consistency(self, phase_strengths: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_temporal_consistency.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la consistance temporelle des corrélations"""

        values = list(phase_strengths.values())
        if len(values) < 2:
            return 0.0

        # Consistance = 1 - coefficient de variation
        mean_strength = sum(values) / len(values)
        if mean_strength == 0:
            return 0.0

        std_dev = (self._calculate_variance(values)) ** 0.5
        cv = std_dev / mean_strength

        return max(0.0, 1.0 - cv)


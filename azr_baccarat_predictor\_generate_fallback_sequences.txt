# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7118 à 7162
# Type: Méthode de la classe AZRCluster

    def _generate_fallback_sequences(self, generation_space: Dict) -> List[Dict]:
        """
        Génération de séquences fallback si pas de signaux optimisés

        FALLBACK : Méthode classique si les nouvelles sections ne sont pas disponibles
        """
        candidates = []

        # Séquence 1 : Exploitation corrélations IMPAIR/PAIR dominantes
        seq1 = self._generate_impair_pair_optimized_sequence(generation_space)
        candidates.append({
            'sequence_data': seq1,
            'estimated_probability': self.config.rollout2_max_probability,
            'strategy': self.config.rollout2_fallback_strategy_1,
            'justification': self.config.rollout2_fallback_justification_1
        })

        # Séquence 2 : Exploitation synchronisation SYNC/DESYNC
        seq2 = self._generate_sync_based_sequence(generation_space)
        candidates.append({
            'sequence_data': seq2,
            'estimated_probability': self.config.rollout2_alternative_probability,
            'strategy': self.config.rollout2_fallback_strategy_2,
            'justification': self.config.rollout2_fallback_justification_2
        })

        # Séquence 3 : Exploitation index combiné dominant
        seq3 = self._generate_combined_index_sequence(generation_space)
        candidates.append({
            'sequence_data': seq3,
            'estimated_probability': self.config.rollout2_rupture_probability,
            'strategy': self.config.rollout2_fallback_strategy_3,
            'justification': self.config.rollout2_fallback_justification_3
        })

        # Séquence 4 : Exploitation patterns S/O
        seq4 = self._generate_so_pattern_sequence(generation_space)
        candidates.append({
            'sequence_data': seq4,
            'estimated_probability': self.config.rollout2_conservative_probability,
            'strategy': self.config.rollout2_fallback_strategy_4,
            'justification': self.config.rollout2_fallback_justification_4
        })

        return candidates
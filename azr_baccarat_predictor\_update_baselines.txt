# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 16288 à 16309
# Type: Méthode de la classe AZRBaccaratPredictor

    def _update_baselines(self, hypothesis: Dict[str, Any]):
        """
        Met à jour les baselines AZR selon les formules mathématiques centralisées

        Formules AZR :
        b^propose = E_τ~π_θ^propose [r^propose_e(τ,π_θ)]
        b^solve = E_y~π_θ^solve [r^solve_e(y,y*)]
        """
        confidence = hypothesis.get('confidence', self.config.zero_value)
        learnability = hypothesis.get('learnability', self.config.zero_value)

        # Mise à jour avec momentum selon formules mathématiques AZR
        momentum = self.config.baseline_momentum
        epsilon = self.config.baseline_epsilon

        # Application des formules de baseline avec stabilité numérique
        self.baseline_propose = (momentum * self.baseline_propose +
                               (self.config.learnability_zone_proximal - momentum) * learnability +
                               epsilon)
        self.baseline_solve = (momentum * self.baseline_solve +
                             (self.config.learnability_zone_proximal - momentum) * confidence +
                             epsilon)
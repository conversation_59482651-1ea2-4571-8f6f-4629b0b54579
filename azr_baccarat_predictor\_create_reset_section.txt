# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14227 à 14242
# Type: Méthode de la classe AZRBaccaratInterface

    def _create_reset_section(self, parent):
        """Crée les boutons Reset (coin bas droite)"""
        reset_frame = ttk.Frame(parent)
        reset_frame.place(relx=1.0, rely=1.0, anchor="se")

        # Bouton Sauvegarder (en haut)
        ttk.Button(reset_frame, text="💾 Sauvegarder",
                  command=self.save_intelligence).pack(pady=2)

        # Bouton Soft Reset (partie en cours)
        ttk.But<PERSON>(reset_frame, text="🔄 Soft Reset",
                  command=self.soft_reset).pack(pady=2)

        # Bouton Hard Reset (tout l'apprentissage)
        ttk.Button(reset_frame, text="🔥 Hard Reset",
                  command=self.hard_reset).pack(pady=2)
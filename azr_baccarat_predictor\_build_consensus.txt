# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 13560 à 13671
# Type: Méthode de la classe AZRMaster

    def _build_consensus(self, cluster_results: List[Dict]) -> Dict:
        """
        Construction consensus intelligent à partir des résultats clusters

        Utilise vote pondéré par confiance et détection accord majoritaire.
        CORRECTION: Gestion robuste des valeurs None et erreurs clusters
        """
        # CORRECTION: Filtrage plus robuste des résultats valides
        valid_results = []
        for r in cluster_results:
            if (r is not None and
                isinstance(r, dict) and
                r.get('prediction') is not None and
                isinstance(r.get('prediction'), dict)):
                valid_results.append(r)

        if not valid_results:
            logger.warning("⚠️ Aucun résultat cluster valide - Utilisation fallback consensus")
            return {
                'consensus_so': 'S',
                'consensus_confidence': 0.0,
                'agreement_level': 0.0,
                'voting_details': {'error': 'Aucun résultat valide', 'fallback_used': True}
            }

        # Collecte prédictions S/O avec pondération confiance
        so_votes = {'S': 0.0, 'O': 0.0}
        total_weight = 0.0

        for result in valid_results:
            try:
                prediction = result.get('prediction', {})
                if prediction is None:
                    continue

                next_hand = prediction.get('next_hand_prediction', {})
                if next_hand is None:
                    continue

                predicted_so = next_hand.get('predicted_so', 'S')
                confidence = prediction.get('cluster_confidence', 0.5)

                # CORRECTION: Validation des valeurs
                if predicted_so not in ['S', 'O']:
                    predicted_so = 'S'
                if not isinstance(confidence, (int, float)) or confidence < 0:
                    confidence = 0.5

                so_votes[predicted_so] += confidence
                total_weight += confidence
            except Exception as e:
                logger.warning(f"⚠️ Erreur traitement résultat cluster: {e}")
                continue

        # CORRECTION: Normalisation votes avec protection division par zéro
        if total_weight > 0:
            so_votes['S'] /= total_weight
            so_votes['O'] /= total_weight
        else:
            # Fallback si aucun poids valide
            logger.warning("⚠️ Aucun poids valide pour consensus - Utilisation valeurs par défaut")
            so_votes = {'S': 0.5, 'O': 0.5}

        # Détermination consensus
        consensus_so = 'S' if so_votes['S'] > so_votes['O'] else 'O'
        consensus_confidence = max(so_votes['S'], so_votes['O'])

        # Calcul niveau d'accord
        agreement_level = abs(so_votes['S'] - so_votes['O'])  # Plus proche de 1 = plus d'accord

        # CORRECTION: Bonus si 8 clusters d'accord (confiance boostée) avec gestion erreurs
        try:
            if len(valid_results) == 8:
                unanimous_so = True
                for r in valid_results:
                    try:
                        pred = r.get('prediction', {})
                        if pred is None:
                            unanimous_so = False
                            break
                        next_hand = pred.get('next_hand_prediction', {})
                        if next_hand is None:
                            unanimous_so = False
                            break
                        predicted_so = next_hand.get('predicted_so', 'S')
                        if predicted_so != consensus_so:
                            unanimous_so = False
                            break
                    except Exception:
                        unanimous_so = False
                        break

                if unanimous_so:
                    consensus_confidence = min(consensus_confidence * self.config.cluster_boost_factor, 0.95)  # Boost 20%
                    agreement_level = 1.0
        except Exception as e:
            logger.warning(f"⚠️ Erreur calcul unanimité: {e}")

        # Stockage confiance globale
        self.global_shared_memory['global_confidence'] = consensus_confidence

        return {
            'consensus_so': consensus_so,
            'consensus_confidence': consensus_confidence,
            'agreement_level': agreement_level,
            'voting_details': {
                'so_votes': so_votes,
                'clusters_participating': len(valid_results),
                'total_clusters': len(cluster_results),
                'unanimous_agreement': agreement_level >= 0.8
            }
        }
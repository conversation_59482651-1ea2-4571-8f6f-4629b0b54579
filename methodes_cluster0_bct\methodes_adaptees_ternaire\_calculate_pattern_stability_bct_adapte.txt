MÉTHODE : _calculate_pattern_stability
LIGNE DÉBUT : 8239
SIGNATURE : def _calculate_pattern_stability(self, individual_strengths: Dict) -> float:
================================================================================

    def _calculate_pattern_stability(self, individual_strengths: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_pattern_stability.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la stabilité des patterns (inverse de la variance)"""

        strengths = list(individual_strengths.values())
        if len(strengths) < 2:
            return 0.0

        variance = self._calculate_variance(strengths)
        stability = 1.0 / (1.0 + variance)  # Normalisation

        return stability


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10184 à 10193
# Type: Méthode de la classe AZRCluster

    def _calculate_variance(self, values: List[float]) -> float:
        """Calcule la variance d'une liste de valeurs"""

        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)

        return variance
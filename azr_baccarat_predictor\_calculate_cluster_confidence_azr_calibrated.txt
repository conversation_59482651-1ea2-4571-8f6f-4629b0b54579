# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6297 à 6378
# Type: Méthode de la classe AZRCluster

    def _calculate_cluster_confidence_azr_calibrated(self, best_sequence: Dict, analyzer_report: Dict) -> float:
        """
        Calcule la confiance calibrée selon les formules AZR officielles

        Basé sur :
        - Formule de confiance calibrée AZR : C(y|x) = 1 - exp(-λ * max_prob)
        - Facteurs de risque TRR++
        - Incertitude épistémique
        - Consensus inter-rollouts

        Returns:
            float: Confiance calibrée (0-1)
        """
        # 1. PROBABILITÉ MAXIMALE DE LA SOLUTION (Formule AZR)
        sequence_probability = best_sequence.get('estimated_probability', self.config.probability_neutral)
        evaluation_score = best_sequence.get('evaluation', {}).get('total_score', self.config.probability_neutral)

        # Probabilité composite de la solution
        solution_prob = (sequence_probability + evaluation_score) / self.config.two_value

        # 2. FACTEUR DE CALIBRATION ADAPTATIF
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', self.config.probability_neutral)

        signals_summary = analyzer_report.get('signals_summary', {})
        overall_confidence = signals_summary.get('overall_confidence', self.config.probability_neutral)

        # Facteur de calibration basé sur la qualité de l'analyse
        default_factor = self.config.confidence_calibration['default_calibration_factor']
        calibration_factor = default_factor + (analysis_quality - self.config.probability_neutral) + (overall_confidence - self.config.probability_neutral)

        min_factor = self.config.confidence_calibration['min_calibration_factor']
        max_factor = self.config.confidence_calibration['max_calibration_factor']
        calibration_factor = max(min_factor, min(max_factor, calibration_factor))

        # 3. CONFIANCE CALIBRÉE (Formule AZR officielle)
        import math
        calibrated_confidence = self.config.probability_clamp_max - math.exp(-calibration_factor * solution_prob)

        # 4. FACTEURS DE RISQUE TRR++
        risk_factors = self._calculate_confidence_risk_factors(best_sequence, analyzer_report)

        # 5. AJUSTEMENT PAR INCERTITUDE ÉPISTÉMIQUE
        epistemic_uncertainty = self._calculate_epistemic_uncertainty(analyzer_report)
        max_uncertainty_impact = self.config.confidence_calibration['max_uncertainty_impact']
        uncertainty_adjustment = self.config.probability_clamp_max - (epistemic_uncertainty * max_uncertainty_impact)

        # 6. CONSENSUS INTER-ROLLOUTS
        consensus_factor = self._calculate_rollout_consensus(best_sequence, analyzer_report)

        # 7. CONFIANCE FINALE COMPOSITE (pondération selon configuration)
        cal_weight = self.config.confidence_calibration['calibrated_confidence_weight']
        risk_weight = self.config.confidence_calibration['risk_factors_weight']
        unc_weight = self.config.confidence_calibration['uncertainty_weight']
        cons_weight = self.config.confidence_calibration['consensus_weight']

        final_confidence = (
            calibrated_confidence * cal_weight +           # Confiance calibrée AZR
            (self.config.probability_clamp_max - risk_factors) * risk_weight +           # Facteurs de risque inversés
            uncertainty_adjustment * unc_weight +          # Ajustement incertitude
            consensus_factor * cons_weight                 # Consensus rollouts
        )

        # 8. AJUSTEMENTS CONDITIONNELS SELON AZR
        # Bonus si exploitation prête (zone de développement proximal)
        exploitation_ready = signals_summary.get('exploitation_ready', False)
        if exploitation_ready:
            exploitation_bonus = self.config.confidence_calibration['exploitation_bonus']
            final_confidence *= exploitation_bonus

        # Ajustement basé sur le niveau d'alerte (facteur de risque)
        quick_access = analyzer_report.get('quick_access', {})
        alert_level = quick_access.get('alert_level', 'MEDIUM')

        if alert_level == 'HIGH':
            high_alert_bonus = self.config.confidence_calibration['high_alert_bonus']
            final_confidence *= high_alert_bonus
        elif alert_level == 'LOW':
            low_alert_malus = self.config.confidence_calibration['low_alert_malus']
            final_confidence *= low_alert_malus

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, final_confidence))
MÉTHODE : _generate_impair_pair_optimized_sequence
LIGNE DÉBUT : 9167
SIGNATURE : def _generate_impair_pair_optimized_sequence(self, generation_space: Dict) -> List[Dict]:
================================================================================

    def _generate_impair_pair_optimized_sequence(self, generation_space: Dict) -> List[Dict]:
"""
    ADAPTATION BCT - _generate_impair_pair_optimized_sequence.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Génère séquence optimisée basée sur corrélations IMPAIR/PAIR → P/B

        IMPORTANT: Focus sur P/B, exclusion des TIE
        Exploite les corrélations détectées entre positions IMPAIR/PAIR et résultats P/B

        Args:
            generation_space: Espace de génération complet avec contraintes et guidance

        Returns:
            Liste de mains prédites avec stratégie IMPAIR/PAIR optimisée
        """

        sequence_length = self.config.rollout2_fixed_length  # Longueur fixe selon spécifications AZR
        sequence = []

        # ================================================================
        # 1. EXTRACTION DES CORRÉLATIONS IMPAIR/PAIR PRÉFÉRÉES
        # ================================================================

        preferred_correlations = generation_space.get('preferred_correlations', [])

        # Rechercher corrélations IMPAIR/PAIR → P/B
        impair_correlation = None
        pair_correlation = None

        for correlation in preferred_correlations:
            if correlation.get('type') == 'impair_to_pb':
                impair_correlation = correlation
            elif correlation.get('type') == 'pair_to_pb':
                pair_correlation = correlation

        # ================================================================
        # 2. CONTRAINTES ET GUIDANCE
        # ================================================================

        enhanced_constraints = generation_space.get('enhanced_constraints', {})
        cross_impact_guidance = generation_space.get('cross_impact_guidance', {})

        # Contraintes IMPAIR/PAIR
        force_pair_bias = enhanced_constraints.get('force_pair_bias', 0)
        impair_caution = enhanced_constraints.get('impair_caution', 0)
        force_impair_break = enhanced_constraints.get('force_impair_break', 0)

        # Guidance impacts croisés IMPAIR/PAIR → S/O
        impair_pair_so_guidance = cross_impact_guidance.get('impair_pair_so', {})

        # ================================================================
        # 3. GÉNÉRATION SÉQUENCE OPTIMISÉE
        # ================================================================

        for i in range(sequence_length):
            hand_number = i + 1  # Position dans la séquence (1, 2, 3, ...)
            position_type = 'impair_5' if hand_number % 2 == 1 else ['pair_4', 'pair_6']

            # ================================================================
            # 3.1. PRÉDICTION P/B BASÉE SUR CORRÉLATIONS
            # ================================================================

            predicted_pb = 'P'  # Défaut
            pb_confidence = self.config.rollout3_neutral_evaluation_value  # Confiance de base

            if position_type == 'impair_5' and impair_correlation:
                # Exploiter corrélation IMPAIR → P/B
                pattern = impair_correlation.get('pattern', '')
                if '→P' in pattern:
                    predicted_pb = 'P'
                elif '→B' in pattern:
                    predicted_pb = 'B'

                pb_confidence = self.config.rollout3_neutral_evaluation_value + impair_correlation.get('strength', 0)
                pb_confidence = min(pb_confidence, 0.95)  # Limiter confiance max

            elif position_type == ['pair_4', 'pair_6'] and pair_correlation:
                # Exploiter corrélation PAIR → P/B
                pattern = pair_correlation.get('pattern', '')
                if '→P' in pattern:
                    predicted_pb = 'P'
                elif '→B' in pattern:
                    predicted_pb = 'B'

                pb_confidence = self.config.rollout3_neutral_evaluation_value + pair_correlation.get('strength', 0)
                pb_confidence = min(pb_confidence, 0.95)

            # ================================================================
            # 3.2. AJUSTEMENTS BASÉS SUR CONTRAINTES
            # ================================================================

            # Contrainte force PAIR bias (si trop d'IMPAIR consécutifs)
            if force_pair_bias > 0 and position_type == ['pair_4', 'pair_6']:
                # Renforcer prédiction pour position PAIR
                pb_confidence = min(pb_confidence + force_pair_bias * self.config.rollout2_adjustment_large, 0.95)

            # Contrainte prudence IMPAIR
            if impair_caution > 0 and position_type == 'impair_5':
                # Réduire confiance pour position IMPAIR
                pb_confidence = max(pb_confidence - impair_caution * self.config.rollout2_adjustment_small, 0.55)

            # Contrainte force rupture IMPAIR (si trop de PAIR consécutifs)
            if force_impair_break > 0 and position_type == 'impair_5':
                # Légèrement renforcer prédiction pour position IMPAIR
                pb_confidence = min(pb_confidence + force_impair_break * self.config.rollout2_adjustment_small, 0.85)

            # ================================================================
            # 3.3. PRÉDICTION S/O BASÉE SUR GUIDANCE CROISÉE
            # ================================================================

            predicted_so = 'S'  # Défaut
            so_confidence = self.config.rollout3_neutral_evaluation_value  # Confiance de base

            if impair_pair_so_guidance:
                # Exploiter guidance IMPAIR/PAIR → S/O
                so_pattern = impair_pair_so_guidance.get('pattern', '')
                so_strength = impair_pair_so_guidance.get('strength', 0)

                if position_type == 'impair_5':
                    if 'IMPAIR→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'IMPAIR→O' in so_pattern:
                        predicted_so = 'O'
                elif position_type == ['pair_4', 'pair_6']:
                    if 'PAIR→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'PAIR→O' in so_pattern:
                        predicted_so = 'O'

                so_confidence = self.config.rollout3_neutral_evaluation_value + so_strength
                so_confidence = min(so_confidence, 0.9)

            # ================================================================
            # 3.4. DÉTERMINATION ÉTAT SYNC
            # ================================================================

            # État sync basé sur prédiction P/B et pattern attendu
            expected_pattern = ['P', 'B']  # Alternance de base
            expected_outcome = expected_pattern[i % len(expected_pattern)]

            if predicted_pb == expected_outcome:
                sync_state = 'SYNC'
                sync_confidence = pb_confidence
            else:
                sync_state = 'DESYNC'
                sync_confidence = 1.0 - pb_confidence

            # ================================================================
            # 3.5. CONSTRUCTION MAIN PRÉDITE
            # ================================================================

            # Confiance globale pondérée (P/B poids 2, S/O poids 3)
            global_confidence = (pb_confidence * 2 + so_confidence * 3) / 5

            hand = {
                'hand_number': hand_number,
                'position_type': position_type,
                'predicted_pbt': predicted_pb,
                'predicted_so': predicted_so,
                'sync_state': sync_state,
                'pb_confidence': pb_confidence,
                'so_confidence': so_confidence,
                'sync_confidence': sync_confidence,
                'global_confidence': global_confidence,
                'strategy_source': 'impair_pair_optimized',
                'correlations_used': {
                    'impair_correlation': impair_correlation is not None,
                    'pair_correlation': pair_correlation is not None,
                    'cross_impact_so': bool(impair_pair_so_guidance)
                },
                'constraints_applied': {
                    'force_pair_bias': force_pair_bias,
                    'impair_caution': impair_caution,
                    'force_impair_break': force_impair_break
                }
            }

            sequence.append(hand)

        return sequence


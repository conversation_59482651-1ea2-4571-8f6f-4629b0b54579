# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15078 à 15096
# Type: Méthode de la classe AZRBaccaratPredictor

    def _get_cluster_specializations(self) -> Dict:
        """
        🎯 SPÉCIALISATIONS PAR CLUSTER

        Returns:
            Spécialisations découvertes pour chaque cluster
        """
        specializations = {}

        for cluster_id in range(self.config.one_value, self.config.cluster_count + 1):  # 8 clusters
            specializations[f'cluster_{cluster_id}'] = {
                'pattern_type': getattr(self, f'cluster_{cluster_id}_pattern_type', 'mixed'),
                'optimal_window_size': getattr(self, f'cluster_{cluster_id}_window_size', self.config.cluster_default_window_size),
                'discovered_patterns': getattr(self, f'cluster_{cluster_id}_patterns', {}),
                'success_rate': getattr(self, f'cluster_{cluster_id}_success_rate', self.config.zero_value),
                'specialization_strength': getattr(self, f'cluster_{cluster_id}_specialization', self.config.zero_value)
            }

        return specializations
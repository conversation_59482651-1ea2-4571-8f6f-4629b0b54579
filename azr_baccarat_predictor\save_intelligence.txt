# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14451 à 14516
# Type: Méthode de la classe AZRBaccaratInterface

    def save_intelligence(self):
        """
        💾 SAUVEGARDE + RESET : Sauvegarde l'intelligence et remet à zéro la partie

        ÉTAPES :
        1. Sauvegarde toute l'intelligence acquise
        2. Réinitialise la partie courante (comme Soft Reset)
        3. PRÉSERVE tout l'apprentissage global

        Sauvegarde :
        - Toute l'intelligence acquise
        - Tous les patterns découverts
        - Tous les baselines adaptatifs
        - Toutes les métriques de performance
        - Backup horodaté

        Réinitialise :
        - Compteur de manches
        - Indices de la partie courante
        - Session AZR courante
        """
        from tkinter import messagebox

        try:
            if self.azr_predictor:
                # 1. SAUVEGARDER l'intelligence avant reset
                self.azr_predictor._save_azr_intelligence()

                # Capturer les métriques avant reset de session
                precision_actuelle = self.azr_predictor.current_accuracy
                predictions_totales = self.azr_predictor.total_predictions
                patterns_decouverts = len(self.azr_predictor.discovered_patterns)

                # 2. RÉINITIALISER LA PARTIE COURANTE (comme Soft Reset)
                self._reset_current_game_only()

                # 3. Afficher confirmation à l'utilisateur
                messagebox.showinfo(
                    "💾 Sauvegarde + Reset réussi",
                    "✅ Intelligence AZR sauvegardée avec succès !\n"
                    "🔄 Partie courante réinitialisée\n\n"
                    f"📊 Précision globale : {precision_actuelle:.1%}\n"
                    f"🎯 Prédictions totales : {predictions_totales}\n"
                    f"🧠 Patterns découverts : {patterns_decouverts}\n\n"
                    "💾 Fichiers sauvegardés :\n"
                    "• azr_intelligence_state.json\n"
                    "• azr_discoveries.json\n"
                    "• azr_baselines.json\n"
                    "• Backup horodaté\n\n"
                    "🆕 Prêt pour une nouvelle partie !"
                )

                logger.info("💾 Sauvegarde + Reset de partie réussi")
            else:
                messagebox.showwarning(
                    "⚠️ Erreur",
                    "Aucun modèle AZR actif à sauvegarder"
                )

        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde + reset: {e}")
            messagebox.showerror(
                "❌ Erreur de sauvegarde",
                f"Erreur lors de la sauvegarde :\n{str(e)}\n\n"
                "Vérifiez les permissions d'écriture."
            )
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10136 à 10169
# Type: Méthode de la classe AZRCluster

    def _analyze_correlation_trend(self, correlation_values: List[float], trend_name: str) -> Dict:
        """Analyse la tendance d'évolution d'une corrélation"""

        if len(correlation_values) < 3:
            return {'trend_type': 'INSUFFICIENT_DATA', 'trend_strength': 0.0}

        early, mid, late = correlation_values

        # Analyser type de tendance
        if late > mid > early:
            trend_type = 'STRENGTHENING'
            trend_strength = late - early
        elif early > mid > late:
            trend_type = 'WEAKENING'
            trend_strength = early - late
        elif mid > early and mid > late:
            trend_type = 'PEAK_THEN_DECLINE'
            trend_strength = mid - min(early, late)
        elif mid < early and mid < late:
            trend_type = 'DIP_THEN_RECOVER'
            trend_strength = max(early, late) - mid
        else:
            trend_type = 'STABLE'
            trend_strength = max(correlation_values) - min(correlation_values)

        return {
            'trend_type': trend_type,
            'trend_strength': trend_strength,
            'early_value': early,
            'mid_value': mid,
            'late_value': late,
            'best_phase': 'early' if early == max(correlation_values) else
                         ('mid' if mid == max(correlation_values) else 'late')
        }
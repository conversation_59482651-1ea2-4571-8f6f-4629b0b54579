MÉTHODE : _analyze_temporal_correlation_evolution
LIGNE DÉBUT : 7223
SIGNATURE : def _analyze_temporal_correlation_evolution(self, impair_pair_seq: List[str], desync_sync_seq: List[str],
================================================================================

    def _analyze_temporal_correlation_evolution(self, impair_pair_seq: List[str], desync_sync_seq: List[str],
                                              pbt_seq: List[str], so_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _analyze_temporal_correlation_evolution.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Analyse l'évolution TEMPORELLE des corrélations entre indices 1-3 et indices 4-5

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Analyse comment les corrélations évoluent au fil du temps dans le jeu

        Args:
            impair_pair_seq: Séquence index 1 (IMPAIR/PAIR)
            desync_sync_seq: Séquence index 2 (DESYNC/SYNC)
            pbt_seq: Séquence index 4 (P/B/T)
            so_seq: Séquence index 5 (S/O)

        Returns:
            Dictionnaire avec analyse de l'évolution temporelle des corrélations
        """

        temporal_evolution = {
            'phase_analysis': {},                    # Analyse par phase temporelle
            'correlation_trends': {},                # Tendances d'évolution des corrélations
            'optimal_prediction_phases': {},         # Phases optimales pour prédictions
            'temporal_strength_metrics': {}          # Métriques de force temporelle
        }

        if not impair_pair_seq or len(impair_pair_seq) < 6:  # Minimum 6 mains pour analyse temporelle
            return {
                'phase_analysis': {},
                'correlation_trends': {},
                'optimal_prediction_phases': {
                    'insufficient_data': True,
                    'minimum_required': 6,
                    'current_length': len(impair_pair_seq) if impair_pair_seq else 0
                },
                'temporal_strength_metrics': {
                    'total_phases': 0,
                    'evolution_strength': 0.0
                }
            }

        # ================================================================
        # 1. DÉCOUPAGE EN PHASES TEMPORELLES
        # ================================================================

        total_length = len(impair_pair_seq)

        # Découpage en 3 phases : début/milieu/fin
        phase_size = total_length // 3
        remainder = total_length % 3

        phases = {
            'early_game': {
                'start': 0,
                'end': phase_size + (1 if remainder > 0 else 0) - 1,
                'name': 'Début de partie'
            },
            'mid_game': {
                'start': phase_size + (1 if remainder > 0 else 0),
                'end': 2 * phase_size + (1 if remainder > 1 else 0) - 1,
                'name': 'Milieu de partie'
            },
            'late_game': {
                'start': 2 * phase_size + (1 if remainder > 1 else 0),
                'end': total_length - 1,
                'name': 'Fin de partie'
            }
        }

        # ================================================================
        # 2. ANALYSE DES CORRÉLATIONS PAR PHASE
        # ================================================================

        for phase_name, phase_info in phases.items():
            start_pos = phase_info['start']
            end_pos = phase_info['end']

            # Extraire séquences pour cette phase
            phase_impair_pair = impair_pair_seq[start_pos:end_pos + 1]
            phase_desync_sync = desync_sync_seq[start_pos:end_pos + 1]
            phase_pbt = pbt_seq[start_pos:min(end_pos + 1, len(pbt_seq))]
            phase_so = so_seq[max(0, start_pos - 1):min(end_pos, len(so_seq))]  # S/O commence à manche 2

            # Analyser corrélations IMPAIR/PAIR → P/B (sans TIE) pour cette phase
            phase_impair_pb_corr = self._calculate_phase_impair_pair_pb_correlation(phase_impair_pair, phase_pbt)

            # Analyser corrélations IMPAIR/PAIR → S/O pour cette phase
            phase_impair_so_corr = self._calculate_phase_impair_pair_so_correlation(phase_impair_pair, phase_so)

            # Analyser corrélations SYNC/DESYNC → P/B (sans TIE) pour cette phase
            phase_sync_pb_corr = self._calculate_phase_sync_desync_pb_correlation(phase_desync_sync, phase_pbt)

            # Analyser corrélations SYNC/DESYNC → S/O pour cette phase
            phase_sync_so_corr = self._calculate_phase_sync_desync_so_correlation(phase_desync_sync, phase_so)

            # Force globale des corrélations pour cette phase
            phase_strength = self._calculate_phase_correlation_strength(
                phase_impair_pb_corr, phase_impair_so_corr,
                phase_sync_pb_corr, phase_sync_so_corr
            )

            temporal_evolution['phase_analysis'][phase_name] = {
                'phase_info': phase_info,
                'impair_pair_to_pb_correlation': phase_impair_pb_corr,
                'impair_pair_to_so_correlation': phase_impair_so_corr,
                'sync_desync_to_pb_correlation': phase_sync_pb_corr,
                'sync_desync_to_so_correlation': phase_sync_so_corr,
                'phase_correlation_strength': phase_strength,
                'sample_size': end_pos - start_pos + 1
            }

        # ================================================================
        # 3. ANALYSE DES TENDANCES D'ÉVOLUTION
        # ================================================================

        # Évolution corrélations IMPAIR/PAIR → P/B
        impair_pb_evolution = self._analyze_correlation_trend([
            temporal_evolution['phase_analysis']['early_game']['impair_pair_to_pb_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['mid_game']['impair_pair_to_pb_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['late_game']['impair_pair_to_pb_correlation']['correlation_strength']
        ], 'IMPAIR_PAIR_TO_PB')

        # Évolution corrélations IMPAIR/PAIR → S/O
        impair_so_evolution = self._analyze_correlation_trend([
            temporal_evolution['phase_analysis']['early_game']['impair_pair_to_so_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['mid_game']['impair_pair_to_so_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['late_game']['impair_pair_to_so_correlation']['correlation_strength']
        ], 'IMPAIR_PAIR_TO_SO')

        # Évolution corrélations SYNC/DESYNC → P/B
        sync_pb_evolution = self._analyze_correlation_trend([
            temporal_evolution['phase_analysis']['early_game']['sync_desync_to_pb_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['mid_game']['sync_desync_to_pb_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['late_game']['sync_desync_to_pb_correlation']['correlation_strength']
        ], 'SYNC_DESYNC_TO_PB')

        # Évolution corrélations SYNC/DESYNC → S/O
        sync_so_evolution = self._analyze_correlation_trend([
            temporal_evolution['phase_analysis']['early_game']['sync_desync_to_so_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['mid_game']['sync_desync_to_so_correlation']['correlation_strength'],
            temporal_evolution['phase_analysis']['late_game']['sync_desync_to_so_correlation']['correlation_strength']
        ], 'SYNC_DESYNC_TO_SO')

        temporal_evolution['correlation_trends'] = {
            'impair_pair_to_pb_trend': impair_pb_evolution,
            'impair_pair_to_so_trend': impair_so_evolution,
            'sync_desync_to_pb_trend': sync_pb_evolution,
            'sync_desync_to_so_trend': sync_so_evolution
        }

        # ================================================================
        # 4. IDENTIFICATION DES PHASES OPTIMALES
        # ================================================================

        # Identifier la meilleure phase pour chaque type de prédiction
        phase_strengths = {
            'early_game': temporal_evolution['phase_analysis']['early_game']['phase_correlation_strength'],
            'mid_game': temporal_evolution['phase_analysis']['mid_game']['phase_correlation_strength'],
            'late_game': temporal_evolution['phase_analysis']['late_game']['phase_correlation_strength']
        }

        best_phase = max(phase_strengths.keys(), key=lambda x: phase_strengths[x])
        worst_phase = min(phase_strengths.keys(), key=lambda x: phase_strengths[x])

        # Analyser stabilité des corrélations
        correlation_stability = self._calculate_correlation_stability(phase_strengths)

        temporal_evolution['optimal_prediction_phases'] = {
            'best_overall_phase': best_phase,
            'worst_overall_phase': worst_phase,
            'phase_strengths': phase_strengths,
            'correlation_stability': correlation_stability,
            'recommendation': self._generate_temporal_recommendation(
                best_phase, correlation_stability, temporal_evolution['correlation_trends']
            )
        }

        # ================================================================
        # 5. MÉTRIQUES DE FORCE TEMPORELLE
        # ================================================================

        # Force d'évolution globale
        evolution_strength = self._calculate_evolution_strength(temporal_evolution['correlation_trends'])

        # Consistance temporelle
        temporal_consistency = self._calculate_temporal_consistency(phase_strengths)

        # Prédictibilité temporelle
        temporal_predictability = self._calculate_temporal_predictability(temporal_evolution['correlation_trends'])

        temporal_evolution['temporal_strength_metrics'] = {
            'total_phases': len(phases),
            'evolution_strength': evolution_strength,
            'temporal_consistency': temporal_consistency,
            'temporal_predictability': temporal_predictability,
            'average_phase_strength': sum(phase_strengths.values()) / len(phase_strengths),
            'strength_variance': self._calculate_variance(list(phase_strengths.values())),
            'has_significant_evolution': evolution_strength > self.config.evolution_strength_threshold  # Seuil configurable
        }

        return temporal_evolution


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 9945 à 9997
# Type: Méthode de la classe AZRCluster

    def _calculate_phase_impair_pair_so_correlation(self, impair_pair_seq: List[str], so_seq: List[str]) -> Dict:
        """Calcule corrélations IMPAIR/PAIR → S/O pour une phase"""

        if not impair_pair_seq or not so_seq:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        # Aligner séquences (S/O commence à manche 2)
        min_length = min(len(impair_pair_seq) - 1, len(so_seq))  # -1 car S/O décalé
        if min_length < 1:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        aligned_impair_pair = impair_pair_seq[1:min_length + 1]  # Décaler de 1 pour aligner avec S/O
        aligned_so = so_seq[:min_length]

        # Calculer corrélations IMPAIR/PAIR → S/O
        impair_positions = [i for i, val in enumerate(aligned_impair_pair) if val == 'IMPAIR']
        pair_positions = [i for i, val in enumerate(aligned_impair_pair) if val == 'PAIR']

        impair_to_s = sum(1 for pos in impair_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        impair_to_o = sum(1 for pos in impair_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')
        pair_to_s = sum(1 for pos in pair_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        pair_to_o = sum(1 for pos in pair_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')

        # Force de corrélation S/O
        impair_total = impair_to_s + impair_to_o
        pair_total = pair_to_s + pair_to_o

        impair_strength = 0.0
        pair_strength = 0.0

        if impair_total > 0:
            impair_s_ratio = impair_to_s / impair_total
            impair_strength = abs(impair_s_ratio - 0.5)

        if pair_total > 0:
            pair_s_ratio = pair_to_s / pair_total
            pair_strength = abs(pair_s_ratio - 0.5)

        # Force globale pondérée
        total_sample = impair_total + pair_total
        if total_sample > 0:
            correlation_strength = (impair_strength * impair_total + pair_strength * pair_total) / total_sample
        else:
            correlation_strength = 0.0

        return {
            'correlation_strength': correlation_strength,
            'impair_strength': impair_strength,
            'pair_strength': pair_strength,
            'sample_size': total_sample,
            'impair_sample': impair_total,
            'pair_sample': pair_total
        }
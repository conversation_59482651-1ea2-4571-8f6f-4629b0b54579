# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17402 à 17421
# Type: Méthode de la classe AZRBaccaratPredictor

    def _calculate_accuracy_trend(self) -> float:
        """Calcule la tendance de précision récente"""
        if len(self.accuracy_history) < self.config.rollout_accuracy_min_samples:
            return 0.0

        # 🚀 OPTIMISATION INTELLIGENCE ÉMERGENTE : Fenêtre précision spécialisée par cluster
        cluster_accuracy_window = self.config.get_cluster_accuracy_window(self.cluster_id)
        recent_accuracy = self.accuracy_history[-cluster_accuracy_window:]
        if len(recent_accuracy) < self.config.rollout_accuracy_min_regression:
            return 0.0

        # Régression linéaire simple
        x = np.array(range(len(recent_accuracy)))
        y = np.array(recent_accuracy)

        if len(x) < self.config.rollout_accuracy_min_regression:
            return 0.0

        slope, _ = np.polyfit(x, y, 1)
        return slope
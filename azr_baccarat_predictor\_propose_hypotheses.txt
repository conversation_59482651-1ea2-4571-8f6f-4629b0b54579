# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15396 à 15439
# Type: Méthode de la classe AZRBaccaratPredictor

    def _propose_hypotheses(self) -> List[Dict[str, Any]]:
        """
        Rôle Proposeur AZR : Génère des hypothèses de patterns

        Returns:
            Liste d'hypothèses avec leurs métriques de learnability
        """
        hypotheses = []

        # Hypothèse 1: Pattern de synchronisation
        sync_hypothesis = self._analyze_sync_patterns()
        if sync_hypothesis:
            hypotheses.append({
                'type': 'sync_pattern',
                'prediction': sync_hypothesis['prediction'],
                'confidence': sync_hypothesis['confidence'],
                'learnability': self._calculate_learnability(sync_hypothesis)
            })

        # Hypothèse 2: Pattern de parité
        parity_hypothesis = self._analyze_parity_patterns()
        if parity_hypothesis:
            hypotheses.append({
                'type': 'parity_pattern',
                'prediction': parity_hypothesis['prediction'],
                'confidence': parity_hypothesis['confidence'],
                'learnability': self._calculate_learnability(parity_hypothesis)
            })

        # Hypothèse 3: Pattern de séquence récente
        recent_hypothesis = self._analyze_recent_patterns()
        if recent_hypothesis:
            hypotheses.append({
                'type': 'recent_pattern',
                'prediction': recent_hypothesis['prediction'],
                'confidence': recent_hypothesis['confidence'],
                'learnability': self._calculate_learnability(recent_hypothesis)
            })

        # Hypothèse 4: Pattern de rollouts (exploration)
        rollout_hypotheses = self._generate_rollout_hypotheses()
        hypotheses.extend(rollout_hypotheses)

        return hypotheses
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 11030 à 11329
# Type: Méthode de la classe AZRCluster

    def _identify_enhanced_high_confidence_zones(self, all_indices: Dict, cross_index_impacts: Dict) -> List[Dict]:
        """
        Identifie les zones de haute confiance ENRICHIES en utilisant l'analyse complète des impacts croisés

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Version avancée qui utilise tous les impacts croisés pour identifier les zones les plus fiables

        Args:
            all_indices: Dictionnaire complet avec tous les indices analysés
            cross_index_impacts: Analyse complète des impacts croisés entre indices

        Returns:
            Liste des zones de haute confiance enrichies avec métriques avancées
        """

        enhanced_zones = []

        # ================================================================
        # 1. ZONES BASÉES SUR SYNCHRONISATION ENRICHIE
        # ================================================================

        # Zone synchronisation élevée (version enrichie)
        sync_rate = all_indices.get('desync_sync', {}).get('global_sync_rate', 0)
        if sync_rate > self.config.rollout2_sync_rate_enriched_threshold:  # Seuil enrichi

            # Analyser impact de la synchronisation sur S/O
            sync_so_confidence = self.config.rollout3_neutral_evaluation_value  # Confiance de base
            if 'desync_sync_to_so' in cross_index_impacts:
                sync_so_impact = cross_index_impacts['desync_sync_to_so']
                if 'impact_strength' in sync_so_impact:
                    sync_so_confidence = min(sync_rate + sync_so_impact['impact_strength'], 1.0)

            enhanced_zones.append({
                'type': 'enhanced_high_sync',
                'confidence': sync_so_confidence,
                'base_sync_rate': sync_rate,
                'description': f'Synchronisation enrichie ({sync_rate:.1%}) avec impact S/O',
                'category': 'synchronization_zones',
                'strength': sync_so_confidence,
                'exploitability': 'HIGH' if sync_so_confidence > self.config.confidence_very_high_threshold else 'MEDIUM'
            })

        # ================================================================
        # 2. ZONES BASÉES SUR CORRÉLATIONS FORTES P/B (SANS TIE)
        # ================================================================

        # Zones corrélations IMPAIR/PAIR → P/B fortes
        if 'impair_pair_to_pbt' in cross_index_impacts:
            impair_pair_pbt = cross_index_impacts['impair_pair_to_pbt']

            # Analyser corrélations P/B (exclure TIE)
            impair_to_p = impair_pair_pbt.get('impair_to_player', 0)
            impair_to_b = impair_pair_pbt.get('impair_to_banker', 0)
            pair_to_p = impair_pair_pbt.get('pair_to_player', 0)
            pair_to_b = impair_pair_pbt.get('pair_to_banker', 0)

            # Normaliser P/B (exclure TIE)
            impair_pb_total = impair_to_p + impair_to_b
            pair_pb_total = pair_to_p + pair_to_b

            if impair_pb_total > 0:
                impair_p_ratio = impair_to_p / impair_pb_total
                impair_strength = abs(impair_p_ratio - 0.5)

                if impair_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance IMPAIR → P/B
                    enhanced_zones.append({
                        'type': 'impair_pb_high_confidence',
                        'confidence': impair_strength * 2,  # Normalisation
                        'pattern': 'IMPAIR→P' if impair_p_ratio > 0.5 else 'IMPAIR→B',
                        'description': f'Zone haute confiance IMPAIR → P/B ({impair_strength:.1%})',
                        'category': 'correlation_zones',
                        'strength': impair_strength,
                        'sample_size': impair_pb_total,
                        'exploitability': 'VERY_HIGH' if impair_strength > 0.3 else 'HIGH'
                    })

            if pair_pb_total > 0:
                pair_p_ratio = pair_to_p / pair_pb_total
                pair_strength = abs(pair_p_ratio - 0.5)

                if pair_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance PAIR → P/B
                    enhanced_zones.append({
                        'type': 'pair_pb_high_confidence',
                        'confidence': pair_strength * 2,
                        'pattern': 'PAIR→P' if pair_p_ratio > 0.5 else 'PAIR→B',
                        'description': f'Zone haute confiance PAIR → P/B ({pair_strength:.1%})',
                        'category': 'correlation_zones',
                        'strength': pair_strength,
                        'sample_size': pair_pb_total,
                        'exploitability': 'VERY_HIGH' if pair_strength > 0.3 else 'HIGH'
                    })

        # ================================================================
        # 3. ZONES BASÉES SUR CORRÉLATIONS FORTES S/O
        # ================================================================

        # Zones corrélations IMPAIR/PAIR → S/O fortes
        if 'impair_pair_to_so' in cross_index_impacts:
            impair_pair_so = cross_index_impacts['impair_pair_to_so']

            impair_s_ratio = impair_pair_so.get('impair_to_s_ratio', 0)
            pair_s_ratio = impair_pair_so.get('pair_to_s_ratio', 0)

            # Force corrélations S/O
            impair_so_strength = abs(impair_s_ratio - 0.5)
            pair_so_strength = abs(pair_s_ratio - 0.5)

            if impair_so_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance IMPAIR → S/O
                enhanced_zones.append({
                    'type': 'impair_so_high_confidence',
                    'confidence': impair_so_strength * 2,
                    'pattern': 'IMPAIR→S' if impair_s_ratio > 0.5 else 'IMPAIR→O',
                    'description': f'Zone haute confiance IMPAIR → S/O ({impair_so_strength:.1%})',
                    'category': 'so_correlation_zones',
                    'strength': impair_so_strength,
                    'exploitability': 'VERY_HIGH' if impair_so_strength > 0.3 else 'HIGH'
                })

            if pair_so_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance PAIR → S/O
                enhanced_zones.append({
                    'type': 'pair_so_high_confidence',
                    'confidence': pair_so_strength * 2,
                    'pattern': 'PAIR→S' if pair_s_ratio > 0.5 else 'PAIR→O',
                    'description': f'Zone haute confiance PAIR → S/O ({pair_so_strength:.1%})',
                    'category': 'so_correlation_zones',
                    'strength': pair_so_strength,
                    'exploitability': 'VERY_HIGH' if pair_so_strength > 0.3 else 'HIGH'
                })

        # Zones corrélations SYNC/DESYNC → S/O fortes
        if 'desync_sync_to_so' in cross_index_impacts:
            sync_so = cross_index_impacts['desync_sync_to_so']

            sync_s_ratio = sync_so.get('sync_to_s_ratio', 0)
            desync_s_ratio = sync_so.get('desync_to_s_ratio', 0)

            # Force corrélations S/O
            sync_so_strength = abs(sync_s_ratio - 0.5)
            desync_so_strength = abs(desync_s_ratio - 0.5)

            if sync_so_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance SYNC → S/O
                enhanced_zones.append({
                    'type': 'sync_so_high_confidence',
                    'confidence': sync_so_strength * 2,
                    'pattern': 'SYNC→S' if sync_s_ratio > 0.5 else 'SYNC→O',
                    'description': f'Zone haute confiance SYNC → S/O ({sync_so_strength:.1%})',
                    'category': 'so_correlation_zones',
                    'strength': sync_so_strength,
                    'exploitability': 'VERY_HIGH' if sync_so_strength > 0.3 else 'HIGH'
                })

            if desync_so_strength > self.config.rollout2_adjustment_large:  # Zone haute confiance DESYNC → S/O
                enhanced_zones.append({
                    'type': 'desync_so_high_confidence',
                    'confidence': desync_so_strength * 2,
                    'pattern': 'DESYNC→S' if desync_s_ratio > 0.5 else 'DESYNC→O',
                    'description': f'Zone haute confiance DESYNC → S/O ({desync_so_strength:.1%})',
                    'category': 'so_correlation_zones',
                    'strength': desync_so_strength,
                    'exploitability': 'VERY_HIGH' if desync_so_strength > 0.3 else 'HIGH'
                })

        # ================================================================
        # 4. ZONES BASÉES SUR ÉTATS COMBINÉS FORTS
        # ================================================================

        # Zones états combinés → P/B (sans TIE)
        if 'combined_to_pbt' in cross_index_impacts:
            combined_pbt = cross_index_impacts['combined_to_pbt']

            if 'state_impacts' in combined_pbt:
                for state, impact_data in combined_pbt['state_impacts'].items():
                    # Analyser P/B (exclure TIE)
                    to_player = impact_data.get('to_player', 0)
                    to_banker = impact_data.get('to_banker', 0)
                    pb_total = to_player + to_banker

                    if pb_total > 0:
                        p_ratio = to_player / pb_total
                        strength = abs(p_ratio - 0.5)

                        if strength > self.config.rollout2_zone_confidence_threshold:  # Seuil élevé pour états combinés
                            enhanced_zones.append({
                                'type': f'{state.lower()}_pb_high_confidence',
                                'confidence': strength * 2,
                                'pattern': f'{state}→P' if p_ratio > 0.5 else f'{state}→B',
                                'description': f'Zone haute confiance {state} → P/B ({strength:.1%})',
                                'category': 'combined_state_zones',
                                'strength': strength,
                                'sample_size': impact_data.get('total_occurrences', 0),
                                'exploitability': 'VERY_HIGH' if strength > 0.35 else 'HIGH'
                            })

        # Zones états combinés → S/O
        if 'combined_to_so' in cross_index_impacts:
            combined_so = cross_index_impacts['combined_to_so']

            if 'state_impacts' in combined_so:
                for state, impact_data in combined_so['state_impacts'].items():
                    s_ratio = impact_data.get('to_s_ratio', 0)
                    strength = abs(s_ratio - 0.5)

                    if strength > self.config.rollout2_zone_confidence_threshold:  # Seuil élevé pour états combinés
                        enhanced_zones.append({
                            'type': f'{state.lower()}_so_high_confidence',
                            'confidence': strength * 2,
                            'pattern': f'{state}→S' if s_ratio > 0.5 else f'{state}→O',
                            'description': f'Zone haute confiance {state} → S/O ({strength:.1%})',
                            'category': 'combined_state_zones',
                            'strength': strength,
                            'sample_size': impact_data.get('total_occurrences', 0),
                            'exploitability': 'VERY_HIGH' if strength > 0.35 else 'HIGH'
                        })

        # ================================================================
        # 5. ZONES BASÉES SUR PATTERNS TRI-DIMENSIONNELS
        # ================================================================

        if 'tri_dimensional_impacts' in cross_index_impacts:
            tri_impacts = cross_index_impacts['tri_dimensional_impacts']

            # Analyser IMPAIR+SYNC impacts
            if 'impair_sync_impacts' in tri_impacts:
                impair_sync = tri_impacts['impair_sync_impacts']
                sample_size = impair_sync.get('sample_size', 0)

                if sample_size >= 5:  # Échantillon suffisant

                    # P/B distribution (sans TIE)
                    if 'pbt_distribution' in impair_sync:
                        pbt_dist = impair_sync['pbt_distribution']
                        p_ratio = pbt_dist.get('P', 0)
                        b_ratio = pbt_dist.get('B', 0)
                        pb_total = p_ratio + b_ratio

                        if pb_total > 0:
                            normalized_p = p_ratio / pb_total
                            strength = abs(normalized_p - 0.5)

                            if strength > self.config.rollout3_quality_bonus_small:  # Seuil très élevé pour tri-dimensionnel
                                enhanced_zones.append({
                                    'type': 'impair_sync_pb_tri_confidence',
                                    'confidence': strength * 2,
                                    'pattern': 'IMPAIR+SYNC→P' if normalized_p > 0.5 else 'IMPAIR+SYNC→B',
                                    'description': f'Zone tri-dimensionnelle IMPAIR+SYNC → P/B ({strength:.1%})',
                                    'category': 'tri_dimensional_zones',
                                    'strength': strength,
                                    'sample_size': sample_size,
                                    'exploitability': 'ULTRA_HIGH'
                                })

                    # S/O distribution
                    if 'so_distribution' in impair_sync:
                        so_dist = impair_sync['so_distribution']
                        s_ratio = so_dist.get('S', 0)
                        strength = abs(s_ratio - 0.5)

                        if strength > self.config.rollout3_quality_bonus_small:  # Seuil très élevé pour tri-dimensionnel
                            enhanced_zones.append({
                                'type': 'impair_sync_so_tri_confidence',
                                'confidence': strength * 2,
                                'pattern': 'IMPAIR+SYNC→S' if s_ratio > 0.5 else 'IMPAIR+SYNC→O',
                                'description': f'Zone tri-dimensionnelle IMPAIR+SYNC → S/O ({strength:.1%})',
                                'category': 'tri_dimensional_zones',
                                'strength': strength,
                                'sample_size': sample_size,
                                'exploitability': 'ULTRA_HIGH'
                            })

        # ================================================================
        # 6. CLASSEMENT ET FILTRAGE FINAL
        # ================================================================

        # Trier par confiance décroissante
        enhanced_zones.sort(key=lambda x: x['confidence'], reverse=True)

        # Filtrer les zones significatives (top 8 max)
        significant_zones = enhanced_zones[:8]

        # Ajouter métriques globales
        if significant_zones:
            avg_confidence = sum(z['confidence'] for z in significant_zones) / len(significant_zones)
            max_confidence = max(z['confidence'] for z in significant_zones)

            # Ajouter métrique de synthèse
            significant_zones.append({
                'type': 'enhanced_zones_summary',
                'total_zones': len(enhanced_zones),
                'significant_zones': len(significant_zones) - 1,  # -1 pour exclure cette entrée
                'average_confidence': avg_confidence,
                'maximum_confidence': max_confidence,
                'dominant_category': max(
                    set(z['category'] for z in significant_zones[:-1]),
                    key=lambda cat: len([z for z in significant_zones[:-1] if z['category'] == cat])
                ) if len(significant_zones) > 1 else 'none',
                'ultra_high_zones': len([z for z in significant_zones[:-1] if z.get('exploitability') == 'ULTRA_HIGH']),
                'category': 'summary'
            })

        return significant_zones
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 3995 à 4031
# Type: Méthode de la classe AZRCluster

    def _apply_cluster_specialization(self, base_analysis: Dict, cluster_id: int, spec_params: Dict) -> Dict:
        """
        🎯 APPLICATION SPÉCIALISATION CLUSTER GÉNÉRIQUE - Utilise paramètres centralisés

        Applique la spécialisation selon les paramètres centralisés dans AZRConfig.
        """
        cluster_specialization = {
            'specialization_type': spec_params['type'],
            'cluster_id': cluster_id,
            'fenetre_recente_optimisee': self.config.get_cluster_recent_window_size(cluster_id)
        }

        # Calculer le bonus total de spécialisation
        total_bonus = self.config.zero_value

        # Bonus des analyses IMPAIR spécialisées
        impair_analysis = base_analysis.get('impair_bias', {})
        total_bonus += impair_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Bonus des analyses SYNC spécialisées
        sync_analysis = base_analysis.get('sync_bias', {})
        total_bonus += sync_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Bonus global spécialisation
        cluster_specialization['specialization_bonus'] = min(self.config.one_value, total_bonus)
        cluster_specialization['impair_bonus'] = impair_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)
        cluster_specialization['sync_bonus'] = sync_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Métriques spécialisées
        cluster_specialization['patterns_detected'] = (
            impair_analysis.get(f'c{cluster_id}_recent_sequences_count', 0) +
            sync_analysis.get(f'c{cluster_id}_recent_breaks_count', 0)
        )

        cluster_specialization['cluster_score'] = total_bonus

        return cluster_specialization
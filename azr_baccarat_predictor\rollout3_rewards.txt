# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2088 à 2107
# Type: Méthode de la classe AZRConfig

    def rollout3_rewards(self):
        return {
            # Confiance calibrée (TRR++)
            'confidence_bonus_correct': self.rollout3_confidence_bonus_correct,    # +20% si correct
            'confidence_bonus_incorrect': self.rollout3_confidence_bonus_incorrect,  # +10% si humble

            # Zone proximale de risque
            'optimal_risk': self.rollout3_optimal_risk,
            'min_risk': self.rollout3_min_risk,
            'max_risk': self.rollout3_max_risk,
            'difficulty_bonus_max': self.rollout3_difficulty_bonus_max,        # +10% max

            # Sur-confiance (innovation AZR)
            'overconfidence_threshold': self.rollout3_overconfidence_threshold,
            'overconfidence_penalty_max': self.rollout3_overconfidence_penalty_max,  # -10% max

            # Born<PERSON> de récompense
            'min_reward': self.rollout3_min_reward,
            'max_reward': self.rollout3_max_reward                   # Max possible
        }
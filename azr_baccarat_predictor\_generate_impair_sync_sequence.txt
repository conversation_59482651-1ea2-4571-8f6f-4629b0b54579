# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7553 à 7596
# Type: Méthode de la classe AZRCluster

    def _generate_impair_sync_sequence(self, sequence_length: int, generation_space: Dict) -> List[str]:
        """
        Génère une séquence exploitant le pattern IMPAIR_SYNC

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Analyser les corrélations IMPAIR_SYNC dans les données
        combined_analysis = generation_space.get('indices_analysis', {}).get('combined', {})
        combined_sequence = combined_analysis.get('combined_sequence', [])

        # Trouver les patterns IMPAIR_SYNC et leurs outcomes
        impair_sync_outcomes = []
        pbt_sequence = generation_space.get('indices_analysis', {}).get('pbt', {}).get('pbt_sequence', [])

        if len(combined_sequence) == len(pbt_sequence):
            for i, state in enumerate(combined_sequence):
                if state == 'IMPAIR_SYNC' and pbt_sequence[i] in ['P', 'B']:
                    impair_sync_outcomes.append(pbt_sequence[i])

        # Déterminer le résultat le plus fréquent pour IMPAIR_SYNC
        if impair_sync_outcomes:
            p_count = impair_sync_outcomes.count('P')
            b_count = impair_sync_outcomes.count('B')
            preferred_outcome = 'P' if p_count >= b_count else 'B'
        else:
            preferred_outcome = 'P'  # Valeur par défaut basée sur les découvertes

        # Générer la séquence avec focus sur le preferred_outcome (longueur fixe 4)
        for i in range(self.config.rollout2_fixed_length):
            if i < self.config.rollout2_fixed_length // 2:
                # Première moitié : favoriser le preferred_outcome
                sequence.append(preferred_outcome)
            else:
                # Seconde moitié : alterner pour diversifier
                last = sequence[-1]
                sequence.append('B' if last == 'P' else 'P')

        return sequence
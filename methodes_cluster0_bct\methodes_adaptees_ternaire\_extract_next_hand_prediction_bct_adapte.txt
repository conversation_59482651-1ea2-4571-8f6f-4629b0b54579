MÉTHODE : _extract_next_hand_prediction
LIGNE DÉBUT : 10927
SIGNATURE : def _extract_next_hand_prediction(self, best_sequence) -> Dict:
================================================================================

    def _extract_next_hand_prediction(self, best_sequence) -> Dict:
"""
    ADAPTATION BCT - _extract_next_hand_prediction.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Extraction prédiction prochaine manche"""
        # Adaptation à la structure des données (liste ou dictionnaire)
        if isinstance(best_sequence, dict):
            sequence_data = best_sequence.get('sequence_data', [])
        else:
            sequence_data = best_sequence  # C'est déjà une liste

        if sequence_data and len(sequence_data) > 0:
            next_hand = sequence_data[0]  # Premier élément de la séquence
            if isinstance(next_hand, dict):
                return {
                    'predicted_so': next_hand.get('so_conversion', 'S'),
                    'predicted_parity': next_hand.get('parity', ['pair_4', 'pair_6']),
                    'predicted_sync': next_hand.get('sync_state', 'SYNC'),
                    'predicted_combined': next_hand.get('combined_state', 'pair_4_sync, pair_6_sync'),
                    'confidence': next_hand.get('generation_confidence', 0.7)
                }

        return {
            'predicted_so': 'S',
            'predicted_parity': ['pair_4', 'pair_6'],
            'predicted_sync': 'SYNC',
            'predicted_combined': 'pair_4_sync, pair_6_sync',
            'confidence': 0.5
        }

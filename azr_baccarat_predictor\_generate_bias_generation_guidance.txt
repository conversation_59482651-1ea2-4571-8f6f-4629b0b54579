# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 5297 à 5308
# Type: Méthode de la classe AZRCluster

    def _generate_bias_generation_guidance(self, bias_synthesis: Dict) -> Dict:
        """Génère les directives de génération basées sur les biais pour le Rollout 2"""
        strongest_bias = bias_synthesis.get('strongest_bias', {})
        exploitation_signals = bias_synthesis.get('exploitation_signals', {})

        return {
            'primary_focus': strongest_bias.get('bias_type', 'balanced'),
            'exploitation_intensity': strongest_bias.get('bias_strength', 0.0),
            'sequence_generation_strategy': bias_synthesis.get('optimal_exploitation_strategy', {}).get('strategy_type', 'balanced'),
            'bias_multiplier': exploitation_signals.get('exploitation_multiplier', 1.0),
            'confidence_threshold': self.config.confidence_low_threshold if strongest_bias.get('bias_strength', self.config.zero_value) > self.config.confidence_medium_threshold else self.config.confidence_medium_threshold
        }
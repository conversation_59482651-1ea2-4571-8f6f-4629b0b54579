# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6995 à 7030
# Type: Méthode de la classe AZRCluster

    def _define_optimized_generation_space(self, signals_summary: Dict, generation_guidance: Dict,
                                         quick_access: Dict, indices_analysis: Dict,
                                         synthesis: Dict, sequence_metadata: Dict) -> Dict:
        """
        Définit l'espace de génération optimisé basé sur les signaux du Rollout 1

        NOUVEAU : Utilise les sections optimisées pour une génération plus efficace
        """
        generation_space = {
            # Signaux prioritaires du Rollout 1
            'top_signals': signals_summary.get('top_signals', []),
            'recommended_strategy': generation_guidance.get('primary_focus', 'conservative'),
            'exploitation_ready': signals_summary.get('exploitation_ready', False),

            # Guidance de génération
            'primary_focus': generation_guidance.get('primary_focus', 'conservative'),
            'secondary_focus': generation_guidance.get('secondary_focus', 'balanced'),
            'avoid_patterns': generation_guidance.get('avoid_patterns', []),
            'confidence_thresholds': generation_guidance.get('confidence_thresholds', {}),
            'optimal_sequence_length': self.config.rollout2_fixed_length,  # Longueur fixe selon spécifications AZR
            'risk_level': generation_guidance.get('risk_level', 'medium'),

            # Accès rapide aux prédictions
            'current_state': quick_access.get('current_state', 'unknown'),
            'next_prediction_pb': quick_access.get('next_prediction_pb'),
            'next_prediction_so': quick_access.get('next_prediction_so'),
            'prediction_confidence': quick_access.get('prediction_confidence', 0.0),
            'alert_level': quick_access.get('alert_level', 'MEDIUM'),

            # Données détaillées (fallback)
            'indices_analysis': indices_analysis,
            'synthesis': synthesis,
            'sequence_metadata': sequence_metadata
        }

        return generation_space
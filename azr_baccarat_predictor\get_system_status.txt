# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 13693 à 13735
# Type: Méthode de la classe AZRMaster

    def get_system_status(self) -> Dict:
        """Retourne statut complet du système AZR avec spécialisations"""
        cluster_status = []
        for i, cluster in enumerate(self.clusters):
            # Informations de base du cluster
            cluster_info = {
                'cluster_id': i,
                'performance_metrics': cluster.performance_metrics.copy(),
                'shared_memory_size': len(cluster.shared_memory),
                'status': 'active'
            }

            # Ajouter informations de spécialisation si disponibles
            if hasattr(cluster.config, 'cluster_specialization'):
                cluster_info['specialization'] = cluster.config.cluster_specialization
            else:
                cluster_info['specialization'] = {
                    'name': 'STANDARD',
                    'description': 'Configuration standard sans spécialisation',
                    'focus_strategy': 'balanced_default'
                }

            cluster_status.append(cluster_info)

        # Résumé des spécialisations
        specialization_summary = {}
        if self.optimizer:
            specialization_summary = self.optimizer.get_optimization_summary()

        return {
            'system_metrics': self.system_metrics.copy(),
            'global_shared_memory': self.global_shared_memory.copy(),
            'cluster_status': cluster_status,
            'specialization_summary': specialization_summary,
            'architecture': {
                'num_clusters': self.num_clusters,
                'rollouts_per_cluster': self.config.three_value,
                'total_rollouts': self.num_clusters * self.config.three_value,
                'communication_model': 'Master-Worker + Message Passing',
                'synchronization': 'Barriers between phases',
                'optimization_enabled': self.optimizer is not None
            }
        }
MÉTHODE : _calculate_correlation_stability
LIGNE DÉBUT : 7707
SIGNATURE : def _calculate_correlation_stability(self, phase_strengths: Dict) -> float:
================================================================================

    def _calculate_correlation_stability(self, phase_strengths: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_correlation_stability.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la stabilité des corrélations entre phases"""

        values = list(phase_strengths.values())
        if len(values) < 2:
            return 0.0

        # Stabilité = inverse de la variance (plus stable = moins de variance)
        variance = self._calculate_variance(values)
        stability = 1.0 / (1.0 + variance)  # Normalisation

        return stability


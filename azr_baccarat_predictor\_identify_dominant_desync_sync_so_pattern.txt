# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 8587 à 8641
# Type: Méthode de la classe AZRCluster

    def _identify_dominant_desync_sync_so_pattern(self, sync_s_count: int, sync_o_count: int,
                                                desync_s_count: int, desync_o_count: int) -> str:
        """
        Identifie le pattern dominant DESYNC/SYNC → S/O

        IMPORTANT: Analyse seulement S/O, pas les TIE
        Les TIE sont exclus car non prédictifs pour les stratégies

        Args:
            sync_s_count: Nombre de SYNC → S
            sync_o_count: Nombre de SYNC → O
            desync_s_count: Nombre de DESYNC → S
            desync_o_count: Nombre de DESYNC → O

        Returns:
            Pattern dominant sous forme de string
        """

        # Calcul des ratios pour S/O uniquement
        total_sync_so = sync_s_count + sync_o_count      # Seulement S et O
        total_desync_so = desync_s_count + desync_o_count # Seulement S et O

        if total_sync_so == 0 and total_desync_so == 0:
            return 'NO_SO_DATA'

        # Ratios SYNC → S/O (TIE exclus)
        sync_s_ratio = sync_s_count / max(1, total_sync_so) if total_sync_so > 0 else 0
        sync_o_ratio = sync_o_count / max(1, total_sync_so) if total_sync_so > 0 else 0

        # Ratios DESYNC → S/O (TIE exclus)
        desync_s_ratio = desync_s_count / max(1, total_desync_so) if total_desync_so > 0 else 0
        desync_o_ratio = desync_o_count / max(1, total_desync_so) if total_desync_so > 0 else 0

        # Patterns S/O seulement
        so_patterns = [
            ('SYNC_TO_S', abs(sync_s_ratio - 0.5), sync_s_ratio, total_sync_so),
            ('SYNC_TO_O', abs(sync_o_ratio - 0.5), sync_o_ratio, total_sync_so),
            ('DESYNC_TO_S', abs(desync_s_ratio - 0.5), desync_s_ratio, total_desync_so),
            ('DESYNC_TO_O', abs(desync_o_ratio - 0.5), desync_o_ratio, total_desync_so)
        ]

        # Filtrer échantillons S/O suffisants (minimum 3 conversions S/O)
        valid_so_patterns = [(name, strength, ratio, sample) for name, strength, ratio, sample in so_patterns if sample >= 3]

        if not valid_so_patterns:
            return 'INSUFFICIENT_SO_DATA'

        # Pattern S/O le plus fort
        dominant_so = max(valid_so_patterns, key=lambda x: x[1])

        # Seuil S/O significatif (60% vers S ou O)
        if dominant_so[2] < 0.6:
            return 'NO_DOMINANT_SO_PATTERN'

        return dominant_so[0]
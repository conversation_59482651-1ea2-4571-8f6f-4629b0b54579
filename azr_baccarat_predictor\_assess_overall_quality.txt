# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10715 à 10730
# Type: Méthode de la classe AZRCluster

    def _assess_overall_quality(self, consistency: float, sample_adequacy: float,
                              statistical_significance: float, pattern_stability: float) -> str:
        """Évalue la qualité globale de l'analyse"""

        overall_score = (consistency + sample_adequacy + statistical_significance + pattern_stability) / 4

        if overall_score >= 0.8:
            return 'EXCELLENT'
        elif overall_score >= 0.6:
            return 'GOOD'
        elif overall_score >= 0.4:
            return 'FAIR'
        elif overall_score >= 0.2:
            return 'POOR'
        else:
            return 'VERY_POOR'
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2060 à 2083
# Type: Méthode de la classe AZRConfig

    def rollout2_rewards(self):
        return {
            # Zone de développement proximal (<PERSON><PERSON><PERSON><PERSON> + <PERSON>ZR)
            'optimal_difficulty': self.rollout2_optimal_difficulty,
            'min_difficulty': self.rollout2_min_difficulty,
            'max_difficulty': self.rollout2_max_difficulty,

            # Diversité (seuil AZR)
            'diversity_threshold': self.rollout2_diversity_threshold,
            'diversity_bonus': self.rollout2_diversity_bonus,
            'diversity_malus': self.rollout2_diversity_malus,

            # Qualité des séquences
            'excellence_threshold': self.rollout2_excellence_threshold,    # Bonus +15%
            'acceptable_threshold': self.rollout2_acceptable_threshold,    # Neutre
            'weak_threshold': self.rollout2_weak_threshold,                # Malus -15%
            'poor_threshold': self.rollout2_poor_threshold,                # Malus -30%

            # Multiplicateurs
            'excellence_bonus': self.rollout2_excellence_bonus,            # +15%
            'neutral_multiplier': self.rollout2_neutral_multiplier,        # Neutre
            'weak_malus': self.rollout2_weak_malus,                       # -15%
            'poor_malus': self.rollout2_poor_malus                        # -30%
        }
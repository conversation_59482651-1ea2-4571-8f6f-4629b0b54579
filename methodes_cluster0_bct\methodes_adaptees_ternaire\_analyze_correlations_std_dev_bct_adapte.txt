MÉTHODE : _analyze_correlations_std_dev
LIGNE DÉBUT : 10768
SIGNATURE : def _analyze_correlations_std_dev(self, pair_impair_seq: List[str],
================================================================================

    def _analyze_correlations_std_dev(self, pair_impair_seq: List[str],
                                    sync_desync_seq: List[str],
                                    combined_seq: List[str],
                                    so_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _analyze_correlations_std_dev.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Analyse corrélations avec approche anti-moyennes (écart-type)"""
        import statistics

        correlations = {}

        # Analyse corrélations PAIR/IMPAIR → S/O
        if len(so_seq) > 2:
            pair_s_count = sum(1 for p, s in zip(pair_impair_seq[:len(so_seq)], so_seq) if p == ['pair_4', 'pair_6'] and s == 'S')
            impair_s_count = sum(1 for p, s in zip(pair_impair_seq[:len(so_seq)], so_seq) if p == 'impair_5' and s == 'S')

            correlations['pair_impair'] = {
                'pair_s_ratio': pair_s_count / max(pair_impair_seq[:len(so_seq)].count(['pair_4', 'pair_6']), 1),
                'impair_s_ratio': impair_s_count / max(pair_impair_seq[:len(so_seq)].count('impair_5'), 1),
                'std_dev': statistics.stdev([pair_s_count, impair_s_count]) if len(so_seq) > 1 else 1.0
            }

        # Analyse corrélations index combiné → S/O (priorité découvertes)
        if len(so_seq) > 2 and len(combined_seq) >= len(so_seq):
            impair_sync_s = sum(1 for c, s in zip(combined_seq[:len(so_seq)], so_seq) if c == 'IMpair_4_sync, pair_6_sync' and s == 'S')
            pair_sync_o = sum(1 for c, s in zip(combined_seq[:len(so_seq)], so_seq) if c == 'pair_4_sync, pair_6_sync' and s == 'O')

            correlations['combined'] = {
                'impair_sync_s_ratio': impair_sync_s / max(combined_seq[:len(so_seq)].count('IMpair_4_sync, pair_6_sync'), 1),
                'pair_sync_o_ratio': pair_sync_o / max(combined_seq[:len(so_seq)].count('pair_4_sync, pair_6_sync'), 1),
                'std_dev': statistics.stdev([impair_sync_s, pair_sync_o]) if len(so_seq) > 1 else 1.0,
                'dominant_pattern': 'IMpair_4_sync, pair_6_sync→S' if impair_sync_s > pair_sync_o else 'pair_4_sync, pair_6_sync→O'
            }

        return correlations


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4120 à 4158
# Type: Méthode de la classe AZRCluster

    def _correlate_impair_with_pb(self, isolated_impairs: List, consecutive_sequences: List, pb_outcomes: List, total_hands: int) -> Dict:
        """Corrèle les IMPAIRS avec les résultats P/B"""
        correlation = {
            'deviation_strength': 0.0,
            'impair_p_count': 0,
            'impair_b_count': 0,
            'dominant_outcome': 'P'
        }

        total_correlations = 0
        p_count = 0

        # IMPAIRS isolés
        for pos in isolated_impairs:
            if pos - int(self.config.rollout_analyzer_position_offset) < len(pb_outcomes):
                if pb_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'P':
                    p_count += int(self.config.rollout_reward_valid_sequence_increment)
                total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        # Séquences d'IMPAIRS
        for seq in consecutive_sequences:
            for pos in seq:
                if pos - int(self.config.rollout_analyzer_position_offset) < len(pb_outcomes):
                    if pb_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'P':
                        p_count += int(self.config.rollout_reward_valid_sequence_increment)
                    total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        if total_correlations > 0:
            p_ratio = p_count / total_correlations
            correlation['deviation_strength'] = abs(p_ratio - self.config.rollout_analyzer_normality_threshold)
            correlation['impair_p_count'] = p_count
            correlation['impair_b_count'] = total_correlations - p_count

            if p_ratio > self.config.rollout_analyzer_normality_threshold:
                correlation['dominant_outcome'] = 'P'
            else:
                correlation['dominant_outcome'] = 'B'

        return correlation
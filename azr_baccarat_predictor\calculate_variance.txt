# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2219 à 2237
# Type: Méthode de la classe UtilitairesMathematiquesAZR

    def calculate_variance(values: List[float]) -> float:
        """
        Calcule la variance d'une liste de valeurs

        Args:
            values: Liste de valeurs numériques

        Returns:
            float: Variance des valeurs
        """
        if not values:
            config = AZRConfig()
            return config.default_variance_value

        try:
            return float(np.var(values))
        except:
            config = AZRConfig()
            return config.default_variance_value
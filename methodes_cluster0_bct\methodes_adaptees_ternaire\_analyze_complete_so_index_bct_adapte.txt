MÉTHODE : _analyze_complete_so_index
LIGNE DÉBUT : 5394
SIGNATURE : def _analyze_complete_so_index(self, hands_data: List) -> Dict:
================================================================================

    def _analyze_complete_so_index(self, hands_data: List) -> Dict:
"""
    ADAPTATION BCT - _analyze_complete_so_index.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Analyse complète INDEX 5 : S/O (SAME/OPPOSITE)

        LOGIQUE EXACTE :
        - Calcule S/O uniquement sur transitions P/B → P/B
        - EXCLUT complètement les Ties du calcul S/O
        - Analyse les patterns de répétition vs changement sur P/B uniquement
        """
        so_analysis = {
            'so_sequence': [],             # ['S','O','S','O',...]
            'so_frequencies': {},          # Fréquences S vs O
            'so_consecutive': {},          # Séquences consécutives S ou O
            'so_patterns': {},             # Patterns récurrents
            'so_correlation_pbt': {},      # Corrélation S/O avec P/B/T
            'so_transitions': {}           # Analyse transitions S→O, O→S
        }

        # Calcul séquence S/O (FOCUS P/B UNIQUEMENT - exclure Ties)
        pbt_sequence = [hand.pbt_result for hand in hands_data]

        # Filtrer seulement P/B pour le calcul S/O (exclure Ties)
        pb_only_sequence = [outcome for outcome in pbt_sequence if outcome in ['P', 'B']]

        # Calcul S/O sur transitions P/B uniquement
        for i in range(1, len(pb_only_sequence)):
            current_outcome = pb_only_sequence[i]
            previous_outcome = pb_only_sequence[i-1]

            if current_outcome == previous_outcome:
                so_status = 'S'  # Same
            else:
                so_status = 'O'  # Opposite

            so_analysis['so_sequence'].append(so_status)

        # Fréquences S/O
        if so_analysis['so_sequence']:
            total_so = len(so_analysis['so_sequence'])
            so_analysis['so_frequencies'] = {
                'S': so_analysis['so_sequence'].count('S') / total_so,
                'O': so_analysis['so_sequence'].count('O') / total_so,
                'total_so_hands': total_so
            }

            # Séquences consécutives
            for so_type in ['S', 'O']:
                consecutive = self._find_consecutive_sequences(so_analysis['so_sequence'], so_type)
                so_analysis['so_consecutive'][so_type] = {
                    'max_length': max(consecutive) if consecutive else 0,
                    'average_length': sum(consecutive) / len(consecutive) if consecutive else 0,
                    'frequency': len(consecutive)
                }

        return so_analysis


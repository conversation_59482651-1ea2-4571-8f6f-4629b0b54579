MÉTHODE : _calculate_global_strength_metrics
LIGNE DÉBUT : 3121
SIGNATURE : def _calculate_global_strength_metrics(self, all_indices: Dict) -> Dict:
================================================================================

    def _calculate_global_strength_metrics(self, all_indices: Dict) -> Dict:
"""
    ADAPTATION BCT - _calculate_global_strength_metrics.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule les métriques de force globale de l'analyse

        FOCUS : Évaluation de la qualité et fiabilité globale
        """
        global_metrics = {
            'overall_analysis_strength': 0.0,
            'index_reliability_scores': {},
            'prediction_confidence_factors': {}
        }

        # Évaluer la fiabilité de chaque index
        reliability_scores = {}

        # Index IMPAIR/PAIR
        impair_pair = all_indices.get('impair_pair', {})
        correlations = impair_pair.get('correlations', {})
        impair_hands = correlations.get('impair_pb_hands', 0)
        pair_hands = correlations.get('pair_pb_hands', 0)

        if impair_hands + pair_hands > 0:
            sample_adequacy = min(1.0, (impair_hands + pair_hands) / 20.0)
            correlation_strength = abs(correlations.get('impair_to_player', 0.5) - 0.5) * 2
            reliability_scores['impair_pair'] = (sample_adequacy + correlation_strength) / 2
        else:
            reliability_scores['impair_pair'] = 0.0

        # Index COMBINÉ
        combined = all_indices.get('combined', {})
        pattern_frequencies = combined.get('pattern_frequencies', {})

        if pattern_frequencies:
            # Évaluer la diversité et la force des patterns
            total_patterns = sum(pattern_frequencies.values())
            if total_patterns > 0:
                pattern_diversity = len(pattern_frequencies) / 4.0  # 4 états possibles
                max_frequency = max(pattern_frequencies.values()) / total_patterns
                pattern_strength = max_frequency if max_frequency > self.config.priority_threshold_significant else self.config.confidence_medium_threshold
                reliability_scores['combined'] = (pattern_diversity + pattern_strength) / 2
            else:
                reliability_scores['combined'] = 0.0
        else:
            reliability_scores['combined'] = 0.0

        # Index S/O
        so = all_indices.get('so', {})
        so_frequencies = so.get('so_frequencies', {})

        if so_frequencies:
            total_so = sum(so_frequencies.values())
            if total_so > 0:
                s_ratio = so_frequencies.get('S', 0) / total_so
                so_strength = abs(s_ratio - 0.5) * 2  # Force de la déviation de 50/50
                sample_adequacy = min(1.0, total_so / 15.0)
                reliability_scores['so'] = (so_strength + sample_adequacy) / 2
            else:
                reliability_scores['so'] = 0.0
        else:
            reliability_scores['so'] = 0.0

        global_metrics['index_reliability_scores'] = reliability_scores

        # Calcul de la force globale (moyenne pondérée)
        if reliability_scores:
            # Pondération : COMBINÉ (40%), IMPAIR/PAIR (35%), S/O (25%)
            weights = {'combined': 0.4, 'impair_pair': 0.35, 'so': 0.25}
            weighted_sum = sum(reliability_scores.get(index, 0) * weight
                             for index, weight in weights.items())
            global_metrics['overall_analysis_strength'] = weighted_sum

        # Facteurs de confiance pour prédictions
        global_metrics['prediction_confidence_factors'] = {
            'pb_prediction_confidence': reliability_scores.get('impair_pair', 0.5),
            'so_prediction_confidence': reliability_scores.get('combined', 0.5),
            'overall_prediction_confidence': global_metrics['overall_analysis_strength']
        }

        return global_metrics


MÉTHODE : _calculate_rollout_consensus
LIGNE DÉBUT : 4013
SIGNATURE : def _calculate_rollout_consensus(self, best_sequence: Dict, analyzer_report: Dict) -> float:
================================================================================

    def _calculate_rollout_consensus(self, best_sequence: Dict, analyzer_report: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_rollout_consensus.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule le niveau de consensus entre les rollouts

        Returns:
            float: Score de consensus (0-1)
        """
        consensus_factors = []

        # 1. Consensus entre analyse et génération
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', self.config.probability_neutral)

        sequence_probability = best_sequence.get('estimated_probability', self.config.probability_neutral)

        # Accord entre qualité d'analyse et probabilité de séquence
        analysis_generation_agreement = self.config.one_value - abs(analysis_quality - sequence_probability)
        consensus_factors.append(analysis_generation_agreement)

        # 2. Consensus des signaux
        signals_summary = analyzer_report.get('signals_summary', {})
        overall_confidence = signals_summary.get('overall_confidence', self.config.probability_neutral)

        # Cohérence des signaux avec la sélection
        evaluation = best_sequence.get('evaluation', {})
        signal_alignment = evaluation.get('signal_alignment_score', self.config.probability_neutral)

        signal_consensus = (overall_confidence + signal_alignment) / self.config.two_value
        consensus_factors.append(signal_consensus)

        # 3. Consensus des métriques d'évaluation
        evaluation_scores = [
            evaluation.get('signal_alignment_score', self.config.probability_neutral),
            evaluation.get('consistency_score', self.config.probability_neutral),
            evaluation.get('logic_validation_score', self.config.probability_neutral)
        ]

        # Cohérence des scores d'évaluation
        import numpy as np
        mean_score = np.mean(evaluation_scores)
        score_consistency = self.config.one_value - (np.std(evaluation_scores) / max(self.config.variance_threshold_minimum, mean_score))
        score_consistency = max(self.config.zero_value, min(self.config.one_value, score_consistency))
        consensus_factors.append(score_consistency)

        # Score de consensus composite
        consensus_score = sum(consensus_factors) / len(consensus_factors)

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, consensus_score))


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17835 à 17877
# Type: Méthode de la classe AZRBaccaratPredictor

    def _demonstrate_anti_blindness_system(self, priority_distribution: Dict) -> None:
        """
        Démontre l'efficacité du système anti-aveuglement avec exemples concrets

        AVANT vs APRÈS les corrections d'équilibrage
        """
        logger.info("🔍 DÉMONSTRATION SYSTÈME ANTI-AVEUGLEMENT")

        # Extraire les forces des signaux
        impair_strength = priority_distribution.get('priority_1_impair', {}).get('strength', 0.0)
        pair_strength = priority_distribution.get('priority_2_pair', {}).get('strength', 0.0)
        sync_strength = priority_distribution.get('priority_3_sync', {}).get('strength', 0.0)
        combined_strength = priority_distribution.get('priority_4_combined', {}).get('strength', 0.0)

        # Calcul ANCIEN système (80%-10%-5%-5%)
        old_contribution = (impair_strength * self.config.rollout2_base_confidence_high + pair_strength * self.config.rollout2_adjustment_small +
                           sync_strength * self.config.signal_strength_threshold + combined_strength * self.config.signal_strength_threshold)

        # Calcul NOUVEAU système (40%-30%-20%-10%)
        new_contribution = (impair_strength * self.config.rollout3_quality_bonus_medium + pair_strength * self.config.rollout3_quality_bonus_small +
                           sync_strength * self.config.rollout2_adjustment_large + combined_strength * self.config.rollout2_adjustment_small)

        logger.info(f"📊 COMPARAISON SYSTÈMES :")
        logger.info(f"   IMPAIRS: {impair_strength:.1%} | PAIRS: {pair_strength:.1%} | "
                   f"SYNC: {sync_strength:.1%} | COMBINÉ: {combined_strength:.1%}")
        logger.info(f"   ANCIEN (80-10-5-5): {old_contribution:.1%}")
        logger.info(f"   NOUVEAU (40-30-20-10): {new_contribution:.1%}")

        # Identifier les cas d'aveuglement évités
        if sync_strength > self.config.veto_sync_strength_threshold and impair_strength < self.config.veto_sync_impair_weakness:
            improvement = (sync_strength * self.config.rollout2_adjustment_large) - (sync_strength * self.config.signal_strength_threshold)
            logger.info(f"🚨 AVEUGLEMENT ÉVITÉ : Signal SYNC fort ({sync_strength:.1%}) "
                       f"maintenant valorisé +{improvement:.1%}")

        if combined_strength > self.config.veto_combined_strength_threshold and impair_strength < self.config.veto_combined_impair_weakness:
            improvement = (combined_strength * self.config.rollout2_adjustment_small) - (combined_strength * self.config.signal_strength_threshold)
            logger.info(f"🚨 AVEUGLEMENT ÉVITÉ : Signal COMBINÉ ultra-rare ({combined_strength:.1%}) "
                       f"maintenant valorisé +{improvement:.1%}")

        if pair_strength > self.config.veto_pair_strength_threshold and impair_strength < self.config.veto_pair_impair_weakness:
            improvement = (pair_strength * self.config.rollout3_quality_bonus_small) - (pair_strength * self.config.rollout2_adjustment_small)
            logger.info(f"🚨 AVEUGLEMENT ÉVITÉ : Signal PAIRS fort ({pair_strength:.1%}) "
                       f"maintenant valorisé +{improvement:.1%}")
MÉTHODE : _analyze_correlation_trend
LIGNE DÉBUT : 7672
SIGNATURE : def _analyze_correlation_trend(self, correlation_values: List[float], trend_name: str) -> Dict:
================================================================================

    def _analyze_correlation_trend(self, correlation_values: List[float], trend_name: str) -> Dict:
"""
    ADAPTATION BCT - _analyze_correlation_trend.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Analyse la tendance d'évolution d'une corrélation"""

        if len(correlation_values) < 3:
            return {'trend_type': 'INSUFFICIENT_DATA', 'trend_strength': 0.0}

        early, mid, late = correlation_values

        # Analyser type de tendance
        if late > mid > early:
            trend_type = 'STRENGTHENING'
            trend_strength = late - early
        elif early > mid > late:
            trend_type = 'WEAKENING'
            trend_strength = early - late
        elif mid > early and mid > late:
            trend_type = 'PEAK_THEN_DECLINE'
            trend_strength = mid - min(early, late)
        elif mid < early and mid < late:
            trend_type = 'DIP_THEN_RECOVER'
            trend_strength = max(early, late) - mid
        else:
            trend_type = 'STABLE'
            trend_strength = max(correlation_values) - min(correlation_values)

        return {
            'trend_type': trend_type,
            'trend_strength': trend_strength,
            'early_value': early,
            'mid_value': mid,
            'late_value': late,
            'best_phase': 'early' if early == max(correlation_values) else
                         ('mid' if mid == max(correlation_values) else 'late')
        }


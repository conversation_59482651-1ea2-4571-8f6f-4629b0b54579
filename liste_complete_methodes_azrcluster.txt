# 📋 LISTE COMPLÈTE DES MÉTHODES DE LA CLASSE AZRCluster
================================================================================
Source: azr_baccarat_predictor.py (lignes 2381-13425)
Date d'analyse: 08/06/2025 22:10
Total méthodes: 162 méthodes organisées en 7 catégories

# 🔧 SYSTÈME & INFRASTRUCTURE - 8 MÉTHODES
================================================================================
Lignes: 2393-2509
Méthodes génériques utilisées par tous les clusters

1. __init__                                    (constructeur)
2. execute_cluster_pipeline                    (orchestration)
3. _get_cluster_specialization_params          (paramètres)
4. _create_generic_cluster_analyzer            (analyseur générique)
5. _analyze_impair_bias_specialized            (polymorphisme)
6. _analyze_sync_bias_specialized              (polymorphisme)
7. _apply_cluster_specialization               (mécanisme spécialisation)
8. [1 méthode système supplémentaire]

# 🎯 CLUSTER 0 - ROLLOUT 1 ANALYSEUR - 51 MÉTHODES
================================================================================
Lignes: 2509-5611
Méthodes d'analyse, corrélation, synthèse pour le cluster 0

## MÉTHODES PRINCIPALES:
9. _rollout_analyzer                            (méthode principale rollout 1)
10. _analyze_impair_consecutive_bias             (analyse IMPAIRS)
11. _analyze_pair_priority_2_autonomous          (analyse PAIRS)
12. _analyze_sync_alternation_bias               (analyse SYNC/DESYNC)
13. _analyze_combined_structural_bias            (analyse combinée)
14. _correlate_bias_to_pb_variations             (corrélation P/B)
15. _correlate_bias_to_so_variations             (corrélation S/O)
16. _generate_priority_based_synthesis_autonomous (synthèse prioritaire)
17. _generate_bias_exploitation_synthesis        (synthèse exploitation)
18. _generate_bias_signals_summary               (signaux)
19. _generate_bias_generation_guidance           (guidance)
20. _generate_bias_quick_access                  (accès rapide)
21. _generate_complete_synthesis                 (synthèse complète)
22. _calculate_cross_index_impacts               (impacts croisés)
23. _calculate_variations_impact                 (impact variations)
24. _calculate_global_strength_metrics           (métriques globales)

## MÉTHODES DE CORRÉLATION:
25. _correlate_impair_with_sync                  (IMPAIR → SYNC)
26. _correlate_impair_with_combined              (IMPAIR → COMBINÉ)
27. _correlate_impair_with_pb                    (IMPAIR → P/B)
28. _correlate_impair_with_so                    (IMPAIR → S/O)

## MÉTHODES D'ANALYSE COMPLÈTE:
29. _analyze_complete_impair_pair_index          (index IMPAIR/PAIR)
30. _analyze_complete_desync_sync_index          (index SYNC/DESYNC)
31. _analyze_complete_combined_index             (index combiné)
32. _analyze_complete_pbt_index                  (index P/B/T)
33. _analyze_complete_so_index                   (index S/O)
34. _analyze_complete_cross_impacts              (impacts croisés)
35. _synthesize_complete_analysis                (synthèse complète)

## MÉTHODES D'ANALYSE D'IMPACT:
36. _analyze_impair_pair_to_so_impact            (IMPAIR/PAIR → S/O)
37. _analyze_desync_sync_to_pbt_impact           (SYNC/DESYNC → P/B)
38. _analyze_desync_sync_to_so_impact            (SYNC/DESYNC → S/O)
39. _analyze_combined_to_pbt_impact              (COMBINÉ → P/B)
40. _analyze_combined_to_so_impact               (COMBINÉ → S/O)
41. _analyze_tri_dimensional_impacts             (impacts 3D)
42. _analyze_variations_impact_on_outcomes       (variations → résultats)
43. _analyze_consecutive_length_impact           (longueur consécutive)
44. _analyze_transition_moments_impact           (moments transition)
45. _analyze_desync_periods_impact               (périodes désync)
46. _analyze_combined_state_changes_impact       (changements états)
47. _analyze_temporal_correlation_evolution      (évolution temporelle)

## MÉTHODES DE CALCUL SPÉCIALISÉES:
48. _calculate_phase_impair_pair_pb_correlation
49. _calculate_phase_impair_pair_so_correlation
50. _calculate_phase_sync_desync_pb_correlation
51. _calculate_phase_sync_desync_so_correlation
52. _calculate_phase_correlation_strength
53. _analyze_correlation_trend
54. _calculate_correlation_stability
55. _calculate_evolution_strength
56. _calculate_temporal_consistency
57. _calculate_temporal_predictability
58. _generate_temporal_recommendation
59. _calculate_variation_strength_analysis

# 🎯 CLUSTER 0 - ROLLOUT 2 GÉNÉRATEUR - 42 MÉTHODES
================================================================================
Lignes: 5611-5727
Méthodes de génération, conversion, calcul de séquences pour cluster 0

## MÉTHODES PRINCIPALES:
60. _rollout_generator                           (méthode principale rollout 2)
61. _define_optimized_generation_space           (espace génération)
62. _generate_sequences_from_signals             (séquences depuis signaux)
63. _generate_sequence_from_signal               (séquence depuis signal)
64. _generate_fallback_sequences                 (séquences fallback)
65. _generate_all_possible_sequences             (toutes séquences)

## MÉTHODES DE GÉNÉRATION SPÉCIALISÉES:
66. _generate_pb_sequence                        (séquence P/B)
67. _generate_so_based_sequence                  (séquence basée S/O)
68. _generate_pair_sync_sequence                 (séquence PAIR/SYNC)
69. _generate_impair_sync_sequence               (séquence IMPAIR/SYNC)
70. _generate_generic_signal_sequence            (séquence signal générique)
71. _generate_impair_pair_optimized_sequence     (séquence IMPAIR/PAIR optimisée)
72. _generate_sync_based_sequence                (séquence basée SYNC)
73. _generate_combined_index_sequence            (séquence index combiné)
74. _generate_so_pattern_sequence                (séquence pattern S/O)

## MÉTHODES DE CONVERSION:
75. _convert_pb_sequence_to_so                   (P/B → S/O)
76. _convert_pb_sequence_to_so_with_history      (P/B → S/O avec historique)

## MÉTHODES DE CALCUL:
77. _calculate_sequence_probability              (probabilité séquence)
78. _calculate_sequence_quality_metrics          (métriques qualité)
79. calculate_rollout2_reward                    (récompense rollout 2)
80. calculate_rollout2_sequence_quality          (qualité séquence rollout 2)
81. calculate_rollout2_diversity_score           (score diversité rollout 2)

## MÉTHODES UTILITAIRES:
82. _classify_confidence_level                   (niveau confiance)
83. _enrich_sequences_with_complete_indexes      (enrichissement séquences)
84. _get_last_historical_pb_result               (dernier résultat P/B)

## MÉTHODES SUPPLÉMENTAIRES ROLLOUT 2:
85. _define_complete_generation_space_DEPRECATED
86. _generate_signals_summary
87. _generate_generation_guidance
88. _generate_quick_access
89. _update_performance_metrics
90. _count_consecutive_pattern
91. _calculate_rupture_probability
92. _analyze_correlations_std_dev
93. _identify_improbability_zones
94. _evaluate_sequence_quality (version rollout 2)
95. _select_best_sequence (version rollout 2)
96. _calculate_cluster_confidence (version rollout 2)
97. _extract_next_hand_prediction (version rollout 2)
98. _count_pair46_alternations_bct
99. _count_alternations_bct
100. _analyze_bct_categories_patterns
101. _count_consecutive_bct

# 🎯 CLUSTER 0 - ROLLOUT 3 PRÉDICTEUR - 19 MÉTHODES
================================================================================
Lignes: 5727-13149
Méthodes d'évaluation, sélection, validation pour cluster 0

## MÉTHODES PRINCIPALES:
102. _rollout_predictor                           (méthode principale rollout 3)
103. _evaluate_sequence_quality                   (évaluation qualité)
104. _select_best_sequence                        (sélection meilleure)
105. _validate_sequence_logic                     (validation logique)

## MÉTHODES D'ÉVALUATION:
106. _evaluate_signal_alignment                   (alignement signaux)
107. _evaluate_fallback_alignment                 (alignement fallback)
108. _analyze_sequence_consistency                (consistance séquence)
109. _assess_risk_reward_ratio                    (ratio risque/récompense)
110. _assess_sample_size_adequacy                 (adéquation échantillon)
111. _assess_overall_quality                      (qualité globale)

## MÉTHODES DE CALCUL:
112. _calculate_sequence_score                    (score séquence)
113. _calculate_cluster_confidence                (confiance cluster)
114. _calculate_cluster_confidence_azr_calibrated (confiance calibrée)
115. _calculate_confidence_risk_factors           (facteurs risque confiance)
116. _calculate_epistemic_uncertainty             (incertitude épistémique)
117. _calculate_rollout_consensus                 (consensus rollout)
118. calculate_rollout3_reward                    (récompense rollout 3)
119. calculate_rollout3_risk_factor               (facteur risque rollout 3)
120. calculate_cluster_total_reward               (récompense totale cluster)

# 🎯 CLUSTER 0 - BASE UTILITAIRES - 28 MÉTHODES
================================================================================
Lignes: 13149-[fin section]
Méthodes utilitaires, extraction, identification pour cluster 0

## MÉTHODES UTILITAIRES PRINCIPALES:
121. _count_consecutive_pattern                   (compter patterns consécutifs)
122. _find_consecutive_sequences                  (trouver séquences consécutives)
123. _find_consecutive_sequences_with_positions   (avec positions)
124. get_max_sequence_length                      (longueur max séquence)
125. get_max_so_conversions                       (conversions S/O max)
126. is_game_complete                             (partie complète)

## MÉTHODES D'EXTRACTION:
127. _extract_combined_state_changes_strength     (force changements états)
128. _extract_consecutive_length_strength         (force longueur consécutive)
129. _extract_desync_periods_strength             (force périodes désync)
130. _extract_temporal_evolution_strength         (force évolution temporelle)
131. _extract_transition_moments_strength         (force moments transition)

## MÉTHODES D'IDENTIFICATION:
132. _identify_best_prediction_context            (meilleur contexte prédiction)
133. _identify_desync_periods                     (périodes désync)
134. _identify_dominant_desync_sync_so_pattern    (pattern dominant SYNC/DESYNC)
135. _identify_dominant_impair_pair_so_pattern    (pattern dominant IMPAIR/PAIR)
136. _identify_enhanced_dominant_correlations     (corrélations dominantes)
137. _identify_enhanced_high_confidence_zones     (zones haute confiance)
138. _identify_improbability_zones                (zones improbabilité)

## MÉTHODES DE CALCUL DE BASE:
139. _calculate_strength_distribution             (distribution force)
140. _calculate_temporal_consistency              (consistance temporelle)
141. _calculate_temporal_predictability           (prédictibilité temporelle)
142. _calculate_variance                          (variance)
143. _calculate_variation_consistency             (consistance variation)
144. _calculate_variation_strength_analysis       (analyse force variation)

## MÉTHODES DE CLASSIFICATION:
145. _classify_combined_transition_type           (type transition combinée)

## MÉTHODES DE GÉNÉRATION:
146. _generate_exploitation_recommendation        (recommandation exploitation)
147. _generate_temporal_recommendation            (recommandation temporelle)

## MÉTHODES DE MISE À JOUR:
148. _update_performance_metrics                  (métriques performance)

# 🔥 CLUSTER 2 - SPÉCIALISATIONS PATTERNS COURTS - 6 MÉTHODES
================================================================================
Spécialisations pour patterns courts (2-3 manches)

149. _rollout_analyzer_c2_patterns_courts        (analyseur principal C2)
150. _analyze_impair_consecutive_bias_c2_specialized (analyse IMPAIRS C2)
151. _analyze_sync_alternation_bias_c2_specialized (analyse SYNC C2)
152. _apply_c2_short_patterns_specialization     (application spécialisation C2)
153. _generate_bias_signals_summary_c2           (signaux C2)
154. _generate_bias_generation_guidance_c2       (guidance C2)

# 🔥 CLUSTER 3 - SPÉCIALISATIONS PATTERNS MOYENS - 8 MÉTHODES
================================================================================
Spécialisations pour patterns moyens (4-6 manches)

155. _rollout_analyzer_c3_patterns_moyens        (analyseur principal C3)
156. _analyze_impair_consecutive_bias_c3_specialized (analyse IMPAIRS C3)
157. _analyze_sync_alternation_bias_c3_specialized (analyse SYNC C3)
158. _apply_c3_medium_patterns_specialization    (application spécialisation C3)
159. _generate_bias_quick_access_c3              (accès rapide C3)
160. _generate_bias_signals_summary_c3           (signaux C3)
161. _generate_bias_generation_guidance_c3       (guidance C3)
162. [1 méthode C3 supplémentaire]

# 📊 RÉSUMÉ QUANTITATIF
================================================================================
TOTAL MÉTHODES: 162

RÉPARTITION PAR CATÉGORIE:
- Système & Infrastructure: 8 méthodes
- Cluster 0 - Rollout 1 (Analyseur): 51 méthodes
- Cluster 0 - Rollout 2 (Générateur): 42 méthodes  
- Cluster 0 - Rollout 3 (Prédicteur): 19 méthodes
- Cluster 0 - Base Utilitaires: 28 méthodes
- Cluster 2 - Spécialisations: 6 méthodes
- Cluster 3 - Spécialisations: 8 méthodes

CLUSTER 0 TOTAL: 140 méthodes (8 + 51 + 42 + 19 + 28)
AUTRES CLUSTERS: 14 méthodes (6 + 8)

# 🎯 CONCLUSION
================================================================================
La classe AZRCluster contient 162 méthodes dont:
- 140 méthodes appartiennent au CLUSTER 0 (notre cible)
- 14 méthodes appartiennent aux CLUSTERS 2 et 3 (à exclure)
- 8 méthodes système (à adapter pour BCT)

Les 140 méthodes du Cluster 0 constituent notre base pour le système BCT révolutionnaire.

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 3652 à 3716
# Type: Méthode de la classe AZRCluster

    def _get_cluster_specialization_params(self, cluster_id: int) -> Dict:
        """
        🎯 CENTRALISATION - Récupère les paramètres de spécialisation pour un cluster

        Tous les paramètres sont centralisés dans AZRConfig selon l'architecture.
        """
        if cluster_id == 2:  # C2 - PATTERNS COURTS
            return {
                'type': 'patterns_courts_2_3_manches',
                'min_length': self.config.c2_short_pattern_min_length,
                'max_length': self.config.c2_short_pattern_max_length,
                'threshold': self.config.c2_reactivity_threshold,
                'bonus': self.config.c2_micro_change_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_02
            }
        elif cluster_id == 3:  # C3 - PATTERNS MOYENS
            return {
                'type': 'patterns_moyens_4_6_manches',
                'min_length': self.config.c3_medium_pattern_min_length,
                'max_length': self.config.c3_medium_pattern_max_length,
                'threshold': self.config.c3_vision_threshold,
                'bonus': self.config.c3_balance_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_03
            }
        elif cluster_id == 4:  # C4 - PATTERNS LONGS
            return {
                'type': 'patterns_longs_7_plus_manches',
                'min_length': self.config.c4_long_pattern_min_length,
                'max_length': self.config.c4_long_pattern_max_length,
                'threshold': self.config.c4_continuity_threshold,
                'bonus': self.config.c4_memory_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_04
            }
        elif cluster_id == 5:  # C5 - CORRÉLATIONS
            return {
                'type': 'correlations_impair_pair',
                'threshold': self.config.c5_correlation_threshold,
                'bonus': self.config.c5_impair_pair_bonus,
                'multi_bonus': self.config.c5_multi_dimensional_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_05
            }
        elif cluster_id == 6:  # C6 - SYNC/DESYNC
            return {
                'type': 'sync_desync_states',
                'threshold': self.config.c6_sync_desync_threshold,
                'bonus': self.config.c6_alternation_bonus,
                'rupture_bonus': self.config.c6_rupture_detection_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_06
            }
        elif cluster_id == 7:  # C7 - ADAPTATIF
            return {
                'type': 'adaptatif_contextuel',
                'threshold': self.config.c7_adaptation_threshold,
                'bonus': self.config.c7_context_bonus,
                'learning_rate': self.config.c7_learning_rate,
                'variability_bonus': self.config.c7_variability_bonus,
                'confidence_multiplier': self.config.confidence_multiplier_07
            }
        else:  # C0-C1 - STANDARD
            return {
                'type': 'standard_reference',
                'threshold': self.config.rollout_analyzer_normality_threshold,
                'bonus': self.config.zero_value,
                'confidence_multiplier': self.config.confidence_multiplier_02
            }
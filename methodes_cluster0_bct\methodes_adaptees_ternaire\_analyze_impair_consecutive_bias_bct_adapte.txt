MÉTHODE : _analyze_impair5_consecutive_bias
LIGNE DÉBUT : 413
SIGNATURE : def _analyze_impair5_consecutive_bias(self, hands_data: List) -> Dict:
================================================================================

    def _analyze_impair5_consecutive_bias(self, hands_data: List) -> Dict:
"""
    ADAPTATION BCT - _analyze_impair_consecutive_bias.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        PRIORITÉ 1 : Analyse COMPLÈTE des IMPAIRS (isolés + séquences)

        NOUVELLE LOGIQUE SANS SEUILS LIMITANTS :
        - Analyse TOUS les IMPAIRS (même isolés)
        - Attention progressive selon longueur séquence
        - Corrèle avec TOUS les autres indices (2, 3, 4, 5)
        - Génère TOUJOURS des signaux exploitables
        """
        import statistics

        impair_bias = {
            'isolated_impairs': [],              # IMPAIRS isolés analysés
            'consecutive_impair_sequences': [],   # Séquences d'IMPAIRS
            'impair_attention_scores': [],       # Scores d'attention progressifs
            'pb_impact_after_impairs': {},       # Impact sur P/B
            'so_prediction_bias': {},            # Impact sur S/O
            'sync_correlation': {},              # Corrélation avec Index 2
            'combined_correlation': {},          # Corrélation avec Index 3
            'exploitation_confidence': self.config.zero_value,     # Toujours > 0
            'bias_persistence_score': self.config.zero_value,
            'priority_1_signals': []            # Signaux prioritaires
        }

        # ================================================================
        # PHASE 1 : ANALYSE COMPLÈTE DES IMPAIRS (ISOLÉS + SÉQUENCES)
        # ================================================================

        position_types = []
        pb_outcomes = []
        sync_states = []
        combined_states = []
        so_outcomes = []

        # Extraction complète de TOUS les indices
        for hand_number in range(int(self.config.one_value), len(hands_data) + int(self.config.one_value)):
            hand_data = hands_data[hand_number - int(self.config.rollout_analyzer_sequence_increment)]

            # Index 1 : PAIR/IMPAIR
            position_type = 'impair_5' if hand_number % int(self.config.two_value) == int(self.config.one_value) else ['pair_4', 'pair_6']
            position_types.append(position_type)

            # Index 4 : P/B/T
            pbt_result = hand_data.pbt_result
            if pbt_result in ['P', 'B']:
                pb_outcomes.append(pbt_result)

            # Index 2 : SYNC/DESYNC
            sync_state = getattr(hand_data, 'sync_state', 'SYNC')
            sync_states.append(sync_state)

            # Index 3 : COMBINED
            combined_state = getattr(hand_data, 'combined_state', f"{position_type}_{sync_state}")
            combined_states.append(combined_state)

            # Index 5 : S/O
            so_result = getattr(hand_data, 'so_conversion', None)
            if so_result in ['S', 'O']:
                so_outcomes.append(so_result)

        # ================================================================
        # ANALYSE PRIORITÉ 1 : IMPAIRS ISOLÉS ET SÉQUENCES
        # ================================================================

        # 1. Identifier TOUS les IMPAIRS (isolés + dans séquences)
        isolated_impairs = []
        consecutive_sequences = []
        current_sequence = []

        for i, pos_type in enumerate(position_types):
            if pos_type == 'impair_5':
                current_sequence.append(i + self.config.rollout_analyzer_sequence_increment)  # Position dans la partie
            else:
                if current_sequence:
                    if len(current_sequence) == self.config.one_value:
                        # IMPAIR isolé
                        isolated_impairs.append(current_sequence[0])
                    else:
                        # Séquence d'IMPAIRS
                        consecutive_sequences.append(current_sequence)
                    current_sequence = []

        # Fermer la dernière séquence
        if current_sequence:
            if len(current_sequence) == self.config.one_value:
                isolated_impairs.append(current_sequence[0])
            else:
                consecutive_sequences.append(current_sequence)

        impair_bias['isolated_impairs'] = isolated_impairs
        impair_bias['consecutive_impair_sequences'] = consecutive_sequences

        # ================================================================
        # CALCUL SCORES D'ATTENTION PROGRESSIFS (SANS SEUILS LIMITANTS)
        # ================================================================

        attention_scores = []

        # 1. Scores pour IMPAIRS isolés (attention de base)
        for isolated_pos in isolated_impairs:
            attention_score = {
                'type': 'isolated_impair',
                'position': isolated_pos,
                'attention_level': self.config.attention_level_base,  # Attention de base
                'rarity_factor': self.config.rarity_factor_high_str,  # IMPAIR = rare
                'signal_strength': self.config.rollout1_impair_consecutive_common   # Signal minimum garanti
            }
            attention_scores.append(attention_score)

        # 2. Scores pour séquences d'IMPAIRS (attention progressive)
        for seq in consecutive_sequences:
            seq_length = len(seq)
            # Attention croissante : 1.0 → 2.0 → 4.0 → 8.0...
            attention_level = min(self.config.attention_level_max, self.config.attention_power_base ** (seq_length - self.config.one_value))

            attention_score = {
                'type': 'consecutive_impairs',
                'positions': seq,
                'sequence_length': seq_length,
                'attention_level': attention_level,
                'rarity_factor': self.config.rarity_factor_ultra_high_str if seq_length >= self.config.length_threshold_3 else self.config.rarity_factor_very_high_str,
                'signal_strength': min(self.config.one_value, self.config.quality_threshold_30_percent + (seq_length - self.config.one_value) * self.config.multiplier_increment_02)
            }
            attention_scores.append(attention_score)

        impair_bias['impair_attention_scores'] = attention_scores

        # ================================================================
        # CORRÉLATION AVEC TOUS LES AUTRES INDICES
        # ================================================================

        # Corrélation Index 1 (IMPAIR) → Index 2 (SYNC/DESYNC)
        sync_correlation = self._correlate_impair5_with_sync(
            isolated_impairs, consecutive_sequences, sync_states
        )
        impair_bias['sync_correlation'] = sync_correlation

        # Corrélation Index 1 (IMPAIR) → Index 3 (COMBINED)
        combined_correlation = self._correlate_impair5_with_combined(
            isolated_impairs, consecutive_sequences, combined_states
        )
        impair_bias['combined_correlation'] = combined_correlation

        # ================================================================
        # CORRÉLATION AVEC INDEX 4 (P/B) ET INDEX 5 (S/O)
        # ================================================================

        # Corrélation IMPAIR → P/B
        pb_correlation = self._correlate_impair5_with_pb(
            isolated_impairs, consecutive_sequences, pb_outcomes, len(hands_data)
        )
        impair_bias['pb_impact_after_impairs'] = pb_correlation

        # Corrélation IMPAIR → S/O
        so_correlation = self._correlate_impair5_with_so(
            isolated_impairs, consecutive_sequences, so_outcomes, len(hands_data)
        )
        impair_bias['so_prediction_bias'] = so_correlation

        # ================================================================
        # GÉNÉRATION SIGNAUX PRIORITÉ 1 (TOUJOURS EXPLOITABLES)
        # ================================================================

        priority_1_signals = []
        total_attention = self.config.zero_value

        # Générer signaux pour chaque score d'attention
        for score in attention_scores:
            signal = {
                'signal_type': score['type'],
                'attention_level': score['attention_level'],
                'signal_strength': score['signal_strength'],
                'rarity_factor': score['rarity_factor'],
                'pb_influence': pb_correlation.get('deviation_strength', self.config.zero_value),
                'so_influence': so_correlation.get('prediction_strength', self.config.zero_value),
                'sync_influence': sync_correlation.get('correlation_strength', self.config.zero_value),
                'combined_influence': combined_correlation.get('correlation_strength', self.config.zero_value)
            }
            priority_1_signals.append(signal)
            total_attention += score['attention_level']

        impair_bias['priority_1_signals'] = priority_1_signals

        # ================================================================
        # CALCUL CONFIANCE GLOBALE (TOUJOURS > 0)
        # ================================================================

        # Confiance basée sur l'attention totale (jamais 0)
        base_confidence = min(self.config.one_value, total_attention / self.config.normalization_factor_10)  # Normalisation

        # Bonus pour corrélations détectées
        correlation_bonus = self.config.zero_value
        if pb_correlation.get('deviation_strength', self.config.zero_value) > self.config.correlation_strength_threshold:
            correlation_bonus += self.config.correlation_strong_bonus_factor
        if so_correlation.get('prediction_strength', self.config.zero_value) > self.config.correlation_strength_threshold:
            correlation_bonus += self.config.correlation_strong_bonus_factor
        if sync_correlation.get('correlation_strength', self.config.zero_value) > self.config.correlation_strength_threshold:
            correlation_bonus += self.config.correlation_bonus_factor
        if combined_correlation.get('correlation_strength', self.config.zero_value) > self.config.correlation_strength_threshold:
            correlation_bonus += self.config.correlation_bonus_factor

        # Confiance finale : PLUS de minimum forcé (peut être 0 pour éviter aveuglement)
        impair_bias['exploitation_confidence'] = max(self.config.zero_value, base_confidence + correlation_bonus)

        # Score de persistance du biais
        if len(consecutive_sequences) > 0:
            # Extraire les longueurs des séquences pour calculer la persistance
            sequence_lengths = [len(seq) for seq in consecutive_sequences]
            if len(sequence_lengths) > self.config.rollout_analyzer_min_sequence_length:
                # Mesurer la consistance des longueurs de séquences
                # 🚀 OPTIMISATION INTELLIGENCE ÉMERGENTE : Fenêtre récente spécialisée par cluster
                cluster_recent_window = self.config.get_cluster_recent_window_size(self.cluster_id)
                recent_lengths = sequence_lengths[-cluster_recent_window:] if len(sequence_lengths) >= cluster_recent_window else sequence_lengths
                if len(recent_lengths) > self.config.rollout_analyzer_min_sequence_length:
                    recent_std = statistics.stdev(recent_lengths)
                    overall_std = statistics.stdev(sequence_lengths)
                    # Persistance = stabilité récente vs globale
                    impair_bias['bias_persistence_score'] = max(self.config.zero_value, self.config.one_value - (recent_std / max(self.config.variance_threshold_minimum, overall_std)))
                else:
                    impair_bias['bias_persistence_score'] = self.config.half_value
            else:
                impair_bias['bias_persistence_score'] = self.config.half_value
        else:
            impair_bias['bias_persistence_score'] = self.config.zero_value

        return impair_bias


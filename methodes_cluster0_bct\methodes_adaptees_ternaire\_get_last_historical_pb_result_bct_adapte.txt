MÉTHODE : _get_last_historical_pb_result
LIGNE DÉBUT : 4146
SIGNATURE : def _get_last_historical_pb_result(self, analyzer_report: Dict) -> str:
================================================================================

    def _get_last_historical_pb_result(self, analyzer_report: Dict) -> str:
"""
    ADAPTATION BCT - _get_last_historical_pb_result.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Récupère le dernier résultat P/B de l'historique pour conversion S/O

        Returns:
            'P' ou 'B' ou None si pas d'historique
        """
        # Essayer d'obtenir depuis l'analyse PBT
        indices_analysis = analyzer_report.get('indices_analysis', {})
        pbt_analysis = indices_analysis.get('pbt', {})
        pbt_sequence = pbt_analysis.get('pbt_sequence', [])

        if pbt_sequence:
            # Trouver le dernier résultat P/B (ignorer les Ties)
            for result in reversed(pbt_sequence):
                if result in ['P', 'B']:
                    return result

        # Fallback : essayer depuis quick_access
        quick_access = analyzer_report.get('quick_access', {})
        last_hand = quick_access.get('last_hand_analysis', {})
        last_result = last_hand.get('result')

        if last_result in ['P', 'B']:
            return last_result

        return None  # Pas de référence trouvée

    # ========================================================================
    # 🏆 SYSTÈME DE RÉCOMPENSES ROLLOUT 2 - FORMULES AZR
    # ========================================================================


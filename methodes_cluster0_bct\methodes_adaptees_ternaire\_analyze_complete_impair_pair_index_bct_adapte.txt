MÉTHODE : _analyze_complete_impair_pair_index
LIGNE DÉBUT : 5189
SIGNATURE : def _analyze_complete_impair_pair_index(self, hands_data: List) -> Dict:
================================================================================

    def _analyze_complete_impair_pair_index(self, hands_data: List) -> Dict:
"""
    ADAPTATION BCT - _analyze_complete_impair_pair_index.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Analyse complète INDEX 1 : IMPAIR/PAIR

        Calcule pour CHAQUE manche depuis le brûlage :
        - Position dans la séquence (impaire ou paire)
        - Corrélation avec les résultats P/B/T
        - Patterns de distribution et fréquences
        """
        impair_pair_analysis = {
            'sequence_positions': [],      # [1,2,3,4,5,6,7,8,9,10,...]
            'position_types': [],          # ['impair_5',['pair_4', 'pair_6'],'impair_5',['pair_4', 'pair_6'],...]
            'pbt_outcomes': [],            # ['P','B','P','T','B','P',...]
            'correlations': {},            # Corrélations IMPAIR/PAIR → P/B/T
            'consecutive_patterns': {},    # Séquences consécutives
            'distribution_stats': {}       # Statistiques de distribution
        }

        # Calcul pour chaque manche depuis brûlage
        for hand_number in range(1, len(hands_data) + 1):
            position_type = 'impair_5' if hand_number % 2 == 1 else ['pair_4', 'pair_6']
            pbt_outcome = hands_data[hand_number - 1].pbt_result

            impair_pair_analysis['sequence_positions'].append(hand_number)
            impair_pair_analysis['position_types'].append(position_type)
            impair_pair_analysis['pbt_outcomes'].append(pbt_outcome)

        # Analyse corrélations IMPAIR/PAIR → P/B (FOCUS P/B UNIQUEMENT)
        impair_outcomes = [outcome for i, outcome in enumerate(impair_pair_analysis['pbt_outcomes'])
                          if impair_pair_analysis['position_types'][i] == 'impair_5']
        pair_outcomes = [outcome for i, outcome in enumerate(impair_pair_analysis['pbt_outcomes'])
                        if impair_pair_analysis['position_types'][i] == ['pair_4', 'pair_6']]

        # Filtrer seulement P/B pour l'analyse d'impact (exclure Ties)
        impair_pb_outcomes = [outcome for outcome in impair_outcomes if outcome in ['P', 'B']]
        pair_pb_outcomes = [outcome for outcome in pair_outcomes if outcome in ['P', 'B']]

        impair_pair_analysis['correlations'] = {
            # Impact sur P/B uniquement (Ties exclus de l'analyse d'impact)
            'impair_to_player': impair_pb_outcomes.count('P') / max(1, len(impair_pb_outcomes)),
            'impair_to_banker': impair_pb_outcomes.count('B') / max(1, len(impair_pb_outcomes)),
            'pair_to_player': pair_pb_outcomes.count('P') / max(1, len(pair_pb_outcomes)),
            'pair_to_banker': pair_pb_outcomes.count('B') / max(1, len(pair_pb_outcomes)),
            # Métadonnées pour traçabilité
            'total_impair_hands': len(impair_outcomes),  # Total incluant Ties
            'total_pair_hands': len(pair_outcomes),      # Total incluant Ties
            'impair_pb_hands': len(impair_pb_outcomes),  # P/B seulement
            'pair_pb_hands': len(pair_pb_outcomes)       # P/B seulement
        }

        # Analyse séquences consécutives
        impair_consecutive = self._count_consecutive_pattern(impair_pair_analysis['position_types'], 'impair_5')
        pair_consecutive = self._count_consecutive_pattern(impair_pair_analysis['position_types'], ['pair_4', 'pair_6'])

        # Analyse asymétrique avec attention spéciale aux séquences IMPAIR
        impair_alert_level = self._calculate_asymmetric_impair_alert_level(impair_consecutive)
        pair_alert_level = self._calculate_asymmetric_pair_alert_level(pair_consecutive)

        impair_pair_analysis['consecutive_patterns'] = {
            'max_impair_consecutive': impair_consecutive,
            'max_pair_consecutive': pair_consecutive,
            'impair_alert_level': impair_alert_level,
            'pair_alert_level': pair_alert_level,
            'impair_rarity_score': self._calculate_impair_rarity_score(impair_consecutive),
            'pair_commonality_score': self._calculate_pair_commonality_score(pair_consecutive),
            'asymmetric_significance': self._calculate_asymmetric_significance(impair_consecutive, pair_consecutive)
        }

        return impair_pair_analysis


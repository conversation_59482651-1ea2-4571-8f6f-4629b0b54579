# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 8931 à 9094
# Type: Méthode de la classe AZRCluster

    def _analyze_transition_moments_impact(self, impair_pair_seq: List[str], desync_sync_seq: List[str],
                                         pbt_seq: List[str], so_seq: List[str]) -> Dict:
        """
        Analyse l'impact des moments de TRANSITION/VARIATION dans les indices 1-3
        sur les indices 4 (P/B sans TIE) et 5 (S/O)

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Détecte les changements d'état et leur impact prédictif

        Args:
            impair_pair_seq: Séquence index 1 (IMPAIR/PAIR)
            desync_sync_seq: Séquence index 2 (DESYNC/SYNC)
            pbt_seq: Séquence index 4 (P/B/T)
            so_seq: Séquence index 5 (S/O)

        Returns:
            Dictionnaire avec analyse des impacts de transition
        """

        transition_impacts = {
            'impair_pair_transitions': {},      # Transitions IMPAIR↔PAIR → impact P/B & S/O
            'desync_sync_transitions': {},      # Transitions DESYNC↔SYNC → impact P/B & S/O
            'combined_transitions': {},         # Transitions combinées → impact P/B & S/O
            'transition_strength_analysis': {} # Force globale des transitions
        }

        # ================================================================
        # 1. TRANSITIONS IMPAIR/PAIR → IMPACT P/B & S/O
        # ================================================================

        # Détecter transitions IMPAIR↔PAIR
        impair_pair_transitions = []
        for i in range(1, len(impair_pair_seq)):
            if impair_pair_seq[i-1] != impair_pair_seq[i]:
                transition_type = f"{impair_pair_seq[i-1]}_TO_{impair_pair_seq[i]}"
                impair_pair_transitions.append({
                    'position': i,
                    'type': transition_type,
                    'from_state': impair_pair_seq[i-1],
                    'to_state': impair_pair_seq[i]
                })

        # Analyser impact transitions IMPAIR/PAIR sur P/B (sans TIE)
        for transition_type in ['IMPAIR_TO_PAIR', 'PAIR_TO_IMPAIR']:
            transitions_of_type = [t for t in impair_pair_transitions if t['type'] == transition_type]

            if len(transitions_of_type) >= 2:  # Minimum 2 transitions pour analyse
                # Impact sur P/B après transition
                pb_after_transition = []
                for transition in transitions_of_type:
                    pos = transition['position']
                    if pos < len(pbt_seq):
                        result = pbt_seq[pos]
                        if result in ['P', 'B']:  # Exclure TIE
                            pb_after_transition.append(result)

                if pb_after_transition:
                    p_count = pb_after_transition.count('P')
                    b_count = pb_after_transition.count('B')
                    total_pb = p_count + b_count

                    transition_impacts['impair_pair_transitions'][f'{transition_type}_to_pb'] = {
                        'player_ratio': p_count / total_pb,
                        'banker_ratio': b_count / total_pb,
                        'dominant_pb': 'P' if p_count > b_count else 'B',
                        'sample_size': total_pb,
                        'transition_count': len(transitions_of_type)
                    }

                # Impact sur S/O après transition
                so_after_transition = []
                for transition in transitions_of_type:
                    pos = transition['position']
                    # S/O commence à manche 2, donc ajustement
                    if pos > 0 and pos - 1 < len(so_seq):
                        so_after_transition.append(so_seq[pos - 1])

                if so_after_transition:
                    s_count = so_after_transition.count('S')
                    o_count = so_after_transition.count('O')
                    total_so = s_count + o_count

                    transition_impacts['impair_pair_transitions'][f'{transition_type}_to_so'] = {
                        'same_ratio': s_count / total_so,
                        'opposite_ratio': o_count / total_so,
                        'dominant_so': 'S' if s_count > o_count else 'O',
                        'sample_size': total_so
                    }

        # ================================================================
        # 2. TRANSITIONS DESYNC/SYNC → IMPACT P/B & S/O
        # ================================================================

        # Détecter transitions DESYNC↔SYNC
        desync_sync_transitions = []
        for i in range(1, len(desync_sync_seq)):
            if desync_sync_seq[i-1] != desync_sync_seq[i]:
                transition_type = f"{desync_sync_seq[i-1]}_TO_{desync_sync_seq[i]}"
                desync_sync_transitions.append({
                    'position': i,
                    'type': transition_type,
                    'from_state': desync_sync_seq[i-1],
                    'to_state': desync_sync_seq[i]
                })

        # Analyser impact transitions DESYNC/SYNC sur P/B et S/O
        for transition_type in ['DESYNC_TO_SYNC', 'SYNC_TO_DESYNC']:
            transitions_of_type = [t for t in desync_sync_transitions if t['type'] == transition_type]

            if len(transitions_of_type) >= 2:  # Minimum 2 transitions
                # Impact sur P/B après transition (sans TIE)
                pb_after_transition = []
                for transition in transitions_of_type:
                    pos = transition['position']
                    if pos < len(pbt_seq):
                        result = pbt_seq[pos]
                        if result in ['P', 'B']:  # Exclure TIE
                            pb_after_transition.append(result)

                if pb_after_transition:
                    p_count = pb_after_transition.count('P')
                    b_count = pb_after_transition.count('B')
                    total_pb = p_count + b_count

                    transition_impacts['desync_sync_transitions'][f'{transition_type}_to_pb'] = {
                        'player_ratio': p_count / total_pb,
                        'banker_ratio': b_count / total_pb,
                        'dominant_pb': 'P' if p_count > b_count else 'B',
                        'sample_size': total_pb
                    }

                # Impact sur S/O après transition
                so_after_transition = []
                for transition in transitions_of_type:
                    pos = transition['position']
                    if pos > 0 and pos - 1 < len(so_seq):
                        so_after_transition.append(so_seq[pos - 1])

                if so_after_transition:
                    s_count = so_after_transition.count('S')
                    o_count = so_after_transition.count('O')
                    total_so = s_count + o_count

                    transition_impacts['desync_sync_transitions'][f'{transition_type}_to_so'] = {
                        'same_ratio': s_count / total_so,
                        'opposite_ratio': o_count / total_so,
                        'dominant_so': 'S' if s_count > o_count else 'O',
                        'sample_size': total_so
                    }

        # ================================================================
        # 3. FORCE GLOBALE DES TRANSITIONS
        # ================================================================

        total_transitions = len(impair_pair_transitions) + len(desync_sync_transitions)
        transition_impacts['transition_strength_analysis'] = {
            'total_transitions_detected': total_transitions,
            'impair_pair_transitions_count': len(impair_pair_transitions),
            'desync_sync_transitions_count': len(desync_sync_transitions),
            'transition_frequency': total_transitions / max(1, len(impair_pair_seq)) if impair_pair_seq else 0,
            'has_significant_transitions': total_transitions >= 3
        }

        return transition_impacts
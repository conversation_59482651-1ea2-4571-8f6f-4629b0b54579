MÉTHODE : _generate_bias_generation_guidance
LIGNE DÉBUT : 2851
SIGNATURE : def _generate_bias_generation_guidance(self, bias_synthesis: Dict) -> Dict:
================================================================================

    def _generate_bias_generation_guidance(self, bias_synthesis: Dict) -> Dict:
"""
    ADAPTATION BCT - _generate_bias_generation_guidance.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Génère les directives de génération basées sur les biais pour le Rollout 2"""
        strongest_bias = bias_synthesis.get('strongest_bias', {})
        exploitation_signals = bias_synthesis.get('exploitation_signals', {})

        return {
            'primary_focus': strongest_bias.get('bias_type', 'balanced'),
            'exploitation_intensity': strongest_bias.get('bias_strength', 0.0),
            'sequence_generation_strategy': bias_synthesis.get('optimal_exploitation_strategy', {}).get('strategy_type', 'balanced'),
            'bias_multiplier': exploitation_signals.get('exploitation_multiplier', 1.0),
            'confidence_threshold': self.config.confidence_low_threshold if strongest_bias.get('bias_strength', self.config.zero_value) > self.config.confidence_medium_threshold else self.config.confidence_medium_threshold
        }


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6252 à 6295
# Type: Méthode de la classe AZRCluster

    def _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float:
        """
        Calcule la confiance finale du cluster basée sur la séquence sélectionnée

        FOCUS : Confiance composite basée sur Rollout 1 + qualité de sélection
        """
        # 1. Confiance de base de la séquence
        sequence_probability = best_sequence.get('estimated_probability', self.config.rollout3_cluster_confidence_default)

        # 2. Qualité d'évaluation de la séquence
        evaluation_score = best_sequence.get('evaluation', {}).get('total_score', self.config.rollout3_neutral_evaluation_value)

        # 3. Qualité d'analyse du Rollout 1
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', self.config.rollout3_neutral_evaluation_value)

        # 4. Facteur de confiance des signaux
        signals_summary = analyzer_report.get('signals_summary', {})
        overall_confidence = signals_summary.get('overall_confidence', self.config.rollout3_cluster_confidence_default)

        # 5. Calcul de la confiance composite
        cluster_confidence = (
            sequence_probability * self.config.cluster_confidence_sequence_weight +      # Probabilité de la séquence
            evaluation_score * self.config.cluster_confidence_evaluation_weight +          # Qualité de l'évaluation
            analysis_quality * self.config.cluster_confidence_analysis_weight +         # Qualité de l'analyse Rollout 1
            overall_confidence * self.config.cluster_confidence_signals_weight         # Confiance des signaux
        )

        # 6. Ajustements basés sur les conditions
        # Bonus si exploitation prête
        exploitation_ready = signals_summary.get('exploitation_ready', False)
        if exploitation_ready:
            cluster_confidence *= self.config.cluster_exploitation_bonus  # Bonus exploitation

        # Ajustement basé sur le niveau d'alerte
        quick_access = analyzer_report.get('quick_access', {})
        alert_level = quick_access.get('alert_level', 'MEDIUM')

        if alert_level == 'HIGH':
            cluster_confidence *= self.config.cluster_high_alert_bonus   # Bonus alerte haute
        elif alert_level == 'LOW':
            cluster_confidence *= self.config.cluster_low_alert_malus  # Malus alerte faible

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, cluster_confidence))
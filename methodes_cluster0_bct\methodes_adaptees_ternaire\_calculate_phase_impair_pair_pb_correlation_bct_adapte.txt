MÉTHODE : _calculate_phase_impair_pair_pb_correlation
LIGNE DÉBUT : 7424
SIGNATURE : def _calculate_phase_impair_pair_pb_correlation(self, impair_pair_seq: List[str], pbt_seq: List[str]) -> Dict:
================================================================================

    def _calculate_phase_impair_pair_pb_correlation(self, impair_pair_seq: List[str], pbt_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _calculate_phase_impair_pair_pb_correlation.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule corrélations IMPAIR/PAIR → P/B pour une phase (sans TIE)"""

        if not impair_pair_seq or not pbt_seq:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        # Filtrer seulement P/B (exclure TIE)
        pb_only = [result for result in pbt_seq if result in ['P', 'B']]

        if len(pb_only) < 2:
            return {'correlation_strength': 0.0, 'sample_size': len(pb_only)}

        # Aligner séquences (prendre minimum des longueurs)
        min_length = min(len(impair_pair_seq), len(pb_only))
        aligned_impair_pair = impair_pair_seq[:min_length]
        aligned_pb = pb_only[:min_length]

        # Calculer corrélations IMPAIR → P/B
        impair_positions = [i for i, val in enumerate(aligned_impair_pair) if val == 'impair_5']
        pair_positions = [i for i, val in enumerate(aligned_impair_pair) if val == ['pair_4', 'pair_6']]

        impair_to_p = sum(1 for pos in impair_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'P')
        impair_to_b = sum(1 for pos in impair_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'B')
        pair_to_p = sum(1 for pos in pair_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'P')
        pair_to_b = sum(1 for pos in pair_positions if pos < len(aligned_pb) and aligned_pb[pos] == 'B')

        # Force de corrélation (écart par rapport à 50/50)
        impair_total = impair_to_p + impair_to_b
        pair_total = pair_to_p + pair_to_b

        impair_strength = 0.0
        pair_strength = 0.0

        if impair_total > 0:
            impair_p_ratio = impair_to_p / impair_total
            impair_strength = abs(impair_p_ratio - 0.5)

        if pair_total > 0:
            pair_p_ratio = pair_to_p / pair_total
            pair_strength = abs(pair_p_ratio - 0.5)

        # Force globale pondérée
        total_sample = impair_total + pair_total
        if total_sample > 0:
            correlation_strength = (impair_strength * impair_total + pair_strength * pair_total) / total_sample
        else:
            correlation_strength = 0.0

        return {
            'correlation_strength': correlation_strength,
            'impair_strength': impair_strength,
            'pair_strength': pair_strength,
            'sample_size': total_sample,
            'impair_sample': impair_total,
            'pair_sample': pair_total
        }


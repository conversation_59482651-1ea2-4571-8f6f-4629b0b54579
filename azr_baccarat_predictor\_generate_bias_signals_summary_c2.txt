# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 3578 à 3599
# Type: Méthode de la classe AZRCluster

    def _generate_bias_signals_summary_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
        """
        🎯 C2 SPÉCIALISÉ - Génération signaux de biais avec spécialisation patterns courts
        """
        # Utiliser la méthode de base
        base_signals = self._generate_bias_signals_summary(bias_synthesis)

        # Ajouter les signaux spécialisés C2
        c2_signals = {
            'c2_short_patterns_signal': {
                'signal_strength': c2_specialization.get('specialization_bonus', self.config.zero_value),
                'patterns_detected': c2_specialization.get('short_patterns_detected', 0),
                'reactivity_score': c2_specialization.get('reactivity_score', self.config.zero_value),
                'fenetre_optimisee': c2_specialization.get('fenetre_recente_optimisee', 3)
            }
        }

        # Fusionner les signaux
        base_signals.update(c2_signals)
        base_signals['specialization_applied'] = 'C2_patterns_courts'

        return base_signals
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10475 à 10504
# Type: Méthode de la classe AZRCluster

    def _extract_transition_moments_strength(self, transition_impacts: Dict) -> float:
        """Extrait la force des impacts de moments de transition (focus P/B et S/O)"""

        if not transition_impacts:
            return 0.0

        # Chercher métriques de force dans les impacts de transition
        strength_metrics = []

        # Analyser différents types de transitions
        for transition_type, transition_data in transition_impacts.items():
            if isinstance(transition_data, dict):

                # Force P/B (sans TIE)
                if 'pb_impact' in transition_data:
                    pb_impact = transition_data['pb_impact']
                    if 'pb_bias_strength' in pb_impact:
                        strength_metrics.append(pb_impact['pb_bias_strength'])

                # Force S/O
                if 'so_impact' in transition_data:
                    so_impact = transition_data['so_impact']
                    if 'so_bias_strength' in so_impact:
                        strength_metrics.append(so_impact['so_bias_strength'])

                # Force globale du type de transition
                if 'type_strength' in transition_data:
                    strength_metrics.append(transition_data['type_strength'])

        return sum(strength_metrics) / len(strength_metrics) if strength_metrics else 0.0
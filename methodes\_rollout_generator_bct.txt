# MÉTHODE ADAPTÉE POUR BCT.PY
# Source: _rollout_generator.txt (AZR)
# Adaptation: Système de comptage BCT avec INDEX détaillés

def _rollout_generator_bct(self, analyzer_report: Dict) -> Dict:
    """
    Rollout 2 Générateur BCT - Génération séquences candidates basées sur analyse complète
    
    ADAPTATION BCT - GÉNÉRATION AVEC INDEX DÉTAILLÉS :
    - EXPLOITE l'analyse complète des 5 INDEX BCT pour génération optimale
    - GÉNÈRE séquences candidates avec catégories précises (pair_4, impair_5, pair_6)
    - ENRICHIT avec INDEX combinés détaillés (pair_4_sync, impair_5_desync, etc.)
    - UTILISE les biais spécifiques détectés par le Rollout 1 BCT
    
    Stratégies de génération BCT :
    1. STRATÉGIE 1 : Exploitation biais impair_5 consécutifs
    2. STRATÉGIE 2 : Alternance optimisée pair_4 ↔ pair_6
    3. STRATÉGIE 3 : Séquences basées sur états combinés rares
    4. STRATÉGIE 4 : Fallback basé sur patterns historiques BCT
    
    Args:
        analyzer_report: Rapport complet du rollout analyseur BCT avec 5 INDEX détaillés
        
    Returns:
        Dict: Résultat structuré contenant séquences candidates BCT et métadonnées
            - sequences: List[Dict] - Séquences candidates enrichies BCT
            - generation_metadata: Dict - Métadonnées de génération BCT
            - signals_used: Dict - Signaux BCT utilisés pour la génération
            - generation_stats: Dict - Statistiques de génération BCT
    """
    try:
        if 'error' in analyzer_report:
            return {
                'sequences': [],
                'generation_metadata': {
                    'total_sequences_generated': self.config.zero_value,
                    'generation_strategy': 'analyzer_error',
                    'cluster_id': self.config.zero_value,  # Cluster 0 uniquement
                    'generation_timestamp': time.time(),
                    'bct_adaptation': True
                },
                'error': 'Analyzer report contains error',
                'analyzer_error': analyzer_report.get('error')
            }
        
        # ================================================================
        # NOUVEAU BCT : UTILISATION DES SECTIONS OPTIMISÉES DU ROLLOUT 1
        # ================================================================
        
        # Extraction des sections optimisées pour le générateur BCT
        bias_signals_summary = analyzer_report.get('bias_signals_summary', {})
        bias_generation_guidance = analyzer_report.get('bias_generation_guidance', {})
        bias_quick_access = analyzer_report.get('bias_quick_access', {})
        
        # Extraction analyse complète des biais structurels BCT (fallback)
        structural_bias_analysis = analyzer_report.get('structural_bias_analysis', {})
        bias_synthesis = analyzer_report.get('bias_synthesis', {})
        exploitation_metadata = analyzer_report.get('exploitation_metadata', {})
        
        # Définition espace de génération basé sur les biais BCT détectés
        generation_space_bct = self._define_optimized_generation_space_bct(
            bias_signals_summary, bias_generation_guidance, bias_quick_access,
            structural_bias_analysis, bias_synthesis, exploitation_metadata
        )
        
        # ================================================================
        # GÉNÉRATION OPTIMISÉE BCT BASÉE SUR LES SIGNAUX DU ROLLOUT 1
        # ================================================================
        
        # Génération de séquences candidates basées sur les signaux BCT optimisés
        candidates_bct = self._generate_sequences_from_bct_signals(
            bias_signals_summary, bias_generation_guidance, bias_quick_access, generation_space_bct
        )
        
        # Fallback BCT : Génération classique si pas de signaux optimisés
        if not candidates_bct:
            candidates_bct = self._generate_fallback_sequences_bct(generation_space_bct)
        
        # Enrichissement séquences avec tous les INDEX BCT détaillés
        enriched_candidates_bct = self._enrich_sequences_with_complete_bct_indexes(
            candidates_bct, structural_bias_analysis, bias_synthesis
        )
        
        # NOUVEAU BCT : Retourner un dictionnaire structuré avec spécificités BCT
        generator_result_bct = {
            'sequences': enriched_candidates_bct,
            'generation_metadata': {
                'total_sequences_generated': len(enriched_candidates_bct),
                'generation_strategy': 'bct_bias_signals_based',
                'cluster_id': self.config.zero_value,  # Cluster 0 uniquement
                'generation_timestamp': time.time(),
                'bct_adaptation': True,
                'bct_categories_used': ['pair_4', 'impair_5', 'pair_6'],
                'bct_combined_states_generated': [
                    'pair_4_sync', 'pair_4_desync',
                    'impair_5_sync', 'impair_5_desync', 
                    'pair_6_sync', 'pair_6_desync'
                ]
            },
            'signals_used': {
                'bias_signals_summary': bias_signals_summary,
                'bias_generation_guidance': bias_generation_guidance,
                'bias_quick_access': bias_quick_access,
                'bct_specific_signals': True
            },
            'generation_stats': {
                'fallback_used': len(candidates_bct) == self.config.zero_value,
                'enrichment_applied': True,
                'avg_sequence_length': (
                    sum(len(seq) for seq in enriched_candidates_bct) / len(enriched_candidates_bct) 
                    if enriched_candidates_bct else self.config.zero_value
                ),
                'bct_bias_exploitation_rate': bias_synthesis.get('exploitation_confidence', self.config.zero_value),
                'strongest_bct_bias_used': bias_synthesis.get('strongest_bias', {})
            }
        }
        
        return generator_result_bct
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Erreur rollout generator BCT cluster {self.config.zero_value}: {type(e).__name__}: {e}")
        logger.error(f"Détails erreur BCT cluster {self.config.zero_value}: {error_details}")
        return {
            'sequences': [],
            'generation_metadata': {
                'total_sequences_generated': self.config.zero_value,
                'generation_strategy': 'error_fallback_bct',
                'cluster_id': self.config.zero_value,
                'generation_timestamp': time.time(),
                'bct_adaptation': True
            },
            'error': str(e),
            'error_details': error_details
        }

# ================================================================
# MÉTHODES DE SUPPORT BCT À IMPLÉMENTER
# ================================================================

def _define_optimized_generation_space_bct(self, bias_signals: Dict, generation_guidance: Dict,
                                          quick_access: Dict, structural_analysis: Dict,
                                          synthesis: Dict, metadata: Dict) -> Dict:
    """Définit l'espace de génération optimisé pour BCT"""
    # Adaptation pour définir l'espace de génération avec catégories BCT
    generation_space = {
        'bct_categories': ['pair_4', 'impair_5', 'pair_6'],
        'bct_combined_states': [
            'pair_4_sync', 'pair_4_desync',
            'impair_5_sync', 'impair_5_desync',
            'pair_6_sync', 'pair_6_desync'
        ],
        'bias_priorities': {
            'impair_5_consecutive': synthesis.get('impair5_bias_strength', self.config.zero_value),
            'pair_4_6_alternation': synthesis.get('pair46_bias_strength', self.config.zero_value),
            'sync_desync_correlation': synthesis.get('sync_bias_strength', self.config.zero_value)
        },
        'generation_constraints': {
            'max_sequence_length': self.config.rollout2_max_sequence_length,
            'min_bias_threshold': self.config.rollout2_min_bias_threshold,
            'diversity_requirement': self.config.rollout2_diversity_requirement
        }
    }
    return generation_space

def _generate_sequences_from_bct_signals(self, signals: Dict, guidance: Dict,
                                        quick_access: Dict, generation_space: Dict) -> List[Dict]:
    """Génère des séquences basées sur les signaux de biais BCT"""
    # Adaptation pour générer des séquences avec catégories BCT spécifiques
    sequences = []
    
    # Stratégie 1 : Exploitation biais impair_5
    if signals.get('impair_5_bias_detected', False):
        impair5_sequences = self._generate_impair5_exploitation_sequences_bct(signals, generation_space)
        sequences.extend(impair5_sequences)
    
    # Stratégie 2 : Alternance pair_4 ↔ pair_6
    if signals.get('pair_alternation_bias_detected', False):
        pair_alternation_sequences = self._generate_pair46_alternation_sequences_bct(signals, generation_space)
        sequences.extend(pair_alternation_sequences)
    
    # Stratégie 3 : États combinés rares
    if signals.get('combined_rare_states_detected', False):
        combined_sequences = self._generate_combined_rare_sequences_bct(signals, generation_space)
        sequences.extend(combined_sequences)
    
    return sequences

def _generate_fallback_sequences_bct(self, generation_space: Dict) -> List[Dict]:
    """Génère des séquences de fallback basées sur patterns historiques BCT"""
    # Adaptation pour générer des séquences de fallback avec logique BCT
    fallback_sequences = []
    
    # Séquence basée sur distribution équilibrée BCT
    balanced_sequence = self._generate_balanced_bct_sequence(generation_space)
    fallback_sequences.append(balanced_sequence)
    
    # Séquence basée sur tendances récentes
    trend_sequence = self._generate_trend_based_bct_sequence(generation_space)
    fallback_sequences.append(trend_sequence)
    
    return fallback_sequences

def _enrich_sequences_with_complete_bct_indexes(self, sequences: List[Dict],
                                               structural_analysis: Dict,
                                               synthesis: Dict) -> List[Dict]:
    """Enrichit les séquences avec tous les INDEX BCT détaillés"""
    # Adaptation pour enrichir avec INDEX BCT complets
    enriched_sequences = []
    
    for sequence in sequences:
        enriched_sequence = {
            'sequence_data': sequence,
            'bct_indexes': {
                'cards_category_sequence': [],  # pair_4, impair_5, pair_6
                'sync_state_sequence': [],      # sync, desync
                'combined_state_sequence': [],  # pair_4_sync, impair_5_desync, etc.
                'result_sequence': [],          # PLAYER, BANKER, TIE
                'so_conversion_sequence': []    # S, O, --
            },
            'enrichment_metadata': {
                'bias_exploitation_score': synthesis.get('exploitation_confidence', self.config.zero_value),
                'sequence_quality_score': self.config.rollout2_base_quality_score,
                'bct_conformity_score': self.config.one_value  # 100% conforme BCT
            }
        }
        enriched_sequences.append(enriched_sequence)
    
    return enriched_sequences

# ================================================================
# CONFIGURATION BCT CENTRALISÉE
# ================================================================
# Tous les paramètres doivent être dans AZRConfig :
# - Longueurs de séquences (rollout2_max_sequence_length)
# - Seuils de biais (rollout2_min_bias_threshold)
# - Scores de qualité (rollout2_base_quality_score)
# - Exigences de diversité (rollout2_diversity_requirement)
# ================================================================

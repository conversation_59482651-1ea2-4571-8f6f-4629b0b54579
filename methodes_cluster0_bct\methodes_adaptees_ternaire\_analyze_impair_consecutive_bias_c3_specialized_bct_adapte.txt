MÉTHODE : _analyze_impair5_consecutive_bias_c3_specialized
LIGNE DÉBUT : 2043
SIGNATURE : def _analyze_impair5_consecutive_bias_c3_specialized(self, hands_data: List, position_types: List,
================================================================================

    def _analyze_impair5_consecutive_bias_c3_specialized(self, hands_data: List, position_types: List,
                                                       sync_states: List, combined_states: List,
                                                       pb_outcomes: List, so_outcomes: List) -> Dict:
"""
    ADAPTATION BCT - _analyze_impair_consecutive_bias_c3_specialized.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 C3 SPÉCIALISÉ - ANALYSE IMPAIRS avec fenêtres récentes optimisées (4 manches)

        LOGIQUE DE BASE (IDENTIQUE C0) + SPÉCIALISATION C3 :
        - Utilise la fenêtre récente spécialisée C3 (4 manches)
        - Focus sur patterns moyens et vision élargie
        - Équilibre réactivité/continuité
        """
        import statistics

        # Utiliser la méthode de base avec les données pré-extraites
        impair_bias = self._analyze_impair5_consecutive_bias(hands_data)

        # ================================================================
        # SPÉCIALISATION C3 : FENÊTRES RÉCENTES OPTIMISÉES
        # ================================================================

        # Récupérer la fenêtre récente spécialisée C3 (4 manches)
        cluster_recent_window = self.config.get_cluster_recent_window_size(self.cluster_id)  # 4 pour C3

        # Appliquer la fenêtre spécialisée aux séquences d'IMPAIRS
        consecutive_sequences = impair_bias.get('consecutive_impair_sequences', [])
        if consecutive_sequences:
            # Analyser les séquences récentes avec fenêtre C3
            recent_sequences = consecutive_sequences[-cluster_recent_window:] if len(consecutive_sequences) >= cluster_recent_window else consecutive_sequences

            # Bonus spécialisation pour patterns moyens détectés récemment
            c3_medium_pattern_bonus = self.config.zero_value
            for seq in recent_sequences:
                seq_length = len(seq)
                if 4 <= seq_length <= 6:  # Patterns moyens (spécialisation C3)
                    c3_medium_pattern_bonus += self.config.multiplier_increment_03 * seq_length

            # Ajouter le bonus spécialisation C3
            impair_bias['c3_medium_pattern_bonus'] = min(self.config.one_value, c3_medium_pattern_bonus)
            impair_bias['c3_recent_window_applied'] = cluster_recent_window
            impair_bias['c3_recent_sequences_count'] = len(recent_sequences)

        # ================================================================
        # SPÉCIALISATION C3 : VISION ÉLARGIE
        # ================================================================

        # Analyser les transitions moyennes (spécialisation C3)
        if len(position_types) >= cluster_recent_window:
            recent_positions = position_types[-cluster_recent_window:]
            medium_transitions = []

            # Détecter les patterns moyens (4-6 manches)
            for i in range(len(recent_positions) - 3):
                pattern = recent_positions[i:i+4]
                if len(set(pattern)) > 1:  # Pattern avec variation
                    medium_transitions.append(pattern)

            # Bonus vision élargie pour patterns moyens détectés
            if medium_transitions:
                vision_bonus = len(medium_transitions) / cluster_recent_window
                impair_bias['c3_vision_bonus'] = vision_bonus
                impair_bias['c3_medium_transitions_detected'] = len(medium_transitions)
            else:
                impair_bias['c3_vision_bonus'] = self.config.zero_value
                impair_bias['c3_medium_transitions_detected'] = 0

        # ================================================================
        # CONFIANCE FINALE AVEC BONUS SPÉCIALISATION C3
        # ================================================================

        # Confiance de base
        base_confidence = impair_bias.get('exploitation_confidence', self.config.zero_value)

        # Bonus spécialisation C3
        c3_bonus = (
            impair_bias.get('c3_medium_pattern_bonus', self.config.zero_value) * self.config.confidence_multiplier_03 +
            impair_bias.get('c3_vision_bonus', self.config.zero_value) * self.config.confidence_multiplier_04
        )

        # Confiance finale avec spécialisation C3
        impair_bias['exploitation_confidence'] = min(self.config.one_value, base_confidence + c3_bonus)
        impair_bias['c3_specialization_applied'] = True
        impair_bias['c3_total_bonus'] = c3_bonus

        return impair_bias


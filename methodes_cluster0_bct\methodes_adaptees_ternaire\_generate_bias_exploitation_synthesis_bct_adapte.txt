MÉTHODE : _generate_bias_exploitation_synthesis
LIGNE DÉBUT : 2670
SIGNATURE : def _generate_bias_exploitation_synthesis(self, bias_analyses: Dict, hands_data: List) -> Dict:
================================================================================

    def _generate_bias_exploitation_synthesis(self, bias_analyses: Dict, hands_data: List) -> Dict:
"""
    ADAPTATION BCT - _generate_bias_exploitation_synthesis.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        SYNTHÈSE ÉQUILIBRÉE AVEC SYSTÈME DE VETO ANTI-AVEUGLEMENT

        HIÉRARCHIE ÉQUILIBRÉE :
        1. PRIORITÉ 1 : IMPAIRS (40% du poids) - Réduit pour éviter domination
        2. PRIORITÉ 2 : PAIRS (30% du poids) - Fortement augmenté
        3. PRIORITÉ 3 : SYNC/DESYNC (20% du poids) - Quadruplé pour éviter aveuglement
        4. PRIORITÉ 4 : COMBINÉS (10% du poids) - Doublé

        SYSTÈME DE VETO :
        - SYNC > 85% → Domine avec 70% du poids
        - COMBINÉ > 90% → Domine avec 70% du poids
        - PAIRS > 70% ET IMPAIRS < 20% → PAIRS domine
        - Coalition SYNC+COMBINÉ > 75% ET IMPAIRS < 25% → Coalition domine

        Génère TOUJOURS des signaux exploitables (jamais de blocage)
        """
        priority_synthesis = {
            'exploitation_quality': 0.0,
            'strongest_priority': {},
            'exploitation_confidence': 0.0,  # Toujours > 0
            'priority_distribution': {},
            'exploitation_signals': {},
            'risk_assessment': {},
            'optimal_exploitation_strategy': {},
            'no_blocking_guaranteed': True
        }

        # Extraction des analyses par priorité
        impair_bias = bias_analyses.get('priority_1_impair_bias', {})
        pair_bias = bias_analyses.get('priority_2_pair_bias', {})
        sync_bias = bias_analyses.get('priority_3_sync_bias', {})
        combined_bias = bias_analyses.get('priority_4_combined_bias', {})
        pb_correlation = bias_analyses.get('pb_correlation', {})
        so_correlation = bias_analyses.get('so_correlation', {})

        # ================================================================
        # CALCUL PONDÉRÉ PAR PRIORITÉS ÉQUILIBRÉES (ANTI-AVEUGLEMENT)
        # ================================================================

        # Priorité 1 : IMPAIRS (40% du poids - réduit pour éviter domination)
        priority_1_strength = impair_bias.get('exploitation_confidence', 0.0)  # Plus de minimum forcé !
        priority_1_weight = self.config.rollout2_priority_weight_1

        # Priorité 2 : PAIRS (30% du poids - augmenté pour équilibrer)
        priority_2_strength = pair_bias.get('priority_2_confidence', 0.0)  # Plus de minimum forcé !
        priority_2_weight = self.config.rollout2_priority_weight_2

        # Priorité 3 : SYNC/DESYNC (20% du poids - fortement augmenté)
        priority_3_strength = sync_bias.get('exploitation_confidence', 0.0)
        priority_3_weight = self.config.rollout2_priority_weight_3

        # Priorité 4 : COMBINÉS (10% du poids - doublé)
        priority_4_strength = combined_bias.get('combined_confidence', 0.0)
        priority_4_weight = self.config.rollout2_priority_weight_4

        # Distribution des priorités
        priority_synthesis['priority_distribution'] = {
            'priority_1_impair': {
                'strength': priority_1_strength,
                'weight': priority_1_weight,
                'contribution': priority_1_strength * priority_1_weight
            },
            'priority_2_pair': {
                'strength': priority_2_strength,
                'weight': priority_2_weight,
                'contribution': priority_2_strength * priority_2_weight
            },
            'priority_3_sync': {
                'strength': priority_3_strength,
                'weight': priority_3_weight,
                'contribution': priority_3_strength * priority_3_weight
            },
            'priority_4_combined': {
                'strength': priority_4_strength,
                'weight': priority_4_weight,
                'contribution': priority_4_strength * priority_4_weight
            }
        }

        # ================================================================
        # IDENTIFICATION DE LA PRIORITÉ DOMINANTE
        # ================================================================

        contributions = priority_synthesis['priority_distribution']
        strongest_priority_key = max(contributions.keys(),
                                   key=lambda k: contributions[k]['contribution'])

        priority_synthesis['strongest_priority'] = {
            'priority_type': strongest_priority_key,
            'strength': contributions[strongest_priority_key]['strength'],
            'contribution': contributions[strongest_priority_key]['contribution'],
            'exploitation_ready': contributions[strongest_priority_key]['strength'] > self.config.rollout2_exploitation_readiness_threshold
        }

        # ================================================================
        # QUALITÉ D'EXPLOITATION GARANTIE
        # ================================================================

        total_hands = len(hands_data)
        sample_quality = min(1.0, total_hands / 20.0)  # Optimal à 20+ mains

        # Somme pondérée des contributions (toujours > 0)
        weighted_contribution = sum(contrib['contribution'] for contrib in contributions.values())

        # Qualité garantie : minimum 0.2 (20%)
        priority_synthesis['exploitation_quality'] = max(0.2,
                                                       (sample_quality + weighted_contribution) / 2)

        # ================================================================
        # SYSTÈME DE VETO POUR SIGNAUX EXCEPTIONNELS
        # ================================================================

        veto_result = self._apply_veto_system(priority_synthesis['priority_distribution'])

        if veto_result['veto_applied']:
            # Signal exceptionnel détecté - redistribution des poids
            dominant_priority = veto_result['dominant_priority']
            veto_reason = veto_result['veto_reason']

            logger.info(f"🚨 VETO APPLIQUÉ : {veto_reason} - Priorité dominante: {dominant_priority}")

            # Redistribution : 70% au signal exceptionnel, 30% aux autres
            for priority_key in priority_synthesis['priority_distribution']:
                if priority_key == dominant_priority:
                    priority_synthesis['priority_distribution'][priority_key]['weight'] = 0.7
                else:
                    # Redistribuer les 30% restants proportionnellement
                    original_weight = priority_synthesis['priority_distribution'][priority_key]['weight']
                    priority_synthesis['priority_distribution'][priority_key]['weight'] = original_weight * self.config.rollout3_quality_bonus_small / 0.6

            priority_synthesis['veto_applied'] = True
            priority_synthesis['veto_reason'] = veto_reason

        # ================================================================
        # DÉMONSTRATION ANTI-AVEUGLEMENT (MODE DEBUG)
        # ================================================================

        if len(hands_data) > 10:  # Seulement si assez de données
            self._demonstrate_anti_blindness_system(priority_synthesis['priority_distribution'])

        # ================================================================
        # CONFIANCE D'EXPLOITATION ÉQUILIBRÉE
        # ================================================================

        # Recalcul avec nouveaux poids (après veto éventuel)
        weighted_contribution = sum(
            contrib['strength'] * contrib['weight']
            for contrib in priority_synthesis['priority_distribution'].values()
        )

        # Bonus pour corrélations P/B et S/O
        pb_confidence = pb_correlation.get('pb_prediction_confidence', 0.0)
        so_confidence = so_correlation.get('so_prediction_confidence', 0.0)
        correlation_bonus = (pb_confidence + so_confidence) / 10.0  # Bonus modéré

        # Confiance finale : minimum 0.05 (5%) seulement si aucun signal
        final_confidence = weighted_contribution + correlation_bonus
        priority_synthesis['exploitation_confidence'] = max(self.config.minimum_confidence, final_confidence) if final_confidence > self.config.zero_value else self.config.zero_value

        return priority_synthesis




# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4160 à 4198
# Type: Méthode de la classe AZRCluster

    def _correlate_impair_with_so(self, isolated_impairs: List, consecutive_sequences: List, so_outcomes: List, total_hands: int) -> Dict:
        """Corrèle les IMPAIRS avec les conversions S/O"""
        correlation = {
            'prediction_strength': 0.0,
            'impair_s_count': 0,
            'impair_o_count': 0,
            'dominant_conversion': 'S'
        }

        total_correlations = 0
        s_count = 0

        # IMPAIRS isolés
        for pos in isolated_impairs:
            if pos - int(self.config.rollout_analyzer_position_offset) < len(so_outcomes):
                if so_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'S':
                    s_count += int(self.config.rollout_reward_valid_sequence_increment)
                total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        # Séquences d'IMPAIRS
        for seq in consecutive_sequences:
            for pos in seq:
                if pos - int(self.config.rollout_analyzer_position_offset) < len(so_outcomes):
                    if so_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'S':
                        s_count += int(self.config.rollout_reward_valid_sequence_increment)
                    total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        if total_correlations > 0:
            s_ratio = s_count / total_correlations
            correlation['prediction_strength'] = abs(s_ratio - self.config.rollout_analyzer_normality_threshold)
            correlation['impair_s_count'] = s_count
            correlation['impair_o_count'] = total_correlations - s_count

            if s_ratio > self.config.rollout_analyzer_normality_threshold:
                correlation['dominant_conversion'] = 'S'
            else:
                correlation['dominant_conversion'] = 'O'

        return correlation
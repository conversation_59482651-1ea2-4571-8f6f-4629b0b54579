MÉTHODE : _generate_bias_signals_summary_c2
LIGNE DÉBUT : 1328
SIGNATURE : def _generate_bias_signals_summary_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
================================================================================

    def _generate_bias_signals_summary_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
"""
    ADAPTATION BCT - _generate_bias_signals_summary_c2.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 C2 SPÉCIALISÉ - Génération signaux de biais avec spécialisation patterns courts
        """
        # Utiliser la méthode de base
        base_signals = self._generate_bias_signals_summary(bias_synthesis)

        # Ajouter les signaux spécialisés C2
        c2_signals = {
            'c2_short_patterns_signal': {
                'signal_strength': c2_specialization.get('specialization_bonus', self.config.zero_value),
                'patterns_detected': c2_specialization.get('short_patterns_detected', 0),
                'reactivity_score': c2_specialization.get('reactivity_score', self.config.zero_value),
                'fenetre_optimisee': c2_specialization.get('fenetre_recente_optimisee', 3)
            }
        }

        # Fusionner les signaux
        base_signals.update(c2_signals)
        base_signals['specialization_applied'] = 'C2_patterns_courts'

        return base_signals


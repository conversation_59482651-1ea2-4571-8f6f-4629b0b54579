# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 8084 à 8116
# Type: Méthode de la classe AZRCluster

    def _analyze_desync_sync_to_pbt_impact(self, desync_sync_seq: List[str], pbt_seq: List[str]) -> Dict:
        """Analyse impact DESYNC/SYNC → P/B (FOCUS P/B UNIQUEMENT)"""
        if len(desync_sync_seq) != len(pbt_seq):
            return {'alignment_error': True}

        # Calcul corrélations SYNC/DESYNC → P/B (exclure Ties de l'analyse d'impact)
        sync_p_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'SYNC' and pbt == 'P')
        sync_b_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'SYNC' and pbt == 'B')
        sync_t_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'SYNC' and pbt == 'T')

        desync_p_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'DESYNC' and pbt == 'P')
        desync_b_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'DESYNC' and pbt == 'B')
        desync_t_count = sum(1 for ds, pbt in zip(desync_sync_seq, pbt_seq) if ds == 'DESYNC' and pbt == 'T')

        # Totaux P/B uniquement (Ties exclus de l'analyse d'impact)
        total_sync_pb = sync_p_count + sync_b_count
        total_desync_pb = desync_p_count + desync_b_count

        return {
            # Impact sur P/B uniquement (Ties exclus de l'analyse d'impact)
            'sync_to_player': sync_p_count / max(1, total_sync_pb),
            'sync_to_banker': sync_b_count / max(1, total_sync_pb),
            'desync_to_player': desync_p_count / max(1, total_desync_pb),
            'desync_to_banker': desync_b_count / max(1, total_desync_pb),
            # Métadonnées pour traçabilité (incluant Ties pour information)
            'sync_pb_hands': total_sync_pb,
            'desync_pb_hands': total_desync_pb,
            'sync_tie_hands': sync_t_count,
            'desync_tie_hands': desync_t_count,
            'dominant_sync_outcome': 'P' if sync_p_count > sync_b_count else 'B',
            'dominant_desync_outcome': 'P' if desync_p_count > desync_b_count else 'B',
            'impact_strength': abs((sync_p_count / max(1, total_sync_pb)) - (desync_p_count / max(1, total_desync_pb)))
        }
MÉTHODE : calculate_rollout3_risk_factor
LIGNE DÉBUT : 4414
SIGNATURE : def calculate_rollout3_risk_factor(self, prediction_data: Dict, analyzer_report: Dict) -> float:
================================================================================

    def calculate_rollout3_risk_factor(self, prediction_data: Dict, analyzer_report: Dict) -> float:
"""
    ADAPTATION BCT - calculate_rollout3_risk_factor.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule le facteur de risque pour une prédiction du Rollout 3

        Args:
            prediction_data: Données de la prédiction
            analyzer_report: Rapport de l'analyseur

        Returns:
            float: Facteur de risque (0-1)
        """
        # Facteurs de risque basés sur l'analyse
        risk_factors = []

        # 1. Risque basé sur la qualité d'analyse
        analysis_quality = analyzer_report.get('sequence_metadata', {}).get('analysis_quality', self.config.probability_neutral)
        analysis_risk = self.config.one_value - analysis_quality  # Plus l'analyse est faible, plus le risque est élevé
        risk_factors.append(analysis_risk)

        # 2. Risque basé sur la confiance du cluster
        cluster_confidence = prediction_data.get('cluster_confidence', self.config.probability_neutral)
        confidence_risk = self.config.one_value - cluster_confidence
        risk_factors.append(confidence_risk)

        # 3. Risque basé sur le score d'évaluation
        evaluation_score = prediction_data.get('evaluation_score', self.config.probability_neutral)
        evaluation_risk = self.config.one_value - evaluation_score
        risk_factors.append(evaluation_risk)

        # 4. Risque basé sur la cohérence des signaux
        signals_summary = analyzer_report.get('signals_summary', {})
        overall_confidence = signals_summary.get('overall_confidence', self.config.probability_neutral)
        signals_risk = self.config.one_value - overall_confidence
        risk_factors.append(signals_risk)

        # Moyenne pondérée des facteurs de risque
        weights = [self.config.weight_30_percent, self.config.weight_25_percent, self.config.weight_25_percent, self.config.weight_20_percent]  # Pondération par importance
        weighted_risk = sum(risk * weight for risk, weight in zip(risk_factors, weights))

        return min(self.config.probability_clamp_max, max(self.config.probability_clamp_min, weighted_risk))


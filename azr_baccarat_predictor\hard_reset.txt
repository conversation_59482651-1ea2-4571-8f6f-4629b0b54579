# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14392 à 14449
# Type: Méthode de la classe AZRBaccaratInterface

    def hard_reset(self):
        """
        🔥 HARD RESET : Remet à zéro ABSOLUMENT TOUT

        Réinitialise :
        - Tous les indices de toutes les parties
        - Toute l'intelligence acquise
        - Tous les patterns découverts
        - Tous les baselines adaptatifs
        - Tous les fichiers de persistance
        - Toutes les métriques de performance

        ATTENTION : Cette action est IRRÉVERSIBLE !
        """
        from tkinter import messagebox

        # Demander confirmation à l'utilisateur
        confirm = messagebox.askyesno(
            "⚠️ HARD RESET - CONFIRMATION",
            "🔥 ATTENTION : HARD RESET va supprimer DÉFINITIVEMENT :\n\n"
            "• Toute l'intelligence acquise\n"
            "• Tous les patterns découverts\n"
            "• Tous les baselines adaptatifs\n"
            "• Toutes les métriques de performance\n"
            "• Tous les fichiers de sauvegarde\n\n"
            "Cette action est IRRÉVERSIBLE !\n\n"
            "Êtes-vous ABSOLUMENT sûr de vouloir continuer ?",
            icon='warning'
        )

        if not confirm:
            logger.info("🔥 HARD RESET annulé par l'utilisateur")
            return

        # Confirmation supplémentaire
        final_confirm = messagebox.askyesno(
            "🔥 DERNIÈRE CHANCE",
            "DERNIÈRE CHANCE !\n\n"
            "Vous allez perdre DÉFINITIVEMENT toute l'intelligence AZR.\n"
            "Le système repartira de ZÉRO.\n\n"
            "Confirmer le HARD RESET ?",
            icon='error'
        )

        if not final_confirm:
            logger.info("🔥 HARD RESET annulé à la dernière chance")
            return

        logger.warning("🔥 HARD RESET CONFIRMÉ - Suppression de toute l'intelligence AZR...")

        # 1. SOFT RESET d'abord (partie courante)
        self.soft_reset()

        # 2. HARD RESET du modèle AZR (toute l'intelligence)
        if self.azr_predictor:
            self.azr_predictor.hard_reset_intelligence()

        logger.warning("🔥 HARD RESET TERMINÉ - Système reparti de zéro")
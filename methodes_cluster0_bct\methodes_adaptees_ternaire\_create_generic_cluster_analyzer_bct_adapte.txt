MÉTHODE : _create_generic_cluster_analyzer
LIGNE DÉBUT : 1468
SIGNATURE : def _create_generic_cluster_analyzer(self, cluster_id: int, standardized_sequence: Dict) -> Dict:
================================================================================

    def _create_generic_cluster_analyzer(self, cluster_id: int, standardized_sequence: Dict) -> Dict:
"""
    ADAPTATION BCT - _create_generic_cluster_analyzer.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 ANALYSEUR GÉNÉRIQUE ALIGNÉ - Tous clusters suivent la même logique de base

        LOGIQUE DE RÉFÉRENCE COMMUNE :
        1. Extraction systématique des 5 index (IDENTIQUE pour tous)
        2. Analyse des corrélations 1,2,3 → 4,5 (IDENTIQUE pour tous)
        3. Application spécialisation EN PLUS de la logique de base
        4. Utilisation fenêtres récentes DANS le cadre de cette structure
        """
        try:
            analysis_start = time.time()

            # Récupérer les paramètres de spécialisation centralisés
            spec_params = self._get_cluster_specialization_params(cluster_id)

            # Extraction données complètes depuis brûlage (IDENTIQUE TOUS CLUSTERS)
            hands_data = standardized_sequence.get('hands_history', [])
            if not hands_data:
                return {'error': 'Aucune donnée historique disponible'}

            # ================================================================
            # PHASE 1 : EXTRACTION SYSTÉMATIQUE DES 5 INDEX (IDENTIQUE TOUS)
            # ================================================================

            position_types = []
            sync_states = []
            combined_states = []
            pb_outcomes = []
            so_outcomes = []

            # Extraction complète de TOUS les indices (IDENTIQUE TOUS CLUSTERS)
            for hand_number in range(int(self.config.one_value), len(hands_data) + int(self.config.one_value)):
                hand_data = hands_data[hand_number - int(self.config.rollout_analyzer_sequence_increment)]

                # Index 1 : PAIR/IMPAIR
                position_type = 'impair_5' if hand_number % int(self.config.two_value) == int(self.config.one_value) else ['pair_4', 'pair_6']
                position_types.append(position_type)

                # Index 2 : SYNC/DESYNC
                sync_state = getattr(hand_data, 'sync_state', 'SYNC')
                sync_states.append(sync_state)

                # Index 3 : COMBINED
                combined_state = getattr(hand_data, 'combined_state', f"{position_type}_{sync_state}")
                combined_states.append(combined_state)

                # Index 4 : P/B/T
                pbt_result = hand_data.pbt_result
                if pbt_result in ['P', 'B']:
                    pb_outcomes.append(pbt_result)

                # Index 5 : S/O
                so_result = getattr(hand_data, 'so_conversion', None)
                if so_result in ['S', 'O']:
                    so_outcomes.append(so_result)

            # ================================================================
            # PHASE 2 : ANALYSE DE BASE AVEC SPÉCIALISATION
            # ================================================================

            # Analyse IMPAIRS avec spécialisation cluster
            impair_bias_analysis = self._analyze_impair5_bias_specialized(
                hands_data, position_types, sync_states, combined_states,
                pb_outcomes, so_outcomes, cluster_id, spec_params
            )

            # Analyse PAIRS simplifiée (compatible tous clusters)
            pair_bias_analysis = {
                'exploitation_confidence': impair_bias_analysis.get('exploitation_confidence', 0.0) * 0.8,
                'pair_context_analysis': f'simplified_for_c{cluster_id}',
                f'c{cluster_id}_compatible': True
            }

            # Analyse SYNC/DESYNC avec spécialisation cluster
            sync_bias_analysis = self._analyze_sync_bias_specialized(hands_data, cluster_id, spec_params)

            # Analyse BIAIS COMBINÉS (IDENTIQUE TOUS CLUSTERS)
            combined_bias_analysis = self._analyze_combined_structural_bias(
                impair_bias_analysis, sync_bias_analysis, hands_data
            )

            # ================================================================
            # PHASE 3 : CORRÉLATIONS 1,2,3 → 4,5 (IDENTIQUE TOUS CLUSTERS)
            # ================================================================

            pb_correlation_analysis = self._correlate_bias_to_pb_variations(
                impair_bias_analysis, sync_bias_analysis, combined_bias_analysis, hands_data
            )

            so_correlation_analysis = self._correlate_bias_to_so_variations(
                pb_correlation_analysis, hands_data
            )

            # ================================================================
            # PHASE 4 : SPÉCIALISATION CLUSTER
            # ================================================================

            cluster_specialization = self._apply_cluster_specialization({
                'impair_bias': impair_bias_analysis,
                'pair_bias': pair_bias_analysis,
                'sync_bias': sync_bias_analysis,
                'combined_bias': combined_bias_analysis,
                'pb_correlation': pb_correlation_analysis,
                'so_correlation': so_correlation_analysis
            }, cluster_id, spec_params)

            # ================================================================
            # PHASE 5 : SYNTHÈSE FINALE AVEC SPÉCIALISATION
            # ================================================================

            bias_synthesis = {
                'exploitation_quality': (
                    impair_bias_analysis.get('exploitation_confidence', 0.0) +
                    sync_bias_analysis.get('exploitation_confidence', 0.0) +
                    combined_bias_analysis.get('exploitation_confidence', 0.0) +
                    cluster_specialization.get('specialization_bonus', 0.0)
                ) / 4,
                'strongest_bias': f'c{cluster_id}_{spec_params["type"]}',
                'exploitation_confidence': min(1.0, cluster_specialization.get('specialization_bonus', 0.0) + 0.5),
                'bias_persistence': cluster_specialization.get('cluster_score', 0.0)
            }

            # Génération des signaux avec spécialisation
            bias_signals_summary = {
                f'c{cluster_id}_{spec_params["type"]}': {
                    'signal_strength': cluster_specialization.get('specialization_bonus', 0.0),
                    'confidence': bias_synthesis['exploitation_confidence'],
                    'specialization': spec_params['type']
                }
            }

            bias_generation_guidance = {
                'primary_guidance': f'focus_{spec_params["type"]}',
                'fenetre_optimale': self.config.get_cluster_recent_window_size(cluster_id),
                'cluster_mode': spec_params['type']
            }

            bias_quick_access = {
                'cluster_type': f'C{cluster_id}_{spec_params["type"]}',
                'specialization_bonus': cluster_specialization.get('specialization_bonus', 0.0),
                'fenetre_optimisee': self.config.get_cluster_recent_window_size(cluster_id)
            }

            # Rapport final OPTIMISÉ pour exploitation de biais avec spécialisation
            analyzer_report = {
                # SIGNAUX DE BIAIS EXPLOITABLES avec spécialisation
                'bias_signals_summary': bias_signals_summary,
                'bias_generation_guidance': bias_generation_guidance,
                'bias_quick_access': bias_quick_access,

                # ANALYSE DÉTAILLÉE DES BIAIS STRUCTURELS (IDENTIQUE TOUS)
                'structural_bias_analysis': {
                    'impair_consecutive_bias': impair_bias_analysis,
                    'sync_alternation_bias': sync_bias_analysis,
                    'combined_structural_bias': combined_bias_analysis,
                    'pb_correlation_bias': pb_correlation_analysis,
                    'so_correlation_bias': so_correlation_analysis
                },
                'bias_synthesis': bias_synthesis,

                # SPÉCIALISATION CLUSTER
                f'c{cluster_id}_specialization': cluster_specialization,
                'cluster_specialization_type': spec_params['type'],

                'exploitation_metadata': {
                    'total_hands_analyzed': len(hands_data),
                    'bias_exploitation_quality': bias_synthesis.get('exploitation_quality', self.config.zero_value),
                    'strongest_bias_detected': bias_synthesis.get('strongest_bias', {}),
                    'exploitation_confidence': bias_synthesis.get('exploitation_confidence', self.config.zero_value),
                    'bias_persistence_score': bias_synthesis.get('bias_persistence', self.config.zero_value),
                    f'c{cluster_id}_specialization_bonus': cluster_specialization.get('specialization_bonus', self.config.zero_value)
                },
                'execution_time_ms': (time.time() - analysis_start) * 1000,
                'cluster_id': cluster_id,
                'analysis_type': f'structural_bias_exploitation_c{cluster_id}_{spec_params["type"]}'
            }

            return analyzer_report

        except Exception as e:
            logger.error(f"Erreur rollout analyzer générique cluster {cluster_id}: {e}")
            return {'error': str(e)}


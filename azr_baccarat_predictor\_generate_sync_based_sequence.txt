# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 11810 à 12012
# Type: Méthode de la classe AZRCluster

    def _generate_sync_based_sequence(self, generation_space: Dict) -> List[Dict]:
        """
        Génère séquence basée sur exploitation patterns synchronisation SYNC/DESYNC

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Exploite les patterns de synchronisation détectés pour optimiser les prédictions

        Args:
            generation_space: Espace de génération complet avec contraintes et guidance

        Returns:
            Liste de mains prédites avec stratégie SYNC/DESYNC optimisée
        """

        sequence_length = self.config.rollout2_fixed_length  # Longueur fixe selon spécifications AZR
        sequence = []

        # ================================================================
        # 1. EXTRACTION DES CONTRAINTES SYNC/DESYNC
        # ================================================================

        enhanced_constraints = generation_space.get('enhanced_constraints', {})
        cross_impact_guidance = generation_space.get('cross_impact_guidance', {})

        # Contraintes synchronisation
        maintain_sync = enhanced_constraints.get('maintain_sync', 0)
        expect_chaos = enhanced_constraints.get('expect_chaos', 0)
        desync_pattern_length = enhanced_constraints.get('desync_pattern_length', 3)

        # Guidance impacts croisés SYNC/DESYNC → S/O
        sync_desync_so_guidance = cross_impact_guidance.get('sync_desync_so', {})

        # ================================================================
        # 2. STRATÉGIE DE SYNCHRONISATION
        # ================================================================

        # Déterminer stratégie dominante
        if maintain_sync > self.config.rollout2_confidence_value_high:
            sync_strategy = 'MAINTAIN_HIGH_SYNC'
            base_sync_probability = maintain_sync
        elif expect_chaos > self.config.rollout2_confidence_value_high:
            sync_strategy = 'ADAPT_TO_CHAOS'
            base_sync_probability = 1.0 - expect_chaos
        else:
            sync_strategy = 'BALANCED_SYNC'
            base_sync_probability = 0.6  # Légèrement favorable à SYNC

        # ================================================================
        # 3. GÉNÉRATION SÉQUENCE OPTIMISÉE
        # ================================================================

        # Pattern attendu de base (alternance P/B)
        expected_pattern = ['P', 'B']

        for i in range(sequence_length):
            hand_number = i + 1
            position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'

            # ================================================================
            # 3.1. PRÉDICTION BASÉE SUR STRATÉGIE SYNC
            # ================================================================

            expected_outcome = expected_pattern[i % len(expected_pattern)]

            # Ajustement probabilité sync selon position dans séquence
            current_sync_probability = base_sync_probability

            # Stratégie MAINTAIN_HIGH_SYNC
            if sync_strategy == 'MAINTAIN_HIGH_SYNC':
                # Favoriser continuité synchronisation
                current_sync_probability = min(base_sync_probability  + self.config.rollout2_adjustment_small, 0.95)
                predicted_pb = expected_outcome  # Suivre pattern attendu
                sync_state = 'SYNC'
                pb_confidence = current_sync_probability

            # Stratégie ADAPT_TO_CHAOS
            elif sync_strategy == 'ADAPT_TO_CHAOS':
                # Adapter à désynchronisation
                if i % int(desync_pattern_length) == 0:
                    # Moment de rupture potentielle
                    predicted_pb = 'B' if expected_outcome == 'P' else 'P'  # Inverser
                    sync_state = 'DESYNC'
                    pb_confidence = expect_chaos
                else:
                    # Suivre pattern attendu
                    predicted_pb = expected_outcome
                    sync_state = 'SYNC'
                    pb_confidence = 1.0 - expect_chaos

            # Stratégie BALANCED_SYNC
            else:
                # Équilibre entre sync et adaptation
                if i < sequence_length // 2:
                    # Première moitié : favoriser SYNC
                    predicted_pb = expected_outcome
                    sync_state = 'SYNC'
                    pb_confidence = current_sync_probability
                else:
                    # Seconde moitié : légère adaptation
                    if i % 3 == 0:  # Rupture occasionnelle
                        predicted_pb = 'B' if expected_outcome == 'P' else 'P'
                        sync_state = 'DESYNC'
                        pb_confidence = self.config.rollout2_confidence_value_high
                    else:
                        predicted_pb = expected_outcome
                        sync_state = 'SYNC'
                        pb_confidence = current_sync_probability

            # ================================================================
            # 3.2. PRÉDICTION S/O BASÉE SUR GUIDANCE SYNC/DESYNC
            # ================================================================

            predicted_so = 'S'  # Défaut
            so_confidence = self.config.rollout3_neutral_evaluation_value  # Confiance de base

            if sync_desync_so_guidance:
                # Exploiter guidance SYNC/DESYNC → S/O
                so_pattern = sync_desync_so_guidance.get('pattern', '')
                so_strength = sync_desync_so_guidance.get('strength', 0)

                if sync_state == 'SYNC':
                    if 'SYNC→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'SYNC→O' in so_pattern:
                        predicted_so = 'O'
                elif sync_state == 'DESYNC':
                    if 'DESYNC→S' in so_pattern:
                        predicted_so = 'S'
                    elif 'DESYNC→O' in so_pattern:
                        predicted_so = 'O'

                so_confidence = self.config.rollout3_neutral_evaluation_value + so_strength
                so_confidence = min(so_confidence, 0.9)
            else:
                # Logique par défaut : SYNC favorise S, DESYNC favorise O
                if sync_state == 'SYNC':
                    predicted_so = 'S'
                    so_confidence = self.config.cluster_base_confidence_medium
                else:
                    predicted_so = 'O'
                    so_confidence = self.config.rollout2_confidence_value_high

            # ================================================================
            # 3.3. AJUSTEMENTS BASÉS SUR CONTRAINTES TEMPORELLES
            # ================================================================

            # Ajustement confiance selon évolution temporelle
            temporal_hints = generation_space.get('temporal_evolution_hints', {})

            if 'strong_evolution' in temporal_hints:
                evolution_strength = temporal_hints['strong_evolution']
                # Renforcer confiance si évolution temporelle forte
                pb_confidence = min(pb_confidence + evolution_strength * self.config.rollout2_adjustment_small, 0.95)
                so_confidence = min(so_confidence + evolution_strength * self.config.rollout2_adjustment_medium, 0.9)

            # Ajustement selon phase optimale
            optimal_phase = temporal_hints.get('optimal_phase', 'unknown')
            if optimal_phase == 'late_game' and i >= sequence_length * self.config.rollout2_confidence_value_high:
                # Renforcer confiance en fin de séquence
                pb_confidence = min(pb_confidence + self.config.rollout2_adjustment_small, 0.95)
                so_confidence = min(so_confidence + self.config.rollout2_adjustment_small, 0.9)
            elif optimal_phase == 'early_game' and i < sequence_length * self.config.rollout3_quality_bonus_small:
                # Renforcer confiance en début de séquence
                pb_confidence = min(pb_confidence + self.config.rollout2_adjustment_small, 0.95)
                so_confidence = min(so_confidence + self.config.rollout2_adjustment_small, 0.9)

            # ================================================================
            # 3.4. CONSTRUCTION MAIN PRÉDITE
            # ================================================================

            # Confiance sync basée sur prédiction P/B
            sync_confidence = pb_confidence if sync_state == 'SYNC' else (1.0 - pb_confidence)

            # Confiance globale pondérée (P/B poids 2, S/O poids 3)
            global_confidence = (pb_confidence * 2 + so_confidence * 3) / 5

            hand = {
                'hand_number': hand_number,
                'position_type': position_type,
                'predicted_pbt': predicted_pb,
                'predicted_so': predicted_so,
                'sync_state': sync_state,
                'pb_confidence': pb_confidence,
                'so_confidence': so_confidence,
                'sync_confidence': sync_confidence,
                'global_confidence': global_confidence,
                'strategy_source': 'sync_based',
                'sync_strategy': sync_strategy,
                'expected_outcome': expected_outcome,
                'guidance_used': {
                    'sync_desync_so': bool(sync_desync_so_guidance),
                    'temporal_evolution': bool(temporal_hints)
                },
                'constraints_applied': {
                    'maintain_sync': maintain_sync,
                    'expect_chaos': expect_chaos,
                    'desync_pattern_length': desync_pattern_length
                }
            }

            sequence.append(hand)

        return sequence
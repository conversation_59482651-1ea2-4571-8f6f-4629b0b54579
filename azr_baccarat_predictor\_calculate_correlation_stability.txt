# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10171 à 10182
# Type: Méthode de la classe AZRCluster

    def _calculate_correlation_stability(self, phase_strengths: Dict) -> float:
        """Calcule la stabilité des corrélations entre phases"""

        values = list(phase_strengths.values())
        if len(values) < 2:
            return 0.0

        # Stabilité = inverse de la variance (plus stable = moins de variance)
        variance = self._calculate_variance(values)
        stability = 1.0 / (1.0 + variance)  # Normalisation

        return stability
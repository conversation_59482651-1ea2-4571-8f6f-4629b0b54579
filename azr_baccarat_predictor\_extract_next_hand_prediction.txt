# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6526 à 6558
# Type: Méthode de la classe AZRCluster

    def _extract_next_hand_prediction(self, best_sequence: Dict) -> str:
        """
        Extrait la prédiction pour la prochaine main de la meilleure séquence

        FOCUS : Prédiction immédiate pour la prochaine main
        """
        sequence_data = best_sequence.get('sequence_data', [])

        if sequence_data:
            first_element = sequence_data[0]

            # Si c'est un dictionnaire enrichi, extraire predicted_pbt
            if isinstance(first_element, dict):
                # Essayer plusieurs clés possibles
                for key in ['predicted_pbt', 'pbt_result', 'result']:
                    if key in first_element:
                        value = first_element[key]
                        # S'assurer que c'est une chaîne simple P/B
                        if isinstance(value, str) and value in ['P', 'B']:
                            return value
                # Fallback si aucune clé valide trouvée
                return 'B'
            # Si c'est une chaîne simple
            elif isinstance(first_element, str):
                # S'assurer que c'est P ou B
                if first_element in ['P', 'B']:
                    return first_element
                else:
                    return 'B'  # Fallback pour chaînes invalides
            else:
                return 'B'  # Fallback pour types inattendus
        else:
            return 'B'  # Fallback par défaut
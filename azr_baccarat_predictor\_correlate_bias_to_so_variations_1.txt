# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4888 à 4956
# Type: Méthode de la classe AZRCluster
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict:
        """
        Corrèle les biais P/B avec les variations S/O

        LOGIQUE ANTI-MOYENNES :
        - Dérive les signaux S/O à partir des signaux P/B
        - Mesure l'impact sur les transitions S/O
        - Génère des signaux prédictifs pour S/O
        """
        so_correlation = {
            'pb_to_so_correlation': 0.0,
            'so_prediction_signal': {},
            'so_prediction_confidence': 0.0,
            'transition_bias_strength': 0.0
        }

        # Extraction des transitions S/O
        pb_outcomes = []
        so_outcomes = []

        for hand in hands_data:
            if hand.pbt_result in ['P', 'B']:
                pb_outcomes.append(hand.pbt_result)

        # Calcul S/O sur transitions P/B
        for i in range(1, len(pb_outcomes)):
            current = pb_outcomes[i]
            previous = pb_outcomes[i-1]

            if current == previous:
                so_outcomes.append('S')
            else:
                so_outcomes.append('O')

        if len(so_outcomes) < 2:
            return so_correlation

        # Corrélation P/B → S/O
        pb_signal_strength = pb_correlation.get('pb_prediction_confidence', 0.0)
        strongest_pb_signal = pb_correlation.get('strongest_pb_signal', {})

        if pb_signal_strength > self.config.signal_very_strong_threshold:
            # Dérivation du signal S/O à partir du signal P/B
            # Plus le signal P/B est fort, plus l'impact sur S/O est prévisible
            so_correlation['pb_to_so_correlation'] = pb_signal_strength * self.config.rollout2_base_confidence_high  # Facteur de transmission

            # Signal S/O dérivé
            so_correlation['so_prediction_signal'] = {
                'derived_from': strongest_pb_signal.get('signal_type', 'unknown'),
                'signal_strength': so_correlation['pb_to_so_correlation'],
                'prediction_type': 'transition_based',
                'exploitation_ready': so_correlation['pb_to_so_correlation'] > 0.25
            }

            # Confiance prédiction S/O
            so_correlation['so_prediction_confidence'] = min(1.0, so_correlation['pb_to_so_correlation'])

        # Mesure du biais de transition
        s_count = so_outcomes.count('S')
        o_count = so_outcomes.count('O')
        total_transitions = len(so_outcomes)

        if total_transitions > 0:
            # Écart par rapport à l'équilibre 50/50
            s_ratio = s_count / total_transitions
            transition_deviation = abs(s_ratio - 0.5)
            so_correlation['transition_bias_strength'] = transition_deviation

        return so_correlation
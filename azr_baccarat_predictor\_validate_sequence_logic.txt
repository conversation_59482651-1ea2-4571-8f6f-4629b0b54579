# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6123 à 6182
# Type: Méthode de la classe AZRCluster

    def _validate_sequence_logic(self, sequence: Dict, analyzer_report: Dict) -> float:
        """
        Valide la logique d'une séquence par rapport aux découvertes du Rollout 1

        FOCUS : S'assurer que la séquence respecte les patterns découverts
        """
        logic_score = 0.5  # Score de base

        sequence_data = sequence.get('sequence_data', [])
        if not sequence_data:
            return 0.0

        # 1. Validation contre les corrélations dominantes
        synthesis = analyzer_report.get('synthesis', {})
        dominant_correlations = synthesis.get('dominant_correlations', [])

        correlation_validation = 0.0
        for correlation in dominant_correlations:
            corr_type = correlation.get('type', '')
            corr_strength = correlation.get('strength', 0.0)

            # Vérifier si la séquence respecte cette corrélation
            if 'IMPAIR_TO_PLAYER' in corr_type:
                # Vérifier si les positions impaires favorisent P
                impair_positions = [i for i in range(len(sequence_data)) if (i + 1) % 2 == 1]
                if impair_positions:
                    p_in_impair = sum(1 for i in impair_positions if sequence_data[i] == 'P')
                    impair_p_ratio = p_in_impair / len(impair_positions)
                    if impair_p_ratio >= 0.6:  # Respecte la corrélation
                        correlation_validation += corr_strength * self.config.rollout3_neutral_evaluation_value

            elif 'PAIR_TO_BANKER' in corr_type:
                # Vérifier si les positions paires favorisent B
                pair_positions = [i for i in range(len(sequence_data)) if (i + 1) % 2 == 0]
                if pair_positions:
                    b_in_pair = sum(1 for i in pair_positions if sequence_data[i] == 'B')
                    pair_b_ratio = b_in_pair / len(pair_positions)
                    if pair_b_ratio >= 0.6:  # Respecte la corrélation
                        correlation_validation += corr_strength * self.config.rollout3_neutral_evaluation_value

        # 2. Validation contre les patterns S/O
        quick_access = analyzer_report.get('quick_access', {})
        next_prediction_so = quick_access.get('next_prediction_so')

        so_validation = 0.0
        if next_prediction_so and len(sequence_data) >= 2:
            # Vérifier si la séquence respecte la prédiction S/O
            last_result = sequence_data[0]  # Premier élément prédit
            second_result = sequence_data[1] if len(sequence_data) > 1 else None

            if second_result:
                if next_prediction_so == 'S' and last_result == second_result:
                    so_validation = 0.3
                elif next_prediction_so == 'O' and last_result != second_result:
                    so_validation = 0.3

        # 3. Score de logique final
        logic_score = min(1.0, 0.5 + correlation_validation + so_validation)

        return logic_score
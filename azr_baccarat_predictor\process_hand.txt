# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14248 à 14346
# Type: Méthode de la classe AZRBaccaratInterface

    def process_hand(self, result: str, parity: str):
        """Traite une manche complète avec résultat et parité"""
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser les cartes brûlées")
            return

        # Vérification des limites selon la logique de référence AZR
        current_pb_hands = self.current_pb_number  # Nombre de manches P/B déjà enregistrées
        current_so_conversions = len([h for h in self.current_game['hands'] if h.get('so_conversion') in ['S', 'O']])

        # Vérifier limite configurée de manches par partie
        max_manches = self.config.max_manches_per_game

        if result in ['PLAYER', 'BANKER'] and current_pb_hands >= max_manches:
            messagebox.showwarning("🚫 Limite atteinte",
                                 f"Maximum {max_manches} manches par partie\n\n"
                                 f"💾 Pensez à sauvegarder avant de reset !\n"
                                 f"🔄 Utilisez Soft Reset pour une nouvelle partie")
            return

        # Vérifier limite S/O (max_manches - 1 car S/O commence à la manche 2)
        max_so_conversions = max_manches - 1
        if result in ['PLAYER', 'BANKER'] and current_so_conversions >= max_so_conversions:
            messagebox.showinfo("🏁 Partie terminée",
                               f"✅ {max_so_conversions} manches S/O atteintes - Partie terminée\n\n"
                               f"💾 N'oubliez pas de sauvegarder l'apprentissage !\n"
                               f"🔄 Utilisez Soft Reset pour une nouvelle partie")
            self._disable_all_buttons_except_reset()
            return

        # LOGIQUE DE RÉFÉRENCE AZR BACCARAT
        # Déterminer le numéro de manche P/B selon la logique exacte
        pb_number = self.current_pb_number

        # CORRECTION: Mettre à jour l'état de synchronisation AVANT de créer hand_data
        if parity == 'IMPAIR':
            self.current_sync_state = 'DESYNC' if self.current_sync_state == 'SYNC' else 'SYNC'

        # Calculer la conversion S/O
        so_conversion = '--'
        if result in ['PLAYER', 'BANKER']:
            if self.last_pb_result is not None:
                if result == self.last_pb_result:
                    so_conversion = 'S'
                else:
                    so_conversion = 'O'

            # Mettre à jour pour la prochaine manche
            self.last_pb_result = result
            self.last_so_conversion = so_conversion

            # LOGIQUE CORRECTE : Incrémenter APRÈS enregistrement P/B (manche terminée)
            self.current_pb_number += self.config.one_value

        # Créer les données de la manche avec l'état correct
        hand_data = {
            'pb_hand_number': pb_number,
            'result': result,
            'parity': parity,
            'sync_state': self.current_sync_state,
            'so_conversion': so_conversion,
            'combined_state': f"{parity}_{self.current_sync_state}"
        }

        # Ajouter à la partie
        self.current_game['hands'].append(hand_data)

        # Mettre à jour les statistiques
        self.current_game['statistics']['total_hands'] += self.config.one_value
        if result in ['PLAYER', 'BANKER']:
            self.current_game['statistics']['pb_hands'] += self.config.one_value
            if so_conversion != '--':
                self.current_game['statistics']['so_conversions'] += self.config.one_value
        else:
            self.current_game['statistics']['tie_hands'] += self.config.one_value

        # Envoyer au modèle AZR et obtenir la prédiction
        prediction = "Attendre"
        if self.azr_predictor:
            prediction = self.azr_predictor.receive_hand_data(hand_data)

        # Mettre à jour l'affichage du compteur avec limite
        next_manche_to_record = self.current_pb_number + 1
        max_manches = self.config.max_manches_per_game
        manches_restantes = max_manches - self.current_pb_number

        self.counter_label.config(
            text=f"Manche P/B: {next_manche_to_record} / {max_manches} (reste: {manches_restantes})"
        )

        # Afficher la prédiction pour la manche suivante
        if prediction in ['S', 'O']:
            prediction_text = f"Prédiction: {prediction}"
            color = "green" if prediction == "S" else "red"
            self.prediction_label.config(text=prediction_text, foreground=color)
        else:
            self.prediction_label.config(text="Prédiction: Attendre", foreground="blue")

        logger.info(f"✅ Manche traitée: P/B#{pb_number} {result} {parity} {hand_data['sync_state']} {so_conversion} → Prédiction: {prediction}")
MÉTHODE : _calculate_variance
LIGNE DÉBUT : 7720
SIGNATURE : def _calculate_variance(self, values: List[float]) -> float:
================================================================================

    def _calculate_variance(self, values: List[float]) -> float:
"""
    ADAPTATION BCT - _calculate_variance.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la variance d'une liste de valeurs"""

        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)

        return variance


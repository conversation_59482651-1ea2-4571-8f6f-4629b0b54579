# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17429 à 17565
# Type: Méthode de la classe AZRBaccaratPredictor

    def _analyze_pair_priority_2(self, hands_data: List, impair_analysis: Dict) -> Dict:
        """
        PRIORITÉ 2 : Analyse PAIRS en contexte des IMPAIRS

        NOUVELLE LOGIQUE COMPLÉMENTAIRE :
        - Analyse PAIRS comme information contextuelle
        - Séquences PAIR comme patterns de normalité
        - Ruptures PAIR comme signaux secondaires
        - Interprétation en fonction de PRIORITÉ 1
        """
        import statistics

        pair_analysis = {
            'isolated_pairs': [],                # PAIRS isolés analysés
            'consecutive_pair_sequences': [],    # Séquences de PAIRS
            'pair_context_scores': [],           # Scores contextuels
            'pair_support_signals': [],          # Signaux de support
            'normalcy_patterns': {},             # Patterns de normalité
            'break_patterns': {},                # Patterns de rupture
            'priority_2_confidence': 0.0        # Confiance de support
        }

        # Récupérer les signaux IMPAIR de priorité 1
        impair_signals = impair_analysis.get('priority_1_signals', [])
        total_impair_attention = sum(signal['attention_level'] for signal in impair_signals)

        # Extraction des positions PAIR
        position_types = []
        for hand_number in range(1, len(hands_data) + 1):
            position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'
            position_types.append(position_type)

        # Identifier PAIRS isolés et séquences
        isolated_pairs = []
        consecutive_sequences = []
        current_sequence = []

        for i, pos_type in enumerate(position_types):
            if pos_type == 'PAIR':
                current_sequence.append(i + self.config.rollout_analyzer_sequence_increment)  # Position dans la partie
            else:
                if current_sequence:
                    if len(current_sequence) == 1:
                        isolated_pairs.append(current_sequence[0])
                    else:
                        consecutive_sequences.append(current_sequence)
                    current_sequence = []

        # Fermer la dernière séquence
        if current_sequence:
            if len(current_sequence) == 1:
                isolated_pairs.append(current_sequence[0])
            else:
                consecutive_sequences.append(current_sequence)

        pair_analysis['isolated_pairs'] = isolated_pairs
        pair_analysis['consecutive_pair_sequences'] = consecutive_sequences

        # ================================================================
        # CALCUL SCORES CONTEXTUELS (EN FONCTION DES IMPAIRS)
        # ================================================================

        context_scores = []

        # 1. Scores pour PAIRS isolés (contexte de base)
        for isolated_pos in isolated_pairs:
            # Valeur contextuelle basée sur proximité avec IMPAIRS
            context_value = self._calculate_pair_context_value(isolated_pos, impair_signals)

            context_score = {
                'type': 'isolated_pair',
                'position': isolated_pos,
                'context_level': context_value,
                'normalcy_factor': 'standard',  # PAIR = normal
                'support_strength': self.config.rollout_analyzer_support_min_weight + (context_value * self.config.rollout2_adjustment_large)  # Support minimum
            }
            context_scores.append(context_score)

        # 2. Scores pour séquences de PAIRS (normalité progressive)
        for seq in consecutive_sequences:
            seq_length = len(seq)
            # Normalité croissante : plus la séquence est longue, plus c'est "normal"
            normalcy_level = min(self.config.correlation_player_value, seq_length / self.config.rollout_analyzer_normalcy_divisor)  # Normalisation configurable

            context_score = {
                'type': 'consecutive_pairs',
                'positions': seq,
                'sequence_length': seq_length,
                'normalcy_level': normalcy_level,
                'normalcy_factor': 'high' if seq_length >= 6 else 'standard',
                'support_strength': self.config.rollout2_adjustment_small + (normalcy_level * self.config.rollout2_adjustment_medium)  # Support faible mais stable
            }
            context_scores.append(context_score)

        pair_analysis['pair_context_scores'] = context_scores

        # ================================================================
        # GÉNÉRATION SIGNAUX DE SUPPORT PRIORITÉ 2
        # ================================================================

        support_signals = []
        total_support = 0.0

        # Générer signaux de support pour chaque score contextuel
        for score in context_scores:
            # Support inversement proportionnel à l'attention IMPAIR
            support_weight = max(self.config.rollout_analyzer_support_min_weight, self.config.correlation_player_value - (total_impair_attention / self.config.rollout_analyzer_support_max_reduction))

            signal = {
                'signal_type': score['type'],
                'context_level': score.get('context_level', score.get('normalcy_level', 0.5)),
                'support_strength': score['support_strength'] * support_weight,
                'normalcy_factor': score['normalcy_factor'],
                'complements_impair': score['support_strength'] > 0.2
            }
            support_signals.append(signal)
            total_support += signal['support_strength']

        pair_analysis['pair_support_signals'] = support_signals

        # ================================================================
        # CONFIANCE DE SUPPORT (TOUJOURS COMPLÉMENTAIRE)
        # ================================================================

        # Confiance basée sur le support total (peut être 0 si pas de support)
        base_support = min(self.config.rollout_analyzer_normality_threshold, total_support / self.config.rollout2_adjustment_large) if total_support > 0 else 0.0

        # Bonus pour complémentarité avec IMPAIRS
        complementarity_bonus = 0.0
        if total_impair_attention > 0:
            # Plus les IMPAIRS sont forts, plus le support PAIR est précieux
            complementarity_bonus = min(self.config.rollout2_adjustment_large, total_impair_attention / self.config.rollout_analyzer_complementarity_divisor)

        # Confiance finale : toujours secondaire aux IMPAIRS
        pair_analysis['priority_2_confidence'] = base_support + complementarity_bonus

        return pair_analysis
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7183 à 7222
# Type: Méthode de la classe AZRCluster

    def _generate_so_based_sequence(self, target_outcome: str, sequence_length: int, generation_space: Dict) -> List[str]:
        """
        Génère une séquence basée sur prédiction S/O (Same/Opposite)

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            target_outcome: 'S' pour Same, 'O' pour Opposite
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Récupérer le dernier résultat P/B du contexte
        last_pb_result = None
        pbt_sequence = generation_space.get('indices_analysis', {}).get('pbt', {}).get('pbt_sequence', [])

        if pbt_sequence:
            # Trouver le dernier résultat P/B (ignorer les Ties)
            for result in reversed(pbt_sequence):
                if result in ['P', 'B']:
                    last_pb_result = result
                    break

        if not last_pb_result:
            last_pb_result = 'P'  # Valeur par défaut

        # Générer la séquence selon la prédiction S/O (longueur fixe 4)
        for i in range(self.config.rollout2_fixed_length):
            if target_outcome == 'S':
                # Same : répéter le même résultat
                next_result = last_pb_result
            else:  # target_outcome == 'O'
                # Opposite : alterner
                next_result = 'B' if last_pb_result == 'P' else 'P'

            sequence.append(next_result)
            last_pb_result = next_result  # Mise à jour pour la prochaine itération

        return sequence
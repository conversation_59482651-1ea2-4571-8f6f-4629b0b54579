# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10212 à 10226
# Type: Méthode de la classe AZRCluster

    def _calculate_evolution_strength(self, trends: Dict) -> float:
        """Calcule la force globale d'évolution des corrélations"""

        if not trends:
            return 0.0

        total_strength = 0.0
        valid_trends = 0

        for trend_data in trends.values():
            if 'trend_strength' in trend_data:
                total_strength += trend_data['trend_strength']
                valid_trends += 1

        return total_strength / valid_trends if valid_trends > 0 else 0.0
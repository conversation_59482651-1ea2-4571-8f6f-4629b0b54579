# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 16015 à 16034
# Type: Méthode de la classe AZRBaccaratPredictor

    def _analyze_sync_desync_evolution(self, sync_desync_sequence: List[str]) -> float:
        """Analyse l'évolution SYNC/DESYNC"""
        if len(sync_desync_sequence) < self.config.two_value:
            return self.config.probability_neutral

        # Compter les états récents
        recent_states = sync_desync_sequence[-self.config.five_value:] if len(sync_desync_sequence) >= self.config.five_value else sync_desync_sequence
        sync_count = recent_states.count('SYNC')
        desync_count = recent_states.count('DESYNC')

        # Tendance actuelle
        current_state = sync_desync_sequence[-self.config.one_value]

        # Influence basée sur la dominance d'état
        if sync_count > desync_count and current_state == 'SYNC':
            return self.config.probability_neutral + self.config.small_increment
        elif desync_count > sync_count and current_state == 'DESYNC':
            return self.config.probability_neutral - self.config.small_increment
        else:
            return self.config.probability_neutral
MÉTHODE : _analyze_desync_periods_impact
LIGNE DÉBUT : 6684
SIGNATURE : def _analyze_desync_periods_impact(self, desync_periods: List[Dict], pbt_seq: List[str], so_seq: List[str]) -> Dict:
================================================================================

    def _analyze_desync_periods_impact(self, desync_periods: List[Dict], pbt_seq: List[str], so_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _analyze_desync_periods_impact.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Analyse l'impact des périodes de DÉSYNCHRONISATION sur P/B (sans TIE) et S/O

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Analyse comment les périodes DESYNC affectent les prédictions

        Args:
            desync_periods: Liste des périodes de désynchronisation détectées
                           Format: [{'start': int, 'end': int, 'length': int}, ...]
            pbt_seq: Séquence index 4 (P/B/T)
            so_seq: Séquence index 5 (S/O)

        Returns:
            Dictionnaire avec analyse des impacts des périodes DESYNC
        """

        desync_impact = {
            'periods_analysis': {},           # Analyse de chaque période DESYNC
            'length_impact_analysis': {},     # Impact selon longueur des périodes
            'global_desync_impact': {},       # Impact global des périodes DESYNC
            'desync_strength_metrics': {}     # Métriques de force des périodes DESYNC
        }

        if not desync_periods or len(desync_periods) == 0:
            return {
                'periods_analysis': {},
                'length_impact_analysis': {},
                'global_desync_impact': {
                    'no_desync_periods': True,
                    'impact_strength': 0.0
                },
                'desync_strength_metrics': {
                    'total_periods': 0,
                    'average_length': 0.0,
                    'max_length': 0,
                    'desync_coverage': 0.0
                }
            }

        # ================================================================
        # 1. ANALYSE DE CHAQUE PÉRIODE DESYNC INDIVIDUELLEMENT
        # ================================================================

        for i, period in enumerate(desync_periods):
            period_key = f"period_{i+1}"
            start_pos = period['start']
            end_pos = period['end']
            length = period['length']

            # Impact sur P/B pendant cette période DESYNC (sans TIE)
            pb_during_period = []
            for pos in range(start_pos, min(end_pos + 1, len(pbt_seq))):
                if pbt_seq[pos] in ['P', 'B']:  # Exclure TIE
                    pb_during_period.append(pbt_seq[pos])

            # Impact sur S/O pendant cette période DESYNC
            so_during_period = []
            for pos in range(start_pos, min(end_pos + 1, len(so_seq) + 1)):
                # S/O commence à manche 2, donc ajustement
                if pos > 0 and pos - 1 < len(so_seq):
                    so_during_period.append(so_seq[pos - 1])

            # Analyse P/B pour cette période
            pb_analysis = {}
            if pb_during_period:
                p_count = pb_during_period.count('P')
                b_count = pb_during_period.count('B')
                total_pb = p_count + b_count

                pb_analysis = {
                    'player_ratio': p_count / total_pb,
                    'banker_ratio': b_count / total_pb,
                    'dominant_pb': 'P' if p_count > b_count else 'B',
                    'pb_sample_size': total_pb,
                    'pb_deviation_from_50_50': abs((p_count / total_pb) - 0.5)
                }

            # Analyse S/O pour cette période
            so_analysis = {}
            if so_during_period:
                s_count = so_during_period.count('S')
                o_count = so_during_period.count('O')
                total_so = s_count + o_count

                so_analysis = {
                    'same_ratio': s_count / total_so,
                    'opposite_ratio': o_count / total_so,
                    'dominant_so': 'S' if s_count > o_count else 'O',
                    'so_sample_size': total_so,
                    'so_deviation_from_50_50': abs((s_count / total_so) - 0.5)
                }

            # Stocker analyse de cette période
            desync_impact['periods_analysis'][period_key] = {
                'period_info': period,
                'pb_impact': pb_analysis,
                'so_impact': so_analysis,
                'period_strength': max(
                    pb_analysis.get('pb_deviation_from_50_50', 0),
                    so_analysis.get('so_deviation_from_50_50', 0)
                )
            }

        # ================================================================
        # 2. ANALYSE IMPACT SELON LONGUEUR DES PÉRIODES DESYNC
        # ================================================================

        # Grouper périodes par longueur
        periods_by_length = {}
        for period in desync_periods:
            length = period['length']
            if length not in periods_by_length:
                periods_by_length[length] = []
            periods_by_length[length].append(period)

        # Analyser impact pour chaque longueur
        for length, periods_of_length in periods_by_length.items():
            if len(periods_of_length) >= 1:  # Au moins 1 période de cette longueur

                # Collecter tous les P/B pendant périodes de cette longueur (sans TIE)
                all_pb_for_length = []
                all_so_for_length = []

                for period in periods_of_length:
                    # P/B pendant cette période
                    for pos in range(period['start'], min(period['end'] + 1, len(pbt_seq))):
                        if pbt_seq[pos] in ['P', 'B']:  # Exclure TIE
                            all_pb_for_length.append(pbt_seq[pos])

                    # S/O pendant cette période
                    for pos in range(period['start'], min(period['end'] + 1, len(so_seq) + 1)):
                        if pos > 0 and pos - 1 < len(so_seq):
                            all_so_for_length.append(so_seq[pos - 1])

                # Analyse P/B pour cette longueur
                length_pb_analysis = {}
                if all_pb_for_length:
                    p_count = all_pb_for_length.count('P')
                    b_count = all_pb_for_length.count('B')
                    total_pb = p_count + b_count

                    length_pb_analysis = {
                        'player_ratio': p_count / total_pb,
                        'banker_ratio': b_count / total_pb,
                        'dominant_pb': 'P' if p_count > b_count else 'B',
                        'sample_size': total_pb,
                        'periods_count': len(periods_of_length)
                    }

                # Analyse S/O pour cette longueur
                length_so_analysis = {}
                if all_so_for_length:
                    s_count = all_so_for_length.count('S')
                    o_count = all_so_for_length.count('O')
                    total_so = s_count + o_count

                    length_so_analysis = {
                        'same_ratio': s_count / total_so,
                        'opposite_ratio': o_count / total_so,
                        'dominant_so': 'S' if s_count > o_count else 'O',
                        'sample_size': total_so
                    }

                desync_impact['length_impact_analysis'][f'length_{length}'] = {
                    'pb_impact': length_pb_analysis,
                    'so_impact': length_so_analysis,
                    'periods_count': len(periods_of_length)
                }

        # ================================================================
        # 3. IMPACT GLOBAL DES PÉRIODES DESYNC
        # ================================================================

        # Collecter TOUS les P/B pendant TOUTES les périodes DESYNC (sans TIE)
        all_desync_pb = []
        all_desync_so = []

        for period in desync_periods:
            for pos in range(period['start'], min(period['end'] + 1, len(pbt_seq))):
                if pbt_seq[pos] in ['P', 'B']:  # Exclure TIE
                    all_desync_pb.append(pbt_seq[pos])

            for pos in range(period['start'], min(period['end'] + 1, len(so_seq) + 1)):
                if pos > 0 and pos - 1 < len(so_seq):
                    all_desync_so.append(so_seq[pos - 1])

        # Analyse globale P/B
        global_pb_analysis = {}
        if all_desync_pb:
            p_count = all_desync_pb.count('P')
            b_count = all_desync_pb.count('B')
            total_pb = p_count + b_count

            global_pb_analysis = {
                'player_ratio': p_count / total_pb,
                'banker_ratio': b_count / total_pb,
                'dominant_pb': 'P' if p_count > b_count else 'B',
                'total_pb_sample': total_pb,
                'pb_bias_strength': abs((p_count / total_pb) - 0.5)
            }

        # Analyse globale S/O
        global_so_analysis = {}
        if all_desync_so:
            s_count = all_desync_so.count('S')
            o_count = all_desync_so.count('O')
            total_so = s_count + o_count

            global_so_analysis = {
                'same_ratio': s_count / total_so,
                'opposite_ratio': o_count / total_so,
                'dominant_so': 'S' if s_count > o_count else 'O',
                'total_so_sample': total_so,
                'so_bias_strength': abs((s_count / total_so) - 0.5)
            }

        # Force d'impact globale
        global_impact_strength = 0.0
        if global_pb_analysis and global_so_analysis:
            pb_strength = global_pb_analysis['pb_bias_strength']
            so_strength = global_so_analysis['so_bias_strength']
            # Pondération : P/B poids 2, S/O poids 3 (plus exploitable)
            global_impact_strength = (pb_strength * 2 + so_strength * 3) / 5

        desync_impact['global_desync_impact'] = {
            'pb_analysis': global_pb_analysis,
            'so_analysis': global_so_analysis,
            'impact_strength': global_impact_strength,
            'has_significant_impact': global_impact_strength > self.config.correlation_strength_threshold  # Seuil configurable
        }

        # ================================================================
        # 4. MÉTRIQUES DE FORCE DES PÉRIODES DESYNC
        # ================================================================

        total_periods = len(desync_periods)
        total_desync_length = sum(period['length'] for period in desync_periods)
        average_length = total_desync_length / total_periods if total_periods > 0 else 0
        max_length = max(period['length'] for period in desync_periods) if desync_periods else 0

        # Couverture DESYNC (% du jeu en désynchronisation)
        total_game_length = len(pbt_seq) if pbt_seq else 1
        desync_coverage = total_desync_length / total_game_length

        desync_impact['desync_strength_metrics'] = {
            'total_periods': total_periods,
            'total_desync_length': total_desync_length,
            'average_length': average_length,
            'max_length': max_length,
            'desync_coverage': desync_coverage,
            'desync_frequency': total_periods / total_game_length if total_game_length > 0 else 0
        }

        return desync_impact


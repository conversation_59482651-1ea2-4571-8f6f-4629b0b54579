# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 12843 à 12958
# Type: Méthode de la classe AZRCluster

    def _generate_signals_summary(self, all_indices: Dict, synthesis: Dict) -> Dict:
        """
        Génère le résumé optimisé des signaux pour le Rollout 2

        FOCUS : Signaux exploitables classés par force et priorité
        """
        signals_summary = {
            'top_signals': [],
            'recommended_strategy': 'conservative',
            'overall_confidence': 0.0,
            'exploitation_ready': False
        }

        # Extraction des signaux des impacts croisés
        cross_impacts = synthesis.get('cross_index_impacts', {})

        # Liste des signaux potentiels à évaluer
        potential_signals = []

        # 1. Signaux IMPAIR/PAIR → P/B
        impair_pair = all_indices.get('impair_pair', {})
        correlations = impair_pair.get('correlations', {})

        if correlations.get('impair_pb_hands', 0) >= 5:  # Au moins 5 échantillons
            impair_to_player = correlations.get('impair_to_player', 0.5)
            if abs(impair_to_player - 0.5) > self.config.rollout2_signal_detection_threshold:  # Signal significatif
                potential_signals.append({
                    'signal_name': 'IMPAIR_TO_PLAYER',
                    'strength': impair_to_player,
                    'confidence': min(0.9, correlations.get('impair_pb_hands', 0) / 20.0),
                    'sample_size': correlations.get('impair_pb_hands', 0),
                    'strategy': 'impair_pair_optimized',
                    'signal_type': 'pb_prediction',
                    'raw_strength': abs(impair_to_player - 0.5) * 2  # Normalisation 0-1
                })

        if correlations.get('pair_pb_hands', 0) >= 5:
            pair_to_banker = correlations.get('pair_to_banker', 0.5)
            if abs(pair_to_banker - 0.5) > self.config.rollout2_signal_detection_threshold:
                potential_signals.append({
                    'signal_name': 'PAIR_TO_BANKER',
                    'strength': pair_to_banker,
                    'confidence': min(0.9, correlations.get('pair_pb_hands', 0) / 20.0),
                    'sample_size': correlations.get('pair_pb_hands', 0),
                    'strategy': 'impair_pair_optimized',
                    'signal_type': 'pb_prediction',
                    'raw_strength': abs(pair_to_banker - 0.5) * 2
                })

        # 2. Signaux INDEX COMBINÉ → S/O (LES PLUS FORTS)
        combined_to_so = cross_impacts.get('combined_to_so', {})
        state_impacts = combined_to_so.get('state_impacts', {})

        for state, impact_data in state_impacts.items():
            if impact_data.get('total_occurrences', 0) >= 3:  # Au moins 3 échantillons
                to_s_ratio = impact_data.get('to_s_ratio', 0.5)
                to_o_ratio = impact_data.get('to_o_ratio', 0.5)

                # Identifier le signal le plus fort (S ou O)
                if to_s_ratio > to_o_ratio and to_s_ratio > self.config.rollout2_transition_signal_threshold:
                    potential_signals.append({
                        'signal_name': f'{state}_TO_SAME',
                        'strength': to_s_ratio,
                        'confidence': min(0.95, impact_data.get('total_occurrences', 0) / 15.0),
                        'sample_size': impact_data.get('total_occurrences', 0),
                        'strategy': 'combined_index',
                        'signal_type': 'so_prediction',
                        'raw_strength': abs(to_s_ratio - 0.5) * 2,
                        'target_outcome': 'S'
                    })
                elif to_o_ratio > to_s_ratio and to_o_ratio > self.config.rollout2_transition_signal_threshold:
                    potential_signals.append({
                        'signal_name': f'{state}_TO_OPPOSITE',
                        'strength': to_o_ratio,
                        'confidence': min(0.95, impact_data.get('total_occurrences', 0) / 15.0),
                        'sample_size': impact_data.get('total_occurrences', 0),
                        'strategy': 'combined_index',
                        'signal_type': 'so_prediction',
                        'raw_strength': abs(to_o_ratio - 0.5) * 2,
                        'target_outcome': 'O'
                    })

        # 3. Signaux SYNC/DESYNC → P/B
        desync_sync_to_pbt = cross_impacts.get('desync_sync_to_pbt', {})
        if desync_sync_to_pbt.get('sync_pb_hands', 0) >= 5:
            sync_to_player = desync_sync_to_pbt.get('sync_to_player', 0.5)
            if abs(sync_to_player - 0.5) > self.config.rollout2_signal_detection_threshold:
                potential_signals.append({
                    'signal_name': 'SYNC_TO_PLAYER',
                    'strength': sync_to_player,
                    'confidence': min(0.85, desync_sync_to_pbt.get('sync_pb_hands', 0) / 20.0),
                    'sample_size': desync_sync_to_pbt.get('sync_pb_hands', 0),
                    'strategy': 'sync_based',
                    'signal_type': 'pb_prediction',
                    'raw_strength': abs(sync_to_player - 0.5) * 2
                })

        # Tri des signaux par force brute (raw_strength)
        potential_signals.sort(key=lambda x: x['raw_strength'], reverse=True)

        # Sélection des top 5 signaux avec priorités
        for i, signal in enumerate(potential_signals[:5]):
            signal['priority'] = i + 1
            signals_summary['top_signals'].append(signal)

        # Détermination de la stratégie recommandée
        if signals_summary['top_signals']:
            best_signal = signals_summary['top_signals'][0]
            signals_summary['recommended_strategy'] = best_signal['strategy']
            signals_summary['overall_confidence'] = best_signal['confidence']

            # Exploitation prête si signal fort + confiance élevée
            if best_signal['raw_strength'] > self.config.signal_very_strong_threshold and best_signal['confidence'] > self.config.confidence_high_threshold:
                signals_summary['exploitation_ready'] = True

        return signals_summary
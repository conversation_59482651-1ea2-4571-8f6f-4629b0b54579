# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 19550 à 19595
# Type: Méthode

def generate_parameter_recommendations(detailed_stats: Dict, test_metrics: Dict) -> Dict:
    """
    Génère des recommandations d'ajustement des paramètres basées sur l'analyse

    Args:
        detailed_stats: Statistiques détaillées
        test_metrics: Métriques de performance

    Returns:
        Recommandations d'ajustement
    """
    recommendations = {
        'confidence_thresholds': {},
        'learning_rate_adjustments': {},
        'pattern_weights': {},
        'sync_desync_weights': {}
    }

    # Analyse du biais SYNC/DESYNC
    sync_stats = detailed_stats['sync_states']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    desync_o_rate = sync_stats['DESYNC']['O_rate']
    sync_s_rate = sync_stats['SYNC']['S_rate']
    sync_o_rate = sync_stats['SYNC']['O_rate']

    # Recommandations basées sur les biais observés
    if abs(desync_s_rate - 0.5) > 0.1:  # Biais significatif en DESYNC
        if desync_s_rate > self.config.rollout2_so_bias_strong_threshold:
            recommendations['sync_desync_weights']['desync_s_bonus'] = 0.2
            recommendations['confidence_thresholds']['desync_s_threshold'] = 0.3
        elif desync_s_rate < self.config.cluster_desync_bias_weak_threshold:
            recommendations['sync_desync_weights']['desync_o_bonus'] = 0.2
            recommendations['confidence_thresholds']['desync_o_threshold'] = 0.3

    # Ajustements basés sur la performance
    if test_metrics['test_accuracy'] < self.config.cluster_test_accuracy_low_threshold:
        recommendations['learning_rate_adjustments']['increase_learning_rate'] = True
        recommendations['confidence_thresholds']['lower_all_thresholds'] = self.config.cluster_confidence_threshold_adjustment
    elif test_metrics['test_accuracy'] > self.config.cluster_test_accuracy_high_threshold:
        recommendations['confidence_thresholds']['raise_all_thresholds'] = self.config.cluster_confidence_threshold_adjustment

    # Recommandations basées sur la variabilité
    if test_metrics['accuracy_std'] > self.config.cluster_accuracy_variability_threshold:
        recommendations['pattern_weights']['increase_stability_weight'] = 0.1

    return recommendations
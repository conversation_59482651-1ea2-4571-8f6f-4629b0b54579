# 🎯 GUIDE COMPLET D'UNIVERSALISATION DES MÉTHODES BCT-AZR
================================================================================
Date de création: 08/06/2025 21:55
Contexte: Création système révolutionnaire BCT avec cluster principal + 3 rollouts
Objectif: Une méthode universelle au lieu de duplications multiples

# 📋 CONTEXTE SPÉCIFIQUE BCT
================================================================================

## 🎯 ARCHITECTURE CIBLE :
- **1 CLUSTER PRINCIPAL** (au lieu de 8 clusters AZR)
- **3 ROLLOUTS** (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eur)
- **CONFIGURATION CENTRALISÉE** dans AZRConfig du programme principal
- **SYSTÈME TERNAIRE** : pair_4, impair_5, pair_6 (au lieu de binaire PAIR/IMPAIR)

## 🎯 OBJECTIF RÉVOLUTIONNAIRE :
```
1 MÉTHODE UNIVERSELLE
    ↓
3 COMPORTEMENTS ROLLOUTS DIFFÉRENTS
    ↓
0 DUPLICATION DE CODE
    ↓
CONFIGURATIONS CENTRALISÉES DANS AZRConfig
```

# 🔧 PRINCIPE FONDAMENTAL D'UNIVERSALISATION
================================================================================

## 📊 AVANT (Méthodes dupliquées) :
```python
# Rollout 1 - Analyseur
def analyze_impair_bias_r1(self, data):
    threshold = 0.5
    bonus = 0.1
    return self.process(data, threshold, bonus)

# Rollout 2 - Générateur  
def analyze_impair_bias_r2(self, data):
    threshold = 0.7
    bonus = 0.15
    return self.process(data, threshold, bonus)

# Rollout 3 - Prédicteur
def analyze_impair_bias_r3(self, data):
    threshold = 0.6
    bonus = 0.2
    return self.process(data, threshold, bonus)
```

## ✅ APRÈS (Méthode universelle) :
```python
def analyze_impair_bias_universal(self, data):
    """Méthode universelle adaptée aux 3 rollouts via configuration"""
    # Récupération configuration selon rollout actuel
    params = self.config.get_rollout_params(self.rollout_id)
    
    threshold = params['impair_bias_threshold']
    bonus = params['impair_bias_bonus']
    
    return self.process(data, threshold, bonus)
```

# 📋 MÉTHODOLOGIE COMPLÈTE EN 7 ÉTAPES
================================================================================

## 🔍 ÉTAPE 1 : ANALYSE DE LA MÉTHODE SOURCE
```
□ Identifier la méthode dans baseder/methodes_extraites/
□ Analyser les spécialisations conditionnelles (if/elif/else)
□ Identifier les paramètres variables selon rollout/cluster
□ Documenter la logique métier fondamentale
□ Vérifier les dépendances avec autres méthodes
```

**Exemple d'analyse :**
```python
# Méthode source avec spécialisations
def _analyze_impair_consecutive_bias(self, hands_data):
    if self.cluster_id == 2:  # Short patterns
        attention_level = 1.5
        threshold = 0.8
    elif self.cluster_id == 3:  # Medium patterns
        attention_level = 2.0
        threshold = 0.6
    else:  # Default cluster 0
        attention_level = 1.0
        threshold = 0.5
    
    # Logique métier commune...
```

## 🏗️ ÉTAPE 2 : CONCEPTION ARCHITECTURE UNIVERSELLE
```
□ Définir signature méthode universelle
□ Identifier paramètres à centraliser dans AZRConfig
□ Concevoir structure get_rollout_params()
□ Planifier adaptation système ternaire BCT
□ Valider compatibilité avec pipeline 170ms
```

**Structure de configuration :**
```python
# Dans AZRConfig
def get_rollout_params(self, rollout_id):
    """Retourne paramètres spécifiques au rollout"""
    base_params = {
        'impair_bias_threshold': 0.5,
        'impair_bias_bonus': 0.1,
        'pair_4_weight': 1.0,
        'impair_5_weight': 1.2,
        'pair_6_weight': 0.9
    }
    
    if rollout_id == 1:  # Analyseur
        return {**base_params, 'impair_bias_threshold': 0.6}
    elif rollout_id == 2:  # Générateur
        return {**base_params, 'impair_bias_bonus': 0.15}
    elif rollout_id == 3:  # Prédicteur
        return {**base_params, 'impair_bias_threshold': 0.7}
    
    return base_params
```

## 📝 ÉTAPE 3 : CENTRALISATION CONFIGURATION
```
□ Ajouter paramètres dans AZRConfig.get_rollout_params()
□ Définir valeurs par défaut (rollout de référence)
□ Spécialiser pour rollouts 1, 2, 3
□ Adapter au système ternaire BCT (pair_4/impair_5/pair_6)
□ Tester méthode get_rollout_params()
```

**Exemple de centralisation BCT :**
```python
def get_bct_rollout_params(self, rollout_id):
    """Configuration spécialisée BCT avec système ternaire"""
    bct_base = {
        # Système ternaire BCT
        'pair_4_threshold': 0.5,
        'impair_5_threshold': 0.6,  # Plus rare, seuil plus bas
        'pair_6_threshold': 0.5,
        
        # Poids exploitation biais
        'pair_4_exploitation_weight': 1.0,
        'impair_5_exploitation_weight': 1.5,  # Plus exploitable
        'pair_6_exploitation_weight': 1.0,
        
        # Timing optimisé
        'max_analysis_time_ms': 50,  # 170ms total / 3 rollouts
        'correlation_depth': 10
    }
    
    # Spécialisations par rollout
    rollout_specializations = {
        1: {  # Analyseur - Focus détection biais
            'impair_5_threshold': 0.4,  # Plus sensible
            'correlation_depth': 15
        },
        2: {  # Générateur - Focus génération
            'pair_4_exploitation_weight': 1.2,
            'max_analysis_time_ms': 60
        },
        3: {  # Prédicteur - Focus sélection
            'impair_5_exploitation_weight': 1.8,
            'correlation_depth': 8  # Plus rapide
        }
    }
    
    if rollout_id in rollout_specializations:
        return {**bct_base, **rollout_specializations[rollout_id]}
    
    return bct_base

## 🔄 ÉTAPE 4 : TRANSFORMATION MÉTHODE UNIVERSELLE
```
□ Copier méthode originale depuis baseder/methodes_extraites/
□ Remplacer conditions if/elif/else par appel get_rollout_params()
□ Adapter logique binaire → ternaire (PAIR/IMPAIR → pair_4/impair_5/pair_6)
□ Conserver TOUTE la logique métier originale
□ Ajouter documentation universalisation
```

**Template de transformation :**
```python
def _analyze_impair5_consecutive_bias_bct_universal(self, hands_data: List) -> Dict:
    """
    MÉTHODE UNIVERSELLE BCT - Analyse biais impair_5 consécutifs

    UNIVERSALISATION :
    - Adaptée aux 3 rollouts via configuration centralisée
    - Système ternaire BCT : pair_4, impair_5, pair_6
    - Configuration dans AZRConfig.get_bct_rollout_params()

    ROLLOUT BEHAVIORS :
    - Rollout 1 (Analyseur) : Détection sensible biais impair_5
    - Rollout 2 (Générateur) : Génération séquences exploitant biais
    - Rollout 3 (Prédicteur) : Sélection optimale basée biais détectés
    """
    # 🎯 RÉCUPÉRATION CONFIGURATION UNIVERSELLE
    params = self.config.get_bct_rollout_params(self.rollout_id)

    # 🔧 PARAMÈTRES ADAPTÉS AU ROLLOUT
    impair_5_threshold = params['impair_5_threshold']
    exploitation_weight = params['impair_5_exploitation_weight']
    max_time_ms = params['max_analysis_time_ms']
    correlation_depth = params['correlation_depth']

    # 📊 LOGIQUE MÉTIER UNIVERSELLE (conservée de l'original)
    impair_bias = {
        'isolated_impair_5': [],
        'consecutive_impair_5_sequences': [],
        'exploitation_confidence': 0.0,
        'bct_specific_signals': []
    }

    # 🎯 ANALYSE SYSTÈME TERNAIRE BCT
    bct_categories = []
    for hand_number in range(1, len(hands_data) + 1):
        hand_data = hands_data[hand_number - 1]

        # Détermination catégorie BCT selon nombre total cartes
        total_cards = hand_data.cards_distributed
        if total_cards == 4:
            category = 'pair_4'
        elif total_cards == 5:
            category = 'impair_5'  # 🎯 FOCUS PRINCIPAL
        elif total_cards == 6:
            category = 'pair_6'
        else:
            continue

        bct_categories.append({
            'position': hand_number,
            'category': category,
            'sync_state': getattr(hand_data, 'sync_state', 'SYNC'),
            'result': hand_data.result
        })

    # 🔍 DÉTECTION SÉQUENCES IMPAIR_5 CONSÉCUTIVES
    consecutive_impair_5 = []
    current_sequence = []

    for i, cat_data in enumerate(bct_categories):
        if cat_data['category'] == 'impair_5':
            current_sequence.append(cat_data)
        else:
            if len(current_sequence) >= 2:  # Séquence consécutive détectée
                consecutive_impair_5.append(current_sequence)
            current_sequence = []

    # Fermer dernière séquence
    if len(current_sequence) >= 2:
        consecutive_impair_5.append(current_sequence)

    # 📈 CALCUL EXPLOITATION SELON ROLLOUT
    for sequence in consecutive_impair_5:
        sequence_length = len(sequence)

        # Score d'exploitation adapté au rollout
        exploitation_score = min(1.0, sequence_length * exploitation_weight)

        if exploitation_score >= impair_5_threshold:
            impair_bias['bct_specific_signals'].append({
                'type': 'consecutive_impair_5',
                'length': sequence_length,
                'exploitation_score': exploitation_score,
                'positions': [s['position'] for s in sequence],
                'rollout_optimized': True
            })

    # 🎯 CONFIANCE FINALE ADAPTÉE AU ROLLOUT
    if impair_bias['bct_specific_signals']:
        total_exploitation = sum(s['exploitation_score'] for s in impair_bias['bct_specific_signals'])
        impair_bias['exploitation_confidence'] = min(1.0, total_exploitation / len(consecutive_impair_5))

    return impair_bias
```

## ✅ ÉTAPE 5 : VALIDATION UNIVERSALITÉ
```
□ Tester méthode avec rollout_id = 1 (Analyseur)
□ Tester méthode avec rollout_id = 2 (Générateur)
□ Tester méthode avec rollout_id = 3 (Prédicteur)
□ Vérifier comportements différents selon configuration
□ Valider timing ≤ 170ms / 3 rollouts
```

**Script de validation :**
```python
def test_methode_universelle():
    """Test complet universalité méthode"""
    config = AZRConfig()

    # Test données simulées
    test_hands = [create_test_hand(i) for i in range(20)]

    for rollout_id in [1, 2, 3]:
        print(f"\n🧪 TEST ROLLOUT {rollout_id}")

        # Simulation cluster avec rollout_id
        cluster = TestCluster(config, rollout_id)

        # Exécution méthode universelle
        start_time = time.time()
        result = cluster._analyze_impair5_consecutive_bias_bct_universal(test_hands)
        execution_time = (time.time() - start_time) * 1000

        print(f"⏱️ Temps: {execution_time:.1f}ms")
        print(f"🎯 Confiance: {result['exploitation_confidence']:.3f}")
        print(f"📊 Signaux: {len(result['bct_specific_signals'])}")

        # Validation comportement différent
        assert execution_time <= 60  # Max 60ms par rollout
        assert result['exploitation_confidence'] >= 0
```

## 🔧 ÉTAPE 6 : INTÉGRATION DANS CLUSTER PRINCIPAL
```
□ Ajouter méthode dans classe cluster principal
□ Connecter avec pipeline rollouts 1→2→3
□ Vérifier appels entre méthodes universelles
□ Optimiser pour timing global 170ms
□ Documenter interface universelle
```

**Intégration dans cluster :**
```python
class BCTClusterPrincipal:
    """Cluster principal BCT avec 3 rollouts universels"""

    def __init__(self, config: AZRConfig):
        self.config = config
        self.rollout_id = 0  # Sera modifié selon rollout actuel

    def execute_rollout_pipeline(self, game_data):
        """Pipeline séquentiel R1 → R2 → R3 avec méthodes universelles"""
        results = {}

        # 🔍 ROLLOUT 1 : ANALYSEUR
        self.rollout_id = 1
        start_time = time.time()

        analysis_result = self._analyze_impair5_consecutive_bias_bct_universal(game_data.hands)
        # ... autres méthodes d'analyse universelles

        results['rollout_1'] = {
            'analysis': analysis_result,
            'execution_time': (time.time() - start_time) * 1000
        }

        # 🎯 ROLLOUT 2 : GÉNÉRATEUR
        self.rollout_id = 2
        start_time = time.time()

        generation_result = self._generate_sequences_from_bct_signals_universal(
            results['rollout_1']['analysis']
        )

        results['rollout_2'] = {
            'sequences': generation_result,
            'execution_time': (time.time() - start_time) * 1000
        }

        # 🏆 ROLLOUT 3 : PRÉDICTEUR
        self.rollout_id = 3
        start_time = time.time()

        prediction_result = self._select_best_sequence_bct_universal(
            results['rollout_2']['sequences']
        )

        results['rollout_3'] = {
            'prediction': prediction_result,
            'execution_time': (time.time() - start_time) * 1000
        }

        # ⏱️ VALIDATION TIMING GLOBAL
        total_time = sum(r['execution_time'] for r in results.values())
        assert total_time <= 170, f"Timing dépassé: {total_time}ms > 170ms"

        return results
```

## 📋 ÉTAPE 7 : DOCUMENTATION ET MAINTENANCE
```
□ Documenter chaque méthode universelle
□ Créer guide utilisation configuration
□ Établir tests de régression
□ Planifier évolutions futures
□ Archiver méthodes originales
```

**Documentation standard :**
```python
def _methode_universelle_bct(self, data) -> Dict:
    """
    MÉTHODE UNIVERSELLE BCT - [Description fonctionnelle]

    UNIVERSALISATION :
    - Source: baseder/methodes_extraites/[nom_fichier].txt
    - Adaptée aux 3 rollouts via AZRConfig.get_bct_rollout_params()
    - Système ternaire: pair_4, impair_5, pair_6
    - Timing optimisé: ≤ 60ms par rollout

    COMPORTEMENTS ROLLOUTS :
    - Rollout 1 (Analyseur): [Spécialisation]
    - Rollout 2 (Générateur): [Spécialisation]
    - Rollout 3 (Prédicteur): [Spécialisation]

    CONFIGURATION :
    - Paramètres: self.config.get_bct_rollout_params(self.rollout_id)
    - Adaptation automatique selon rollout_id

    Args:
        data: [Type et description]

    Returns:
        Dict: [Structure retour détaillée]

    Raises:
        ValueError: Si données invalides
        TimeoutError: Si dépassement 60ms
    """
```

# 🎯 AVANTAGES OBTENUS PAR L'UNIVERSALISATION
================================================================================

## ✅ ARCHITECTURE RÉVOLUTIONNAIRE :
- **1 méthode** au lieu de 3+ duplications
- **Configuration centralisée** dans AZRConfig
- **Comportements adaptatifs** selon rollout
- **Maintenance simplifiée** (1 point de modification)

## 🚀 PERFORMANCE OPTIMISÉE :
- **Timing contrôlé** ≤ 170ms total
- **Spécialisations efficaces** par rollout
- **Système ternaire BCT** exploité
- **Pipeline séquentiel** optimisé

## 🔧 EXTENSIBILITÉ FUTURE :
- **Nouveaux rollouts** = nouveaux paramètres seulement
- **Évolutions BCT** centralisées dans configuration
- **Tests simplifiés** (1 méthode à valider)
- **Documentation unifiée**

# 📊 CHECKLIST COMPLÈTE D'UNIVERSALISATION
================================================================================

## 🔍 PHASE PRÉPARATION :
□ Identifier méthode source dans baseder/methodes_extraites/
□ Analyser spécialisations conditionnelles existantes
□ Documenter logique métier fondamentale
□ Planifier adaptation système ternaire BCT

## 🏗️ PHASE CONCEPTION :
□ Définir paramètres à centraliser dans AZRConfig
□ Concevoir get_bct_rollout_params() avec spécialisations
□ Planifier signature méthode universelle
□ Valider compatibilité timing 170ms

## 🔧 PHASE IMPLÉMENTATION :
□ Centraliser configuration dans AZRConfig
□ Transformer méthode avec appels configuration
□ Adapter logique binaire → ternaire BCT
□ Conserver logique métier originale intacte

## ✅ PHASE VALIDATION :
□ Tester avec rollout_id = 1, 2, 3
□ Vérifier comportements différents
□ Valider timing ≤ 60ms par rollout
□ Confirmer résultats cohérents

## 📋 PHASE INTÉGRATION :
□ Intégrer dans cluster principal BCT
□ Connecter pipeline rollouts séquentiel
□ Optimiser timing global ≤ 170ms
□ Documenter interface universelle

## 🎯 PHASE FINALISATION :
□ Documentation complète méthode
□ Tests de régression établis
□ Guide configuration créé
□ Archivage méthodes originales

# 🎉 RÉSULTAT FINAL GARANTI
================================================================================

**Cette méthodologie garantit :**

1. **UNIVERSALITÉ COMPLÈTE** : 1 méthode → 3 comportements rollouts
2. **CONFIGURATION CENTRALISÉE** : Tous paramètres dans AZRConfig
3. **SYSTÈME BCT OPTIMISÉ** : Exploitation ternaire pair_4/impair_5/pair_6
4. **PERFORMANCE GARANTIE** : Timing ≤ 170ms respecté
5. **ARCHITECTURE SCALABLE** : Extensibilité future assurée

**Le système révolutionnaire BCT-AZR sera ainsi créé avec une architecture universelle parfaitement optimisée !**
```

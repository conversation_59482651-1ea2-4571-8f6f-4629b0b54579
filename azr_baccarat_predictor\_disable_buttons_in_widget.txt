# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14587 à 14599
# Type: Méthode de la classe AZRBaccaratInterface

    def _disable_buttons_in_widget(self, widget):
        """Désactive récursivement tous les boutons dans un widget sauf Reset et Sauvegarder"""
        for child in widget.winfo_children():
            if isinstance(child, tk.Button):
                button_text = child.cget('text')
                # Garder les boutons Reset et Sauvegarder actifs
                if ('🔄 Soft Reset' not in button_text and
                    '🔥 Hard Reset' not in button_text and
                    '💾 Sauvegarder' not in button_text):
                    child.config(state=tk.DISABLED)
            else:
                # Récursion pour les conteneurs
                self._disable_buttons_in_widget(child)
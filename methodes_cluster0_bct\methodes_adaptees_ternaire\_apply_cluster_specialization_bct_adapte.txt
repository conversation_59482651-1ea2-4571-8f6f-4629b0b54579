MÉTHODE : _apply_cluster_specialization
LIGNE DÉBUT : 1745
SIGNATURE : def _apply_cluster_specialization(self, base_analysis: Dict, cluster_id: int, spec_params: Dict) -> Dict:
================================================================================

    def _apply_cluster_specialization(self, base_analysis: Dict, cluster_id: int, spec_params: Dict) -> Dict:
"""
    ADAPTATION BCT - _apply_cluster_specialization.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 APPLICATION SPÉCIALISATION CLUSTER GÉNÉRIQUE - Utilise paramètres centralisés

        Applique la spécialisation selon les paramètres centralisés dans AZRConfig.
        """
        cluster_specialization = {
            'specialization_type': spec_params['type'],
            'cluster_id': cluster_id,
            'fenetre_recente_optimisee': self.config.get_cluster_recent_window_size(cluster_id)
        }

        # Calculer le bonus total de spécialisation
        total_bonus = self.config.zero_value

        # Bonus des analyses IMPAIR spécialisées
        impair_analysis = base_analysis.get('impair_bias', {})
        total_bonus += impair_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Bonus des analyses SYNC spécialisées
        sync_analysis = base_analysis.get('sync_bias', {})
        total_bonus += sync_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Bonus global spécialisation
        cluster_specialization['specialization_bonus'] = min(self.config.one_value, total_bonus)
        cluster_specialization['impair_bonus'] = impair_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)
        cluster_specialization['sync_bonus'] = sync_analysis.get(f'c{cluster_id}_total_bonus', self.config.zero_value)

        # Métriques spécialisées
        cluster_specialization['patterns_detected'] = (
            impair_analysis.get(f'c{cluster_id}_recent_sequences_count', 0) +
            sync_analysis.get(f'c{cluster_id}_recent_breaks_count', 0)
        )

        cluster_specialization['cluster_score'] = total_bonus

        return cluster_specialization

    # ========================================================================
    # 🔗 MÉTHODES DE CORRÉLATION COPIÉES POUR ALIGNEMENT CLUSTERS
    # ========================================================================


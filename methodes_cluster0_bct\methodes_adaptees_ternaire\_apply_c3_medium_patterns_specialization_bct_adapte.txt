MÉTHODE : _apply_c3_medium_patterns_specialization
LIGNE DÉBUT : 2209
SIGNATURE : def _apply_c3_medium_patterns_specialization(self, base_analysis: Dict) -> Dict:
================================================================================

    def _apply_c3_medium_patterns_specialization(self, base_analysis: Dict) -> Dict:
"""
    ADAPTATION BCT - _apply_c3_medium_patterns_specialization.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 C3 SPÉCIALISATION - Application des bonus patterns moyens

        Applique la spécialisation C3 EN PLUS de la logique de base.
        """
        c3_specialization = {
            'specialization_type': 'patterns_moyens_4_6_manches',
            'cluster_id': self.cluster_id,
            'fenetre_recente_optimisee': self.config.get_cluster_recent_window_size(self.cluster_id)
        }

        # Calculer le bonus total de spécialisation C3
        total_bonus = self.config.zero_value

        # Bonus des analyses IMPAIR spécialisées
        impair_analysis = base_analysis.get('impair_bias', {})
        total_bonus += impair_analysis.get('c3_total_bonus', self.config.zero_value)

        # Bonus des analyses SYNC spécialisées
        sync_analysis = base_analysis.get('sync_bias', {})
        total_bonus += sync_analysis.get('c3_total_bonus', self.config.zero_value)

        # Bonus global spécialisation C3
        c3_specialization['specialization_bonus'] = min(self.config.one_value, total_bonus)
        c3_specialization['impair_bonus'] = impair_analysis.get('c3_total_bonus', self.config.zero_value)
        c3_specialization['sync_bonus'] = sync_analysis.get('c3_total_bonus', self.config.zero_value)

        # Métriques spécialisées C3
        c3_specialization['medium_patterns_detected'] = (
            impair_analysis.get('c3_recent_sequences_count', 0) +
            sync_analysis.get('c3_recent_breaks_count', 0)
        )

        c3_specialization['medium_pattern_score'] = (
            impair_analysis.get('c3_vision_bonus', self.config.zero_value) +
            sync_analysis.get('c3_balance_score', self.config.zero_value)
        ) / 2

        return c3_specialization


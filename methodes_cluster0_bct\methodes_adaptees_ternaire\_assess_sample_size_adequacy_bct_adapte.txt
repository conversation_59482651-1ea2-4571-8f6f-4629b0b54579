MÉTHODE : _assess_sample_size_adequacy
LIGNE DÉBUT : 8205
SIGNATURE : def _assess_sample_size_adequacy(self, variations_impact: Dict) -> float:
================================================================================

    def _assess_sample_size_adequacy(self, variations_impact: Dict) -> float:
"""
    ADAPTATION BCT - _assess_sample_size_adequacy.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Évalue l'adéquation des tailles d'échantillon"""

        sample_scores = []

        # Vérifier échantillons dans chaque type d'analyse
        for analysis_type, analysis_data in variations_impact.items():
            if isinstance(analysis_data, dict):

                # Chercher métriques d'échantillon
                if 'sample_size' in analysis_data:
                    sample_size = analysis_data['sample_size']
                    score = min(sample_size / 20.0, 1.0)  # 20+ = score max
                    sample_scores.append(score)

                # Chercher dans sous-structures
                for sub_key, sub_data in analysis_data.items():
                    if isinstance(sub_data, dict) and 'sample_size' in sub_data:
                        sample_size = sub_data['sample_size']
                        score = min(sample_size / 20.0, 1.0)
                        sample_scores.append(score)

        return sum(sample_scores) / len(sample_scores) if sample_scores else 0.0


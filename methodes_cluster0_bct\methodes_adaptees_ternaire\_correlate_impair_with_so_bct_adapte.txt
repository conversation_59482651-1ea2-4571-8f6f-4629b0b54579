MÉTHODE : _correlate_impair5_with_so
LIGNE DÉBUT : 1910
SIGNATURE : def _correlate_impair5_with_so(self, isolated_impairs: List, consecutive_sequences: List, so_outcomes: List, total_hands: int) -> Dict:
================================================================================

    def _correlate_impair5_with_so(self, isolated_impairs: List, consecutive_sequences: List, so_outcomes: List, total_hands: int) -> Dict:
"""
    ADAPTATION BCT - _correlate_impair_with_so.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Corrèle les IMPAIRS avec les conversions S/O"""
        correlation = {
            'prediction_strength': 0.0,
            'impair_s_count': 0,
            'impair_o_count': 0,
            'dominant_conversion': 'S'
        }

        total_correlations = 0
        s_count = 0

        # IMPAIRS isolés
        for pos in isolated_impairs:
            if pos - int(self.config.rollout_analyzer_position_offset) < len(so_outcomes):
                if so_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'S':
                    s_count += int(self.config.rollout_reward_valid_sequence_increment)
                total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        # Séquences d'IMPAIRS
        for seq in consecutive_sequences:
            for pos in seq:
                if pos - int(self.config.rollout_analyzer_position_offset) < len(so_outcomes):
                    if so_outcomes[pos - int(self.config.rollout_analyzer_position_offset)] == 'S':
                        s_count += int(self.config.rollout_reward_valid_sequence_increment)
                    total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        if total_correlations > 0:
            s_ratio = s_count / total_correlations
            correlation['prediction_strength'] = abs(s_ratio - self.config.rollout_analyzer_normality_threshold)
            correlation['impair_s_count'] = s_count
            correlation['impair_o_count'] = total_correlations - s_count

            if s_ratio > self.config.rollout_analyzer_normality_threshold:
                correlation['dominant_conversion'] = 'S'
            else:
                correlation['dominant_conversion'] = 'O'

        return correlation


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 3601 à 3622
# Type: Méthode de la classe AZRCluster

    def _generate_bias_generation_guidance_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
        """
        🎯 C2 SPÉCIALISÉ - Guidance génération avec spécialisation patterns courts
        """
        # Utiliser la méthode de base
        base_guidance = self._generate_bias_generation_guidance(bias_synthesis)

        # Ajouter la guidance spécialisée C2
        c2_guidance = {
            'c2_short_patterns_guidance': {
                'focus': 'patterns_courts_2_3_manches',
                'reactivity': 'maximale_micro_changements',
                'fenetre_optimale': c2_specialization.get('fenetre_recente_optimisee', 2),
                'bonus_disponible': c2_specialization.get('specialization_bonus', self.config.zero_value)
            }
        }

        # Fusionner la guidance
        base_guidance.update(c2_guidance)
        base_guidance['specialization_type'] = 'C2_patterns_courts'

        return base_guidance
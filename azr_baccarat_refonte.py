#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 AZR BACCARAT PREDICTOR - REFONTE COMPLÈTE
================================================================================

Programme de prédiction Baccarat basé sur l'architecture AZR (Auto-génératrice)
Refonte complète respectant systeme_comptage_baccarat_complet.txt

ARCHITECTURE :
- 8 clusters de 3 rollouts (24 comportements différents)
- Méthodes universelles avec spécialisations centralisées
- Système de comptage à 5 index conforme
- Interface graphique 9 boutons (3 issues × 3 parités)
- Utilisation optimale 8 cœurs + 28GB RAM

AUTEUR : AZR System
DATE : 2025
VERSION : 2.0.0 (Refonte complète)
================================================================================
"""

import os
import sys
import json
import logging
import threading
import multiprocessing
from typing import Dict, List, Optional, Tuple, Any, Iterator
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import tkinter as tk
from tkinter import ttk, messagebox

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('azr_baccarat_refonte.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

class AZRConfig:
    """
    Configuration centralisée pour tous les paramètres AZR
    
    PRINCIPE : Aucune valeur codée en dur dans les méthodes
    Toutes les spécialisations des 8 clusters centralisées ici
    """
    
    def __init__(self):
        # ====================================================================
        # PARAMÈTRES SYSTÈME FONDAMENTAUX
        # ====================================================================
        
        # Architecture clusters
        self.nb_clusters = 8
        self.nb_rollouts_per_cluster = 3
        self.nb_cores = multiprocessing.cpu_count()  # 8 cœurs
        self.max_memory_gb = 28
        
        # Valeurs de base universelles
        self.zero_value = 0
        self.one_value = 1
        self.two_value = 2
        self.three_value = 3
        
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (5 INDEX)
        # ====================================================================
        
        # INDEX 1 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }
        
        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }
        
        # INDEX 2 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }
        
        # INDEX 3 : États combinés (INDEX 1 + INDEX 2)
        self.combined_states = [
            'pair_4_sync', 'pair_4_desync',
            'pair_6_sync', 'pair_6_desync', 
            'impair_5_sync', 'impair_5_desync'
        ]
        
        # INDEX 4 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs S/O
        
        # INDEX 5 : Conversions S/O
        self.so_conversions = ['S', 'O', '--']  # Same, Opposite, Première manche
        
        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================
        
        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE
        
        # ====================================================================
        # SPÉCIALISATIONS DES 8 CLUSTERS
        # ====================================================================
        
        self.cluster_specializations = {
            0: {  # CLUSTER RÉFÉRENCE (comportement par défaut)
                'name': 'Reference',
                'focus': 'balanced',
                'pattern_length': 'medium',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            1: {  # CLUSTER IDENTIQUE À C0 (redondance sécurité)
                'name': 'Reference_Backup',
                'focus': 'balanced',
                'pattern_length': 'medium', 
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            2: {  # CLUSTER PATTERNS COURTS
                'name': 'Short_Patterns',
                'focus': 'short_patterns',
                'pattern_length': 'short',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'high'
            },
            3: {  # CLUSTER PATTERNS MOYENS
                'name': 'Medium_Patterns', 
                'focus': 'medium_patterns',
                'pattern_length': 'medium',
                'confidence_threshold': 0.55,
                'risk_tolerance': 'medium'
            },
            4: {  # CLUSTER PATTERNS LONGS
                'name': 'Long_Patterns',
                'focus': 'long_patterns', 
                'pattern_length': 'long',
                'confidence_threshold': 0.45,
                'risk_tolerance': 'low'
            },
            5: {  # CLUSTER CORRÉLATIONS
                'name': 'Correlations',
                'focus': 'correlations',
                'pattern_length': 'variable',
                'confidence_threshold': 0.65,
                'risk_tolerance': 'medium'
            },
            6: {  # CLUSTER SYNC/DESYNC
                'name': 'Sync_Desync',
                'focus': 'sync_analysis',
                'pattern_length': 'medium',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'medium'
            },
            7: {  # CLUSTER ADAPTATIF
                'name': 'Adaptive',
                'focus': 'adaptive',
                'pattern_length': 'adaptive',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'adaptive'
            }
        }
    
    def get_cluster_params(self, cluster_id: int) -> Dict[str, Any]:
        """
        Retourne les paramètres spécialisés pour un cluster donné
        
        MÉTHODE UNIVERSELLE : Utilisée par toutes les méthodes pour obtenir
        leurs paramètres spécialisés sans conditions if cluster_id ==
        """
        if cluster_id not in self.cluster_specializations:
            logger.warning(f"Cluster {cluster_id} non défini, utilisation cluster 0")
            cluster_id = 0
            
        return self.cluster_specializations[cluster_id].copy()
    
    def get_system_limits(self) -> Dict[str, int]:
        """Retourne les limites système pour optimisation mémoire/CPU"""
        return {
            'nb_clusters': self.nb_clusters,
            'nb_rollouts_per_cluster': self.nb_rollouts_per_cluster,
            'total_rollouts': self.nb_clusters * self.nb_rollouts_per_cluster,
            'nb_cores': self.nb_cores,
            'max_memory_gb': self.max_memory_gb,
            'memory_per_cluster_mb': (self.max_memory_gb * 1024) // self.nb_clusters
        }

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE                      #
#                                                                              #
################################################################################

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int                    # Numéro de main (incluant TIE)
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'
    
    # INDEX 3 : État combiné (INDEX 1 + INDEX 2)
    combined_state: str                 # 'pair_4_sync', 'impair_5_desync', etc.
    
    # INDEX 4 : Résultat
    result: str                         # 'PLAYER', 'BANKER', 'TIE'
    
    # INDEX 5 : Conversion S/O (seulement pour P/B)
    so_conversion: str                  # 'S', 'O', '--'
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_pb_hand(self) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        return self.result in ['PLAYER', 'BANKER']
    
    def is_tie_hand(self) -> bool:
        """Vérifie si c'est un TIE"""
        return self.result == 'TIE'

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    so_conversions: int = 0             # Conversions S/O calculées
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)
        self.total_hands += 1
        
        if hand.is_pb_hand():
            self.pb_hands += 1
            if hand.so_conversion in ['S', 'O']:
                self.so_conversions += 1
        else:
            self.tie_hands += 1
    
    def is_complete(self, max_pb_hands: int = 60) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        return [hand.result for hand in self.hands if hand.is_pb_hand()]
    
    def get_so_sequence(self) -> List[str]:
        """Retourne la séquence S/O (sans '--')"""
        return [hand.so_conversion for hand in self.hands 
                if hand.so_conversion in ['S', 'O']]

################################################################################
#                                                                              #
#  🧮 SECTION 3 : MOTEUR DE COMPTAGE CONFORME                                 #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt
    
    Implémente les 5 INDEX avec la logique exacte de référence
    """
    
    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")
    
    def calculate_cards_distributed(self, player_cards: int, banker_cards: int) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie
        
        Args:
            player_cards: Nombre de cartes Player (2 ou 3)
            banker_cards: Nombre de cartes Banker (2 ou 3)
            
        Returns:
            Tuple[total_cards, parity, category]
        """
        total_cards = player_cards + banker_cards
        
        if total_cards % 2 == 0:
            parity = 'PAIR'
            if total_cards == 4:
                category = 'pair_4'
            elif total_cards == 6:
                category = 'pair_6'
            else:
                # Cas exceptionnels (ne devrait pas arriver)
                category = f'pair_{total_cards}'
        else:
            parity = 'IMPAIR'
            if total_cards == 5:
                category = 'impair_5'
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'
        
        return total_cards, parity, category
    
    def calculate_sync_state(self, current_sync: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC
        
        LOGIQUE EXACTE de référence :
        - Main avec nombre IMPAIR de cartes → CHANGE l'état
        - Main avec nombre PAIR de cartes → CONSERVE l'état
        """
        if cards_parity == 'IMPAIR':
            # Changer l'état
            return 'DESYNC' if current_sync == 'SYNC' else 'SYNC'
        else:
            # Conserver l'état
            return current_sync
    
    def calculate_so_conversion(self, current_result: str, last_pb_result: Optional[str]) -> str:
        """
        Calcule INDEX 5 : conversion S/O
        
        LOGIQUE EXACTE de référence :
        - Première manche P/B : '--'
        - TIE : '--' (pas de conversion)
        - P/B identique au précédent : 'S' (Same)
        - P/B différent du précédent : 'O' (Opposite)
        """
        if current_result == 'TIE':
            return '--'
        
        if current_result not in ['PLAYER', 'BANKER']:
            return '--'
        
        if last_pb_result is None:
            return '--'  # Première manche P/B
        
        if current_result == last_pb_result:
            return 'S'  # Same
        else:
            return 'O'  # Opposite
    
    def process_hand(self, game: BaccaratGame, result: str, 
                    player_cards: int, banker_cards: int) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index
        
        MÉTHODE UNIVERSELLE : Utilisée par tous les clusters
        """
        # INDEX 1 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            player_cards, banker_cards
        )
        
        # INDEX 2 : Nouvel état SYNC/DESYNC
        new_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )
        
        # INDEX 3 : État combiné
        combined_state = f"{cards_category}_{new_sync_state.lower()}"
        
        # INDEX 5 : Conversion S/O
        so_conversion = self.calculate_so_conversion(result, game.last_pb_result)
        
        # Numéro de manche P/B
        pb_hand_number = None
        if result in ['PLAYER', 'BANKER']:
            pb_hand_number = game.pb_hands + 1  # Prochaine manche P/B
        
        # Créer la main
        hand = BaccaratHand(
            hand_number=game.total_hands + 1,
            pb_hand_number=pb_hand_number,
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            sync_state=new_sync_state,
            combined_state=combined_state,
            result=result,
            so_conversion=so_conversion
        )
        
        # Mettre à jour l'état du jeu
        game.current_sync_state = new_sync_state
        if result in ['PLAYER', 'BANKER']:
            game.last_pb_result = result
        
        # Ajouter la main au jeu
        game.add_hand(hand)
        
        self.logger.debug(f"Main traitée: {hand}")
        
        return hand

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 AZR BACCARAT PREDICTOR - REFONTE COMPLÈTE
================================================================================

Programme de prédiction Baccarat basé sur l'architecture AZR (Auto-génératrice)
Refonte complète respectant systeme_comptage_baccarat_complet.txt

ARCHITECTURE :
- 8 clusters de 3 rollouts (24 comportements différents)
- Méthodes universelles avec spécialisations centralisées
- Système de comptage à 5 index conforme
- Interface graphique 9 boutons (3 issues × 3 parités)
- Utilisation optimale 8 cœurs + 28GB RAM

AUTEUR : AZR System
DATE : 2025
VERSION : 2.0.0 (Refonte complète)
================================================================================
"""

import os
import sys
import json
import logging
import threading
import multiprocessing
from typing import Dict, List, Optional, Tuple, Any, Iterator
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import tkinter as tk
from tkinter import ttk, messagebox

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('azr_baccarat_refonte.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

class AZRConfig:
    """
    Configuration centralisée pour tous les paramètres AZR
    
    PRINCIPE : Aucune valeur codée en dur dans les méthodes
    Toutes les spécialisations des 8 clusters centralisées ici
    """
    
    def __init__(self):
        # ====================================================================
        # PARAMÈTRES SYSTÈME FONDAMENTAUX
        # ====================================================================
        
        # Architecture clusters
        self.nb_clusters = 8
        self.nb_rollouts_per_cluster = 3
        self.nb_cores = multiprocessing.cpu_count()  # 8 cœurs
        self.max_memory_gb = 28
        
        # Valeurs de base universelles
        self.zero_value = 0
        self.one_value = 1
        self.two_value = 2
        self.three_value = 3
        
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (5 INDEX)
        # ====================================================================
        
        # INDEX 1 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }
        
        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }
        
        # INDEX 2 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }
        
        # INDEX 3 : États combinés (INDEX 1 + INDEX 2)
        self.combined_states = [
            'pair_4_sync', 'pair_4_desync',
            'pair_6_sync', 'pair_6_desync', 
            'impair_5_sync', 'impair_5_desync'
        ]
        
        # INDEX 4 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs S/O
        
        # INDEX 5 : Conversions S/O
        self.so_conversions = ['S', 'O', '--']  # Same, Opposite, Première manche
        
        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================
        
        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE
        
        # ====================================================================
        # SPÉCIALISATIONS DES 8 CLUSTERS
        # ====================================================================
        
        self.cluster_specializations = {
            0: {  # CLUSTER RÉFÉRENCE (comportement par défaut)
                'name': 'Reference',
                'focus': 'balanced',
                'pattern_length': 'medium',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            1: {  # CLUSTER IDENTIQUE À C0 (redondance sécurité)
                'name': 'Reference_Backup',
                'focus': 'balanced',
                'pattern_length': 'medium', 
                'confidence_threshold': 0.5,
                'risk_tolerance': 'medium'
            },
            2: {  # CLUSTER PATTERNS COURTS
                'name': 'Short_Patterns',
                'focus': 'short_patterns',
                'pattern_length': 'short',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'high'
            },
            3: {  # CLUSTER PATTERNS MOYENS
                'name': 'Medium_Patterns', 
                'focus': 'medium_patterns',
                'pattern_length': 'medium',
                'confidence_threshold': 0.55,
                'risk_tolerance': 'medium'
            },
            4: {  # CLUSTER PATTERNS LONGS
                'name': 'Long_Patterns',
                'focus': 'long_patterns', 
                'pattern_length': 'long',
                'confidence_threshold': 0.45,
                'risk_tolerance': 'low'
            },
            5: {  # CLUSTER CORRÉLATIONS
                'name': 'Correlations',
                'focus': 'correlations',
                'pattern_length': 'variable',
                'confidence_threshold': 0.65,
                'risk_tolerance': 'medium'
            },
            6: {  # CLUSTER SYNC/DESYNC
                'name': 'Sync_Desync',
                'focus': 'sync_analysis',
                'pattern_length': 'medium',
                'confidence_threshold': 0.6,
                'risk_tolerance': 'medium'
            },
            7: {  # CLUSTER ADAPTATIF
                'name': 'Adaptive',
                'focus': 'adaptive',
                'pattern_length': 'adaptive',
                'confidence_threshold': 0.5,
                'risk_tolerance': 'adaptive'
            }
        }
    
    def get_cluster_params(self, cluster_id: int) -> Dict[str, Any]:
        """
        Retourne les paramètres spécialisés pour un cluster donné
        
        MÉTHODE UNIVERSELLE : Utilisée par toutes les méthodes pour obtenir
        leurs paramètres spécialisés sans conditions if cluster_id ==
        """
        if cluster_id not in self.cluster_specializations:
            logger.warning(f"Cluster {cluster_id} non défini, utilisation cluster 0")
            cluster_id = 0
            
        return self.cluster_specializations[cluster_id].copy()
    
    def get_system_limits(self) -> Dict[str, int]:
        """Retourne les limites système pour optimisation mémoire/CPU"""
        return {
            'nb_clusters': self.nb_clusters,
            'nb_rollouts_per_cluster': self.nb_rollouts_per_cluster,
            'total_rollouts': self.nb_clusters * self.nb_rollouts_per_cluster,
            'nb_cores': self.nb_cores,
            'max_memory_gb': self.max_memory_gb,
            'memory_per_cluster_mb': (self.max_memory_gb * 1024) // self.nb_clusters
        }

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE                      #
#                                                                              #
################################################################################

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int                    # Numéro de main (incluant TIE)
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'
    
    # INDEX 3 : État combiné (INDEX 1 + INDEX 2)
    combined_state: str                 # 'pair_4_sync', 'impair_5_desync', etc.
    
    # INDEX 4 : Résultat
    result: str                         # 'PLAYER', 'BANKER', 'TIE'
    
    # INDEX 5 : Conversion S/O (seulement pour P/B)
    so_conversion: str                  # 'S', 'O', '--'
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_pb_hand(self) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        return self.result in ['PLAYER', 'BANKER']
    
    def is_tie_hand(self) -> bool:
        """Vérifie si c'est un TIE"""
        return self.result == 'TIE'

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    so_conversions: int = 0             # Conversions S/O calculées
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)
        self.total_hands += 1
        
        if hand.is_pb_hand():
            self.pb_hands += 1
            if hand.so_conversion in ['S', 'O']:
                self.so_conversions += 1
        else:
            self.tie_hands += 1
    
    def is_complete(self, max_pb_hands: int = 60) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        return [hand.result for hand in self.hands if hand.is_pb_hand()]
    
    def get_so_sequence(self) -> List[str]:
        """Retourne la séquence S/O (sans '--')"""
        return [hand.so_conversion for hand in self.hands 
                if hand.so_conversion in ['S', 'O']]

################################################################################
#                                                                              #
#  🧮 SECTION 3 : MOTEUR DE COMPTAGE CONFORME                                 #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt
    
    Implémente les 5 INDEX avec la logique exacte de référence
    """
    
    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")
    
    def calculate_cards_distributed(self, player_cards: int, banker_cards: int) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie
        
        Args:
            player_cards: Nombre de cartes Player (2 ou 3)
            banker_cards: Nombre de cartes Banker (2 ou 3)
            
        Returns:
            Tuple[total_cards, parity, category]
        """
        total_cards = player_cards + banker_cards
        
        if total_cards % 2 == 0:
            parity = 'PAIR'
            if total_cards == 4:
                category = 'pair_4'
            elif total_cards == 6:
                category = 'pair_6'
            else:
                # Cas exceptionnels (ne devrait pas arriver)
                category = f'pair_{total_cards}'
        else:
            parity = 'IMPAIR'
            if total_cards == 5:
                category = 'impair_5'
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'
        
        return total_cards, parity, category
    
    def calculate_sync_state(self, current_sync: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC
        
        LOGIQUE EXACTE de référence :
        - Main avec nombre IMPAIR de cartes → CHANGE l'état
        - Main avec nombre PAIR de cartes → CONSERVE l'état
        """
        if cards_parity == 'IMPAIR':
            # Changer l'état
            return 'DESYNC' if current_sync == 'SYNC' else 'SYNC'
        else:
            # Conserver l'état
            return current_sync
    
    def calculate_so_conversion(self, current_result: str, last_pb_result: Optional[str]) -> str:
        """
        Calcule INDEX 5 : conversion S/O
        
        LOGIQUE EXACTE de référence :
        - Première manche P/B : '--'
        - TIE : '--' (pas de conversion)
        - P/B identique au précédent : 'S' (Same)
        - P/B différent du précédent : 'O' (Opposite)
        """
        if current_result == 'TIE':
            return '--'
        
        if current_result not in ['PLAYER', 'BANKER']:
            return '--'
        
        if last_pb_result is None:
            return '--'  # Première manche P/B
        
        if current_result == last_pb_result:
            return 'S'  # Same
        else:
            return 'O'  # Opposite
    
    def process_hand(self, game: BaccaratGame, result: str, 
                    player_cards: int, banker_cards: int) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index
        
        MÉTHODE UNIVERSELLE : Utilisée par tous les clusters
        """
        # INDEX 1 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            player_cards, banker_cards
        )
        
        # INDEX 2 : Nouvel état SYNC/DESYNC
        new_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )
        
        # INDEX 3 : État combiné
        combined_state = f"{cards_category}_{new_sync_state.lower()}"
        
        # INDEX 5 : Conversion S/O
        so_conversion = self.calculate_so_conversion(result, game.last_pb_result)
        
        # Numéro de manche P/B
        pb_hand_number = None
        if result in ['PLAYER', 'BANKER']:
            pb_hand_number = game.pb_hands + 1  # Prochaine manche P/B
        
        # Créer la main
        hand = BaccaratHand(
            hand_number=game.total_hands + 1,
            pb_hand_number=pb_hand_number,
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            sync_state=new_sync_state,
            combined_state=combined_state,
            result=result,
            so_conversion=so_conversion
        )
        
        # Mettre à jour l'état du jeu
        game.current_sync_state = new_sync_state
        if result in ['PLAYER', 'BANKER']:
            game.last_pb_result = result
        
        # Ajouter la main au jeu
        game.add_hand(hand)
        
        self.logger.debug(f"Main traitée: {hand}")
        
        return hand

################################################################################
#                                                                              #
#  🎯 SECTION 4 : ROLLOUTS UNIVERSELS (ANALYSEUR, GÉNÉRATEUR, PRÉDICTEUR)     #
#                                                                              #
################################################################################

class UniversalRollout:
    """
    Classe de base pour tous les rollouts

    PRINCIPE UNIVERSALITÉ selon recherches_centralisation_methodes.md :
    - CENTRALIZED CONFIGURATION PATTERN
    - PARAMETER OBJECT + STRATEGY
    - Une seule implémentation par rollout, spécialisations via paramètres
    """

    def __init__(self, cluster_id: int, rollout_id: int, config: AZRConfig):
        self.cluster_id = cluster_id
        self.rollout_id = rollout_id
        self.config = config
        self.cluster_params = config.get_cluster_params(cluster_id)
        self.logger = logging.getLogger(f"{__name__}.Cluster{cluster_id}.Rollout{rollout_id}")

    def get_rollout_name(self) -> str:
        """Retourne le nom du rollout pour identification"""
        cluster_name = self.cluster_params.get('name', f'Cluster{self.cluster_id}')
        return f"{cluster_name}_R{self.rollout_id}"

class AnalyzerRollout(UniversalRollout):
    """
    ROLLOUT 1 : ANALYSEUR UNIVERSEL

    Analyse les données de jeu et génère des signaux
    Applique PARAMETERIZATION selon recherches_centralisation_methodes.md
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 1, config)

    def analyze_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE UNIVERSELLE : Analyse l'état du jeu

        CENTRALIZED CONFIGURATION PATTERN :
        AZRConfig → UniversalMethod(cluster_id, config) → Comportement adapté
        """
        cluster_params = self.cluster_params

        # PARAMETERIZATION : Transformer valeurs codées en paramètres configurables
        focus = cluster_params.get('focus', 'balanced')
        pattern_length = cluster_params.get('pattern_length', 'medium')
        confidence_threshold = cluster_params.get('confidence_threshold', 0.5)

        analysis = {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'focus': focus,
            'pattern_length': pattern_length,
            'confidence_threshold': confidence_threshold,
            'game_stats': self._analyze_basic_stats(game),
            'index_analysis': self._analyze_indexes(game),
            'pattern_analysis': self._analyze_patterns(game, pattern_length),
            'signals': self._generate_signals(game, focus),
            'timestamp': datetime.now()
        }

        self.logger.debug(f"Analyse terminée: {self.get_rollout_name()}")
        return analysis

    def _analyze_basic_stats(self, game: BaccaratGame) -> Dict[str, Any]:
        """Analyse statistiques de base"""
        return {
            'total_hands': game.total_hands,
            'pb_hands': game.pb_hands,
            'tie_hands': game.tie_hands,
            'so_conversions': game.so_conversions,
            'completion_ratio': game.pb_hands / self.config.max_manches_per_game,
            'current_sync_state': game.current_sync_state
        }

    def _analyze_indexes(self, game: BaccaratGame) -> Dict[str, Any]:
        """Analyse des 5 index de comptage"""
        if not game.hands:
            return {}

        # Analyse INDEX 1 : Distribution des cartes
        card_distribution = {}
        for category in self.config.card_count_categories.keys():
            count = sum(1 for hand in game.hands if hand.cards_category == category)
            card_distribution[category] = count

        # Analyse INDEX 2 : États SYNC/DESYNC
        sync_distribution = {}
        for state in self.config.sync_states:
            count = sum(1 for hand in game.hands if hand.sync_state == state)
            sync_distribution[state] = count

        # Analyse INDEX 4 : Résultats P/B/T
        result_distribution = {}
        for result in self.config.game_results:
            count = sum(1 for hand in game.hands if hand.result == result)
            result_distribution[result] = count

        # Analyse INDEX 5 : Conversions S/O
        so_distribution = {}
        for conversion in self.config.so_conversions:
            count = sum(1 for hand in game.hands if hand.so_conversion == conversion)
            so_distribution[conversion] = count

        return {
            'card_distribution': card_distribution,
            'sync_distribution': sync_distribution,
            'result_distribution': result_distribution,
            'so_distribution': so_distribution
        }

    def _analyze_patterns(self, game: BaccaratGame, pattern_length: str) -> Dict[str, Any]:
        """
        Analyse des patterns selon la spécialisation du cluster

        METHOD DISPATCH : Délégation selon paramètres
        """
        # Longueur d'analyse selon spécialisation (PARAMETERIZATION)
        length_mapping = {
            'short': 3,
            'medium': 5,
            'long': 8,
            'variable': min(7, max(3, game.pb_hands // 3)),
            'adaptive': self._calculate_adaptive_length(game)
        }

        analysis_length = length_mapping.get(pattern_length, 5)

        # Séquences pour analyse
        pb_sequence = game.get_pb_sequence()
        so_sequence = game.get_so_sequence()

        if len(pb_sequence) < analysis_length:
            return {'insufficient_data': True, 'required_length': analysis_length}

        # Analyse des patterns récents
        recent_pb = pb_sequence[-analysis_length:]
        recent_so = so_sequence[-min(analysis_length-1, len(so_sequence)):]

        return {
            'analysis_length': analysis_length,
            'recent_pb_pattern': recent_pb,
            'recent_so_pattern': recent_so,
            'pattern_strength': self._calculate_pattern_strength(recent_pb, recent_so),
            'trend_direction': self._calculate_trend_direction(recent_so)
        }

    def _calculate_adaptive_length(self, game: BaccaratGame) -> int:
        """Calcule longueur adaptative selon l'état du jeu"""
        base_length = 5

        # Adapter selon le nombre de manches
        if game.pb_hands < 10:
            return 3
        elif game.pb_hands < 30:
            return 5
        else:
            return 7

    def _calculate_pattern_strength(self, pb_pattern: List[str], so_pattern: List[str]) -> float:
        """Calcule la force du pattern détecté"""
        if not so_pattern:
            return 0.0

        # Analyse de la régularité
        same_count = so_pattern.count('S')
        opposite_count = so_pattern.count('O')
        total = len(so_pattern)

        if total == 0:
            return 0.0

        # Force basée sur la dominance d'un type
        dominance = max(same_count, opposite_count) / total
        return dominance

    def _calculate_trend_direction(self, so_pattern: List[str]) -> str:
        """Calcule la direction de la tendance"""
        if not so_pattern:
            return 'neutral'

        recent = so_pattern[-3:] if len(so_pattern) >= 3 else so_pattern

        same_count = recent.count('S')
        opposite_count = recent.count('O')

        if same_count > opposite_count:
            return 'same_trend'
        elif opposite_count > same_count:
            return 'opposite_trend'
        else:
            return 'neutral'

    def _generate_signals(self, game: BaccaratGame, focus: str) -> Dict[str, Any]:
        """
        Génère des signaux selon la spécialisation du cluster

        METHOD DISPATCH : Une méthode centrale qui délègue selon paramètres
        """
        signals = {
            'primary_signal': None,
            'secondary_signals': [],
            'confidence': 0.0,
            'recommendation': 'wait'
        }

        # METHOD DISPATCH selon spécialisation
        if focus == 'short_patterns':
            signals.update(self._generate_short_pattern_signals(game))
        elif focus == 'long_patterns':
            signals.update(self._generate_long_pattern_signals(game))
        elif focus == 'correlations':
            signals.update(self._generate_correlation_signals(game))
        elif focus == 'sync_analysis':
            signals.update(self._generate_sync_signals(game))
        elif focus == 'adaptive':
            signals.update(self._generate_adaptive_signals(game))
        else:  # balanced
            signals.update(self._generate_balanced_signals(game))

        return signals

    def _generate_short_pattern_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux spécialisés patterns courts"""
        return {
            'primary_signal': 'short_pattern_detected',
            'confidence': 0.6,
            'recommendation': 'exploit_short'
        }

    def _generate_long_pattern_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux spécialisés patterns longs"""
        return {
            'primary_signal': 'long_pattern_detected',
            'confidence': 0.45,
            'recommendation': 'exploit_long'
        }

    def _generate_correlation_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux spécialisés corrélations"""
        return {
            'primary_signal': 'correlation_detected',
            'confidence': 0.65,
            'recommendation': 'exploit_correlation'
        }

    def _generate_sync_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux spécialisés SYNC/DESYNC"""
        return {
            'primary_signal': f'sync_state_{game.current_sync_state.lower()}',
            'confidence': 0.6,
            'recommendation': 'exploit_sync'
        }

    def _generate_adaptive_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux adaptatifs selon contexte"""
        return {
            'primary_signal': 'adaptive_analysis',
            'confidence': 0.5,
            'recommendation': 'adaptive_strategy'
        }

    def _generate_balanced_signals(self, game: BaccaratGame) -> Dict[str, Any]:
        """Signaux équilibrés (cluster référence)"""
        return {
            'primary_signal': 'balanced_analysis',
            'confidence': 0.5,
            'recommendation': 'balanced_strategy'
        }

class GeneratorRollout(UniversalRollout):
    """
    ROLLOUT 2 : GÉNÉRATEUR UNIVERSEL

    Génère des séquences candidates basées sur l'analyse
    Applique PARAMETER OBJECT + STRATEGY selon recherches_centralisation_methodes.md
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 2, config)

    def generate_sequences(self, analysis: Dict[str, Any], game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE UNIVERSELLE : Génère des séquences candidates

        TEMPLATE METHOD + CONFIGURATION :
        TemplateMethod(config) → step1(params) → step2(params) → step3(params)
        """
        cluster_params = self.cluster_params

        # PARAMETERIZATION : Transformer valeurs codées en paramètres configurables
        sequence_count = cluster_params.get('sequence_count', 8)
        generation_strategy = cluster_params.get('focus', 'balanced')  # Réutilise focus
        confidence_threshold = cluster_params.get('confidence_threshold', 0.5)
        risk_tolerance = cluster_params.get('risk_tolerance', 'medium')

        generation_result = {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'generation_strategy': generation_strategy,
            'sequences': [],
            'metadata': {
                'sequence_count': sequence_count,
                'confidence_threshold': confidence_threshold,
                'risk_tolerance': risk_tolerance,
                'generation_quality': 0.0
            },
            'timestamp': datetime.now()
        }

        # METHOD DISPATCH : Délégation selon paramètres
        if generation_strategy == 'short_patterns':
            sequences = self._generate_short_pattern_sequences(analysis, game, cluster_params)
        elif generation_strategy == 'long_patterns':
            sequences = self._generate_long_pattern_sequences(analysis, game, cluster_params)
        elif generation_strategy == 'correlations':
            sequences = self._generate_correlation_sequences(analysis, game, cluster_params)
        elif generation_strategy == 'sync_analysis':
            sequences = self._generate_sync_sequences(analysis, game, cluster_params)
        elif generation_strategy == 'adaptive':
            sequences = self._generate_adaptive_sequences(analysis, game, cluster_params)
        else:  # balanced
            sequences = self._generate_balanced_sequences(analysis, game, cluster_params)

        generation_result['sequences'] = sequences[:sequence_count]
        generation_result['metadata']['generation_quality'] = self._calculate_generation_quality(sequences)

        self.logger.debug(f"Génération terminée: {self.get_rollout_name()}, {len(sequences)} séquences")
        return generation_result

    def _generate_short_pattern_sequences(self, analysis: Dict, game: BaccaratGame,
                                        cluster_params: Dict) -> List[Dict]:
        """Génération spécialisée patterns courts"""
        sequences = []

        # Paramètres spécialisés pour patterns courts
        pattern_length = 3  # Court par définition
        aggressiveness = 0.7  # Plus agressif pour patterns courts

        # Générer séquences basées sur patterns courts récents
        recent_so = game.get_so_sequence()[-pattern_length:] if game.get_so_sequence() else []

        if recent_so:
            # Extrapoler patterns courts
            for i in range(8):  # Générer 8 séquences
                sequence = self._extrapolate_short_pattern(recent_so, aggressiveness)
                sequences.append({
                    'sequence_data': sequence,
                    'pattern_type': 'short',
                    'confidence': 0.6 + (aggressiveness * 0.1),
                    'source': 'short_pattern_extrapolation'
                })
        else:
            # Fallback si pas assez de données
            for i in range(8):
                sequences.append({
                    'sequence_data': ['S', 'O', 'S'],
                    'pattern_type': 'short_fallback',
                    'confidence': 0.3,
                    'source': 'fallback_generation'
                })

        return sequences

    def _generate_long_pattern_sequences(self, analysis: Dict, game: BaccaratGame,
                                       cluster_params: Dict) -> List[Dict]:
        """Génération spécialisée patterns longs"""
        sequences = []

        # Paramètres spécialisés pour patterns longs
        pattern_length = 8  # Long par définition
        stability = 0.8  # Plus stable pour patterns longs

        # Générer séquences basées sur patterns longs
        full_so = game.get_so_sequence()

        if len(full_so) >= pattern_length:
            # Analyser patterns longs
            for i in range(8):
                sequence = self._extrapolate_long_pattern(full_so, pattern_length, stability)
                sequences.append({
                    'sequence_data': sequence,
                    'pattern_type': 'long',
                    'confidence': 0.45 + (stability * 0.1),
                    'source': 'long_pattern_analysis'
                })
        else:
            # Fallback si pas assez de données
            for i in range(8):
                sequences.append({
                    'sequence_data': ['S', 'S', 'O'],
                    'pattern_type': 'long_fallback',
                    'confidence': 0.3,
                    'source': 'fallback_generation'
                })

        return sequences

    def _generate_correlation_sequences(self, analysis: Dict, game: BaccaratGame,
                                      cluster_params: Dict) -> List[Dict]:
        """Génération spécialisée corrélations"""
        sequences = []

        # Paramètres spécialisés pour corrélations
        correlation_depth = 5
        correlation_threshold = 0.65

        # Analyser corrélations entre index
        index_correlations = self._analyze_index_correlations(game, correlation_depth)

        if index_correlations:
            for i in range(8):
                sequence = self._generate_from_correlations(index_correlations, correlation_threshold)
                sequences.append({
                    'sequence_data': sequence,
                    'pattern_type': 'correlation',
                    'confidence': correlation_threshold,
                    'source': 'index_correlation_analysis'
                })
        else:
            # Fallback
            for i in range(8):
                sequences.append({
                    'sequence_data': ['O', 'S', 'O'],
                    'pattern_type': 'correlation_fallback',
                    'confidence': 0.3,
                    'source': 'fallback_generation'
                })

        return sequences

    def _generate_sync_sequences(self, analysis: Dict, game: BaccaratGame,
                               cluster_params: Dict) -> List[Dict]:
        """Génération spécialisée SYNC/DESYNC"""
        sequences = []

        # Paramètres spécialisés pour SYNC/DESYNC
        sync_weight = 0.6
        state_prediction_depth = 4

        # Prédire évolution états SYNC/DESYNC
        current_state = game.current_sync_state

        for i in range(8):
            sequence = self._predict_sync_evolution(game, current_state,
                                                  sync_weight, state_prediction_depth)
            sequences.append({
                'sequence_data': sequence,
                'pattern_type': 'sync_desync',
                'confidence': sync_weight,
                'source': 'sync_state_prediction'
            })

        return sequences

    def _generate_adaptive_sequences(self, analysis: Dict, game: BaccaratGame,
                                   cluster_params: Dict) -> List[Dict]:
        """Génération adaptative selon contexte"""
        sequences = []

        # Paramètres adaptatifs
        adaptation_rate = 0.5
        context_sensitivity = 0.7

        # Adapter stratégie selon contexte actuel
        game_phase = self._determine_game_phase(game)

        for i in range(8):
            sequence = self._generate_adaptive_sequence(game, game_phase,
                                                      adaptation_rate, context_sensitivity)
            sequences.append({
                'sequence_data': sequence,
                'pattern_type': 'adaptive',
                'confidence': adaptation_rate,
                'source': f'adaptive_{game_phase}'
            })

        return sequences

    def _generate_balanced_sequences(self, analysis: Dict, game: BaccaratGame,
                                   cluster_params: Dict) -> List[Dict]:
        """Génération équilibrée (cluster référence)"""
        sequences = []

        # Paramètres équilibrés
        balance_factor = 0.5
        diversity_target = 0.8

        # Générer séquences équilibrées
        for i in range(8):
            sequence = self._generate_balanced_sequence(game, balance_factor, diversity_target)
            sequences.append({
                'sequence_data': sequence,
                'pattern_type': 'balanced',
                'confidence': balance_factor,
                'source': 'balanced_generation'
            })

        return sequences

    def _extrapolate_short_pattern(self, recent_so: List[str], aggressiveness: float) -> List[str]:
        """Extrapole un pattern court"""
        if not recent_so:
            return ['S', 'O', 'S']  # Pattern par défaut

        # Logique d'extrapolation simple
        last_element = recent_so[-1]
        if aggressiveness > 0.6:
            # Agressif : continuer la tendance
            return recent_so + [last_element] * 2
        else:
            # Conservateur : alterner
            next_element = 'O' if last_element == 'S' else 'S'
            return recent_so + [next_element]

    def _extrapolate_long_pattern(self, full_so: List[str], pattern_length: int,
                                stability: float) -> List[str]:
        """Extrapole un pattern long"""
        if len(full_so) < pattern_length:
            return ['S', 'O'] * 4  # Pattern par défaut

        # Analyser pattern récent
        recent_pattern = full_so[-pattern_length:]

        # Extrapoler selon stabilité
        if stability > 0.7:
            # Stable : répéter pattern
            return recent_pattern[:3]
        else:
            # Instable : inverser pattern
            inverted = ['O' if x == 'S' else 'S' for x in recent_pattern[:3]]
            return inverted

    def _analyze_index_correlations(self, game: BaccaratGame, depth: int) -> Dict:
        """Analyse corrélations entre index"""
        if len(game.hands) < depth:
            return {}

        recent_hands = game.hands[-depth:]

        # Analyser corrélations simples
        correlations = {
            'sync_result_correlation': 0.0,
            'cards_result_correlation': 0.0,
            'pattern_strength': 0.0
        }

        # Calculer corrélations basiques
        sync_states = [hand.sync_state for hand in recent_hands]
        results = [hand.result for hand in recent_hands if hand.result in ['PLAYER', 'BANKER']]

        if len(results) > 1:
            # Corrélation simple SYNC/résultat
            sync_player_count = sum(1 for i, hand in enumerate(recent_hands)
                                  if hand.sync_state == 'SYNC' and hand.result == 'PLAYER')
            correlations['sync_result_correlation'] = sync_player_count / len(results)

        return correlations

    def _generate_from_correlations(self, correlations: Dict, threshold: float) -> List[str]:
        """Génère séquence basée sur corrélations"""
        sync_correlation = correlations.get('sync_result_correlation', 0.5)

        if sync_correlation > threshold:
            return ['S', 'S', 'O']  # Pattern basé sur corrélation forte
        else:
            return ['O', 'S', 'O']  # Pattern alternatif

    def _predict_sync_evolution(self, game: BaccaratGame, current_state: str,
                              sync_weight: float, depth: int) -> List[str]:
        """Prédit évolution des états SYNC/DESYNC"""
        # Logique de prédiction simple
        if current_state == 'SYNC':
            if sync_weight > 0.6:
                return ['S', 'S', 'O']  # Maintenir SYNC
            else:
                return ['O', 'S', 'S']  # Transition vers DESYNC
        else:  # DESYNC
            if sync_weight > 0.6:
                return ['O', 'O', 'S']  # Maintenir DESYNC
            else:
                return ['S', 'O', 'O']  # Transition vers SYNC

    def _determine_game_phase(self, game: BaccaratGame) -> str:
        """Détermine la phase actuelle du jeu"""
        completion_ratio = game.pb_hands / self.config.max_manches_per_game

        if completion_ratio < 0.3:
            return 'early'
        elif completion_ratio < 0.7:
            return 'middle'
        else:
            return 'late'

    def _generate_adaptive_sequence(self, game: BaccaratGame, phase: str,
                                  adaptation_rate: float, sensitivity: float) -> List[str]:
        """Génère séquence adaptative selon phase"""
        if phase == 'early':
            return ['S', 'O', 'S']  # Exploration
        elif phase == 'middle':
            return ['O', 'S', 'O']  # Exploitation
        else:  # late
            return ['S', 'S', 'O']  # Conservation

    def _generate_balanced_sequence(self, game: BaccaratGame, balance_factor: float,
                                  diversity_target: float) -> List[str]:
        """Génère séquence équilibrée"""
        # Équilibrer S et O
        return ['S', 'O', 'S'] if balance_factor > 0.5 else ['O', 'S', 'O']

    def _calculate_generation_quality(self, sequences: List[Dict]) -> float:
        """Calcule qualité de la génération"""
        if not sequences:
            return 0.0

        # Qualité basée sur diversité et confiance moyenne
        confidences = [seq.get('confidence', 0.0) for seq in sequences]
        avg_confidence = sum(confidences) / len(confidences)

        # Diversité basée sur types de patterns
        pattern_types = set(seq.get('pattern_type', 'unknown') for seq in sequences)
        diversity = len(pattern_types) / max(1, len(sequences))

        return (avg_confidence + diversity) / 2

class PredictorRollout(UniversalRollout):
    """
    ROLLOUT 3 : PRÉDICTEUR UNIVERSEL

    Prédit la prochaine manche basé sur l'analyse et les séquences générées
    Applique CENTRALIZED CONFIGURATION PATTERN selon recherches_centralisation_methodes.md
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        super().__init__(cluster_id, 3, config)

    def predict_next_hand(self, analysis: Dict[str, Any], generation: Dict[str, Any],
                         game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE UNIVERSELLE : Prédit la prochaine manche

        CENTRALIZED CONFIGURATION PATTERN :
        AZRConfig → UniversalMethod(cluster_id, config) → Comportement adapté
        """
        cluster_params = self.cluster_params

        # PARAMETERIZATION : Transformer valeurs codées en paramètres configurables
        prediction_strategy = cluster_params.get('focus', 'balanced')
        confidence_threshold = cluster_params.get('confidence_threshold', 0.5)
        risk_tolerance = cluster_params.get('risk_tolerance', 'medium')

        prediction_result = {
            'cluster_id': self.cluster_id,
            'rollout_id': self.rollout_id,
            'prediction_strategy': prediction_strategy,
            'next_hand_prediction': None,
            'prediction_confidence': 0.0,
            'prediction_reasoning': '',
            'metadata': {
                'confidence_threshold': confidence_threshold,
                'risk_tolerance': risk_tolerance,
                'analysis_quality': analysis.get('metadata', {}).get('quality', 0.0),
                'generation_quality': generation.get('metadata', {}).get('generation_quality', 0.0)
            },
            'timestamp': datetime.now()
        }

        # METHOD DISPATCH : Délégation selon paramètres
        if prediction_strategy == 'short_patterns':
            prediction = self._predict_from_short_patterns(analysis, generation, game, cluster_params)
        elif prediction_strategy == 'long_patterns':
            prediction = self._predict_from_long_patterns(analysis, generation, game, cluster_params)
        elif prediction_strategy == 'correlations':
            prediction = self._predict_from_correlations(analysis, generation, game, cluster_params)
        elif prediction_strategy == 'sync_analysis':
            prediction = self._predict_from_sync_analysis(analysis, generation, game, cluster_params)
        elif prediction_strategy == 'adaptive':
            prediction = self._predict_adaptive(analysis, generation, game, cluster_params)
        else:  # balanced
            prediction = self._predict_balanced(analysis, generation, game, cluster_params)

        # Mettre à jour le résultat avec la prédiction
        prediction_result.update(prediction)

        # Validation de la confiance
        if prediction_result['prediction_confidence'] < confidence_threshold:
            prediction_result['next_hand_prediction'] = 'WAIT'
            prediction_result['prediction_reasoning'] += ' [Confiance insuffisante]'

        self.logger.debug(f"Prédiction terminée: {self.get_rollout_name()}, "
                         f"Prédiction: {prediction_result['next_hand_prediction']}")
        return prediction_result

    def _predict_from_short_patterns(self, analysis: Dict, generation: Dict,
                                   game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction spécialisée patterns courts"""
        sequences = generation.get('sequences', [])

        if not sequences:
            return {
                'next_hand_prediction': 'WAIT',
                'prediction_confidence': 0.0,
                'prediction_reasoning': 'Aucune séquence générée'
            }

        # Analyser séquences courtes
        short_sequences = [seq for seq in sequences if seq.get('pattern_type') == 'short']

        if short_sequences:
            # Prendre la séquence avec la plus haute confiance
            best_sequence = max(short_sequences, key=lambda x: x.get('confidence', 0.0))
            sequence_data = best_sequence.get('sequence_data', [])

            if sequence_data:
                # Prédire basé sur le premier élément de la séquence
                next_so = sequence_data[0]
                prediction = self._convert_so_to_pb_prediction(next_so, game)

                return {
                    'next_hand_prediction': prediction,
                    'prediction_confidence': best_sequence.get('confidence', 0.0),
                    'prediction_reasoning': f'Pattern court: {sequence_data[:3]}'
                }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Aucun pattern court valide'
        }

    def _predict_from_long_patterns(self, analysis: Dict, generation: Dict,
                                  game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction spécialisée patterns longs"""
        sequences = generation.get('sequences', [])

        # Analyser séquences longues
        long_sequences = [seq for seq in sequences if seq.get('pattern_type') == 'long']

        if long_sequences:
            # Prendre la séquence avec la plus haute confiance
            best_sequence = max(long_sequences, key=lambda x: x.get('confidence', 0.0))
            sequence_data = best_sequence.get('sequence_data', [])

            if sequence_data:
                next_so = sequence_data[0]
                prediction = self._convert_so_to_pb_prediction(next_so, game)

                return {
                    'next_hand_prediction': prediction,
                    'prediction_confidence': best_sequence.get('confidence', 0.0),
                    'prediction_reasoning': f'Pattern long: {sequence_data[:3]}'
                }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Aucun pattern long valide'
        }

    def _predict_from_correlations(self, analysis: Dict, generation: Dict,
                                 game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction spécialisée corrélations"""
        sequences = generation.get('sequences', [])

        # Analyser séquences de corrélation
        correlation_sequences = [seq for seq in sequences if seq.get('pattern_type') == 'correlation']

        if correlation_sequences:
            best_sequence = max(correlation_sequences, key=lambda x: x.get('confidence', 0.0))
            sequence_data = best_sequence.get('sequence_data', [])

            if sequence_data:
                next_so = sequence_data[0]
                prediction = self._convert_so_to_pb_prediction(next_so, game)

                return {
                    'next_hand_prediction': prediction,
                    'prediction_confidence': best_sequence.get('confidence', 0.0),
                    'prediction_reasoning': f'Corrélation: {sequence_data[:3]}'
                }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Aucune corrélation valide'
        }

    def _predict_from_sync_analysis(self, analysis: Dict, generation: Dict,
                                  game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction spécialisée SYNC/DESYNC"""
        sequences = generation.get('sequences', [])

        # Analyser séquences SYNC/DESYNC
        sync_sequences = [seq for seq in sequences if seq.get('pattern_type') == 'sync_desync']

        if sync_sequences:
            best_sequence = max(sync_sequences, key=lambda x: x.get('confidence', 0.0))
            sequence_data = best_sequence.get('sequence_data', [])

            if sequence_data:
                next_so = sequence_data[0]
                prediction = self._convert_so_to_pb_prediction(next_so, game)

                return {
                    'next_hand_prediction': prediction,
                    'prediction_confidence': best_sequence.get('confidence', 0.0),
                    'prediction_reasoning': f'SYNC/DESYNC: {game.current_sync_state} → {sequence_data[:3]}'
                }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Aucune analyse SYNC valide'
        }

    def _predict_adaptive(self, analysis: Dict, generation: Dict,
                        game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction adaptative selon contexte"""
        sequences = generation.get('sequences', [])

        # Analyser séquences adaptatives
        adaptive_sequences = [seq for seq in sequences if seq.get('pattern_type') == 'adaptive']

        if adaptive_sequences:
            best_sequence = max(adaptive_sequences, key=lambda x: x.get('confidence', 0.0))
            sequence_data = best_sequence.get('sequence_data', [])

            if sequence_data:
                next_so = sequence_data[0]
                prediction = self._convert_so_to_pb_prediction(next_so, game)

                # Adapter confiance selon phase du jeu
                game_phase = self._determine_game_phase(game)
                confidence_modifier = {
                    'early': 0.8,    # Moins confiant en début
                    'middle': 1.0,   # Confiance normale
                    'late': 1.2      # Plus confiant en fin
                }.get(game_phase, 1.0)

                adjusted_confidence = min(1.0, best_sequence.get('confidence', 0.0) * confidence_modifier)

                return {
                    'next_hand_prediction': prediction,
                    'prediction_confidence': adjusted_confidence,
                    'prediction_reasoning': f'Adaptatif {game_phase}: {sequence_data[:3]}'
                }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Aucune adaptation valide'
        }

    def _predict_balanced(self, analysis: Dict, generation: Dict,
                        game: BaccaratGame, cluster_params: Dict) -> Dict:
        """Prédiction équilibrée (cluster référence)"""
        sequences = generation.get('sequences', [])

        if not sequences:
            return {
                'next_hand_prediction': 'WAIT',
                'prediction_confidence': 0.0,
                'prediction_reasoning': 'Aucune séquence disponible'
            }

        # Prendre toutes les séquences et faire une moyenne pondérée
        total_confidence = 0.0
        so_votes = {'S': 0, 'O': 0}

        for sequence in sequences:
            sequence_data = sequence.get('sequence_data', [])
            confidence = sequence.get('confidence', 0.0)

            if sequence_data:
                next_so = sequence_data[0]
                so_votes[next_so] += confidence
                total_confidence += confidence

        if total_confidence > 0:
            # Prédiction basée sur vote pondéré
            best_so = max(so_votes.keys(), key=lambda x: so_votes[x])
            prediction = self._convert_so_to_pb_prediction(best_so, game)
            avg_confidence = total_confidence / len(sequences)

            return {
                'next_hand_prediction': prediction,
                'prediction_confidence': avg_confidence,
                'prediction_reasoning': f'Vote équilibré: S={so_votes["S"]:.2f}, O={so_votes["O"]:.2f}'
            }

        return {
            'next_hand_prediction': 'WAIT',
            'prediction_confidence': 0.0,
            'prediction_reasoning': 'Vote équilibré insuffisant'
        }

    def _convert_so_to_pb_prediction(self, so_prediction: str, game: BaccaratGame) -> str:
        """
        Convertit une prédiction S/O en prédiction P/B

        LOGIQUE :
        - S (Same) : Même résultat que la dernière manche P/B
        - O (Opposite) : Résultat opposé à la dernière manche P/B
        """
        last_pb_result = game.last_pb_result

        if last_pb_result is None:
            # Première manche, prédiction par défaut
            return 'PLAYER'

        if so_prediction == 'S':
            # Same : répéter le dernier résultat
            return last_pb_result
        elif so_prediction == 'O':
            # Opposite : inverser le dernier résultat
            return 'BANKER' if last_pb_result == 'PLAYER' else 'PLAYER'
        else:
            # Cas imprévu
            return 'WAIT'

    def _determine_game_phase(self, game: BaccaratGame) -> str:
        """Détermine la phase actuelle du jeu"""
        completion_ratio = game.pb_hands / self.config.max_manches_per_game

        if completion_ratio < 0.3:
            return 'early'
        elif completion_ratio < 0.7:
            return 'middle'
        else:
            return 'late'

################################################################################
#                                                                              #
#  🏗️ SECTION 5 : ARCHITECTURE CLUSTERS ET COORDINATION                       #
#                                                                              #
################################################################################

class AZRCluster:
    """
    Cluster AZR contenant 3 rollouts universels

    Applique CENTRALIZED CONFIGURATION PATTERN pour tous les rollouts
    Utilise threading pour optimisation CPU/mémoire
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        self.cluster_id = cluster_id
        self.config = config
        self.cluster_params = config.get_cluster_params(cluster_id)
        self.logger = logging.getLogger(f"{__name__}.Cluster{cluster_id}")

        # Initialiser les 3 rollouts universels
        self.analyzer = AnalyzerRollout(cluster_id, config)
        self.generator = GeneratorRollout(cluster_id, config)
        self.predictor = PredictorRollout(cluster_id, config)

        # État du cluster
        self.is_active = True
        self.performance_metrics = {
            'predictions_made': 0,
            'correct_predictions': 0,
            'accuracy': 0.0,
            'avg_confidence': 0.0
        }

        self.logger.info(f"Cluster {cluster_id} initialisé: {self.cluster_params.get('name', 'Unknown')}")

    def process_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        Traite l'état du jeu avec les 3 rollouts en séquence

        TEMPLATE METHOD PATTERN : Séquence fixe avec paramètres variables
        """
        try:
            # ROLLOUT 1 : Analyse
            analysis = self.analyzer.analyze_game_state(game)

            # ROLLOUT 2 : Génération
            generation = self.generator.generate_sequences(analysis, game)

            # ROLLOUT 3 : Prédiction
            prediction = self.predictor.predict_next_hand(analysis, generation, game)

            # Résultat consolidé
            cluster_result = {
                'cluster_id': self.cluster_id,
                'cluster_name': self.cluster_params.get('name', f'Cluster{self.cluster_id}'),
                'analysis': analysis,
                'generation': generation,
                'prediction': prediction,
                'processing_timestamp': datetime.now()
            }

            # Mettre à jour métriques
            self._update_metrics(prediction)

            return cluster_result

        except Exception as e:
            self.logger.error(f"Erreur traitement cluster {self.cluster_id}: {e}")
            return {
                'cluster_id': self.cluster_id,
                'error': str(e),
                'processing_timestamp': datetime.now()
            }

    def _update_metrics(self, prediction: Dict[str, Any]):
        """Met à jour les métriques de performance du cluster"""
        self.performance_metrics['predictions_made'] += 1

        # Mettre à jour confiance moyenne
        confidence = prediction.get('prediction_confidence', 0.0)
        current_avg = self.performance_metrics['avg_confidence']
        count = self.performance_metrics['predictions_made']

        self.performance_metrics['avg_confidence'] = (
            (current_avg * (count - 1) + confidence) / count
        )

    def update_prediction_accuracy(self, was_correct: bool):
        """Met à jour la précision des prédictions"""
        if was_correct:
            self.performance_metrics['correct_predictions'] += 1

        total = self.performance_metrics['predictions_made']
        if total > 0:
            self.performance_metrics['accuracy'] = (
                self.performance_metrics['correct_predictions'] / total
            )

    def get_performance_summary(self) -> Dict[str, Any]:
        """Retourne un résumé des performances du cluster"""
        return {
            'cluster_id': self.cluster_id,
            'cluster_name': self.cluster_params.get('name', f'Cluster{self.cluster_id}'),
            'specialization': self.cluster_params.get('focus', 'unknown'),
            'metrics': self.performance_metrics.copy(),
            'is_active': self.is_active
        }

class AZRClusterManager:
    """
    Gestionnaire des 8 clusters AZR

    Coordonne l'exécution parallèle et optimise l'utilisation CPU/mémoire
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.ClusterManager")

        # Initialiser les 8 clusters
        self.clusters = {}
        for cluster_id in range(config.nb_clusters):
            self.clusters[cluster_id] = AZRCluster(cluster_id, config)

        # Configuration threading
        self.executor = ThreadPoolExecutor(
            max_workers=config.nb_cores,
            thread_name_prefix="AZRCluster"
        )

        # Métriques globales
        self.global_metrics = {
            'total_predictions': 0,
            'consensus_predictions': 0,
            'best_cluster_id': 0,
            'processing_times': []
        }

        self.logger.info(f"ClusterManager initialisé: {config.nb_clusters} clusters, {config.nb_cores} threads")

    def process_all_clusters(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        Traite l'état du jeu avec tous les clusters en parallèle

        Utilise ThreadPoolExecutor pour optimisation CPU
        """
        start_time = datetime.now()

        # Soumettre tâches à tous les clusters
        futures = {}
        for cluster_id, cluster in self.clusters.items():
            if cluster.is_active:
                future = self.executor.submit(cluster.process_game_state, game)
                futures[cluster_id] = future

        # Collecter résultats
        cluster_results = {}
        for cluster_id, future in futures.items():
            try:
                result = future.result(timeout=5.0)  # Timeout 5 secondes
                cluster_results[cluster_id] = result
            except Exception as e:
                self.logger.error(f"Timeout/erreur cluster {cluster_id}: {e}")
                cluster_results[cluster_id] = {
                    'cluster_id': cluster_id,
                    'error': f'Timeout: {e}',
                    'processing_timestamp': datetime.now()
                }

        # Calculer consensus
        consensus = self._calculate_consensus(cluster_results)

        # Métriques de traitement
        processing_time = (datetime.now() - start_time).total_seconds()
        self.global_metrics['processing_times'].append(processing_time)

        return {
            'cluster_results': cluster_results,
            'consensus': consensus,
            'processing_time_seconds': processing_time,
            'active_clusters': len([c for c in self.clusters.values() if c.is_active]),
            'timestamp': datetime.now()
        }

    def _calculate_consensus(self, cluster_results: Dict[int, Dict]) -> Dict[str, Any]:
        """
        Calcule le consensus entre tous les clusters

        Utilise vote pondéré par confiance et performance historique
        """
        predictions = {}
        confidences = {}

        # Collecter prédictions valides
        for cluster_id, result in cluster_results.items():
            if 'error' not in result and 'prediction' in result:
                prediction_data = result['prediction']
                next_prediction = prediction_data.get('next_hand_prediction')
                confidence = prediction_data.get('prediction_confidence', 0.0)

                if next_prediction and next_prediction != 'WAIT':
                    if next_prediction not in predictions:
                        predictions[next_prediction] = []
                        confidences[next_prediction] = []

                    # Pondérer par performance historique du cluster
                    cluster = self.clusters.get(cluster_id)
                    if cluster:
                        historical_accuracy = cluster.performance_metrics.get('accuracy', 0.5)
                        weighted_confidence = confidence * (0.5 + historical_accuracy)

                        predictions[next_prediction].append(cluster_id)
                        confidences[next_prediction].append(weighted_confidence)

        # Calculer consensus
        if not predictions:
            return {
                'consensus_prediction': 'WAIT',
                'consensus_confidence': 0.0,
                'voting_details': {},
                'participating_clusters': 0
            }

        # Vote pondéré
        weighted_votes = {}
        for prediction, cluster_ids in predictions.items():
            total_weight = sum(confidences[prediction])
            weighted_votes[prediction] = {
                'total_weight': total_weight,
                'cluster_count': len(cluster_ids),
                'clusters': cluster_ids,
                'avg_confidence': total_weight / len(cluster_ids) if cluster_ids else 0.0
            }

        # Prédiction gagnante
        best_prediction = max(weighted_votes.keys(),
                            key=lambda x: weighted_votes[x]['total_weight'])

        consensus_confidence = weighted_votes[best_prediction]['avg_confidence']

        return {
            'consensus_prediction': best_prediction,
            'consensus_confidence': consensus_confidence,
            'voting_details': weighted_votes,
            'participating_clusters': len([r for r in cluster_results.values() if 'error' not in r])
        }

    def get_cluster_performance_summary(self) -> Dict[str, Any]:
        """Retourne un résumé des performances de tous les clusters"""
        cluster_summaries = {}
        for cluster_id, cluster in self.clusters.items():
            cluster_summaries[cluster_id] = cluster.get_performance_summary()

        # Statistiques globales
        total_predictions = sum(c.performance_metrics['predictions_made'] for c in self.clusters.values())
        avg_accuracy = sum(c.performance_metrics['accuracy'] for c in self.clusters.values()) / len(self.clusters)

        return {
            'cluster_summaries': cluster_summaries,
            'global_stats': {
                'total_predictions': total_predictions,
                'average_accuracy': avg_accuracy,
                'active_clusters': len([c for c in self.clusters.values() if c.is_active]),
                'avg_processing_time': sum(self.global_metrics['processing_times'][-10:]) / min(10, len(self.global_metrics['processing_times'])) if self.global_metrics['processing_times'] else 0.0
            }
        }

    def shutdown(self):
        """Arrêt propre du gestionnaire de clusters"""
        self.logger.info("Arrêt du ClusterManager...")
        self.executor.shutdown(wait=True)
        self.logger.info("ClusterManager arrêté")

################################################################################
#                                                                              #
#  🎮 SECTION 6 : INTERFACE GRAPHIQUE 9 BOUTONS                               #
#                                                                              #
################################################################################

class AZRBaccaratInterface:
    """
    Interface graphique avec 9 boutons (3 issues × 3 parités)

    Conforme aux spécifications :
    - 3 issues : PLAYER, BANKER, TIE
    - 3 parités : pair_4, pair_6, impair_5
    - Total : 9 boutons pour toutes les combinaisons
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.Interface")

        # Composants système
        self.counting_engine = BaccaratCountingEngine(config)
        self.cluster_manager = AZRClusterManager(config)

        # État de l'interface
        self.current_game = None
        self.burn_initialized = False

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🧠 AZR Baccarat Predictor - Refonte Complète")
        self.root.geometry("1200x800")

        # Variables d'affichage
        self.prediction_var = tk.StringVar(value="Initialisation...")
        self.confidence_var = tk.StringVar(value="0.0%")
        self.game_stats_var = tk.StringVar(value="Partie: 0/60")

        self._create_interface()

        self.logger.info("Interface graphique initialisée")

    def _create_interface(self):
        """Crée l'interface graphique complète"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Section initialisation brûlage
        self._create_burn_section(main_frame)

        # Section affichage prédictions
        self._create_prediction_display(main_frame)

        # Section 9 boutons (3×3)
        self._create_nine_buttons_section(main_frame)

        # Section contrôles
        self._create_controls_section(main_frame)

        # Section statistiques
        self._create_stats_section(main_frame)

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation du brûlage"""
        burn_frame = ttk.LabelFrame(parent, text="🔥 Initialisation Brûlage", padding="10")
        burn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(burn_frame, text="Nombre de cartes brûlées:").pack(side=tk.LEFT)

        # Boutons pour brûlage PAIR
        pair_frame = ttk.Frame(burn_frame)
        pair_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(pair_frame, text="PAIR:").pack()
        pair_buttons_frame = ttk.Frame(pair_frame)
        pair_buttons_frame.pack()

        for count in [2, 4, 6, 8, 10]:
            btn = ttk.Button(pair_buttons_frame, text=str(count), width=3,
                           command=lambda c=count: self._initialize_burn(c, 'PAIR'))
            btn.pack(side=tk.LEFT, padx=1)

        # Boutons pour brûlage IMPAIR
        impair_frame = ttk.Frame(burn_frame)
        impair_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(impair_frame, text="IMPAIR:").pack()
        impair_buttons_frame = ttk.Frame(impair_frame)
        impair_buttons_frame.pack()

        for count in [3, 5, 7, 9, 11]:
            btn = ttk.Button(impair_buttons_frame, text=str(count), width=3,
                           command=lambda c=count: self._initialize_burn(c, 'IMPAIR'))
            btn.pack(side=tk.LEFT, padx=1)

    def _create_prediction_display(self, parent):
        """Crée la section d'affichage des prédictions"""
        pred_frame = ttk.LabelFrame(parent, text="🎯 Prédictions AZR", padding="10")
        pred_frame.pack(fill=tk.X, pady=(0, 10))

        # Prédiction principale
        main_pred_frame = ttk.Frame(pred_frame)
        main_pred_frame.pack(fill=tk.X)

        ttk.Label(main_pred_frame, text="Prédiction Consensus:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        ttk.Label(main_pred_frame, textvariable=self.prediction_var,
                 font=("Arial", 14, "bold"), foreground="blue").pack(side=tk.LEFT, padx=(10, 0))

        # Confiance
        conf_frame = ttk.Frame(pred_frame)
        conf_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(conf_frame, text="Confiance:", font=("Arial", 10)).pack(side=tk.LEFT)
        ttk.Label(conf_frame, textvariable=self.confidence_var,
                 font=("Arial", 10, "bold"), foreground="green").pack(side=tk.LEFT, padx=(10, 0))

        # Statistiques partie
        stats_frame = ttk.Frame(pred_frame)
        stats_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(stats_frame, text="Progression:", font=("Arial", 10)).pack(side=tk.LEFT)
        ttk.Label(stats_frame, textvariable=self.game_stats_var,
                 font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))

    def _create_nine_buttons_section(self, parent):
        """
        Crée la section des 9 boutons (3 issues × 3 parités)

        LAYOUT :
        ┌─────────────┬─────────────┬─────────────┐
        │   PLAYER    │   BANKER    │     TIE     │
        ├─────────────┼─────────────┼─────────────┤
        │  pair_4     │   pair_4    │   pair_4    │
        │  pair_6     │   pair_6    │   pair_6    │
        │  impair_5   │  impair_5   │  impair_5   │
        └─────────────┴─────────────┴─────────────┘
        """
        buttons_frame = ttk.LabelFrame(parent, text="🎲 Saisie Manches (9 Boutons)", padding="15")
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Configuration grid
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)

        # Headers
        headers = ["PLAYER", "BANKER", "TIE"]
        colors = ["#1E3A8A", "#B91C1C", "#166534"]  # Bleu, Rouge, Vert

        for col, (header, color) in enumerate(zip(headers, colors)):
            header_label = tk.Label(buttons_frame, text=header,
                                  font=("Arial", 14, "bold"),
                                  bg=color, fg="white", pady=10)
            header_label.grid(row=0, column=col, sticky="ew", padx=2, pady=2)

        # Boutons pour chaque combinaison
        parities = [
            ("pair_4", "4 cartes (P:2, B:2)"),
            ("pair_6", "6 cartes (P:3, B:3)"),
            ("impair_5", "5 cartes (P:2/3, B:3/2)")
        ]

        for row, (parity_code, parity_desc) in enumerate(parities, start=1):
            for col, (result, color) in enumerate(zip(headers, colors)):
                btn_text = f"{result}\n{parity_desc}"

                btn = tk.Button(buttons_frame, text=btn_text,
                              font=("Arial", 9, "bold"),
                              bg=color, fg="white",
                              relief="raised", bd=3,
                              height=3, width=20,
                              command=lambda r=result, p=parity_code: self._process_hand(r, p))
                btn.grid(row=row, column=col, sticky="ew", padx=3, pady=3)

    def _create_controls_section(self, parent):
        """Crée la section des contrôles"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Boutons de contrôle
        ttk.Button(controls_frame, text="💾 Sauvegarder",
                  command=self._save_game).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(controls_frame, text="🔄 Nouvelle Partie",
                  command=self._new_game).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="📊 Statistiques",
                  command=self._show_statistics).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="❌ Quitter",
                  command=self._quit_application).pack(side=tk.RIGHT)

    def _create_stats_section(self, parent):
        """Crée la section des statistiques en temps réel"""
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistiques Temps Réel", padding="10")
        stats_frame.pack(fill=tk.X)

        # Créer un Text widget pour affichage des stats
        self.stats_text = tk.Text(stats_frame, height=6, width=80,
                                 font=("Courier", 9), state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)

    def _initialize_burn(self, count: int, parity: str):
        """Initialise le brûlage avec le nombre de cartes et la parité"""
        if self.burn_initialized:
            messagebox.showwarning("Attention", "Brûlage déjà initialisé pour cette partie")
            return

        # Créer nouvelle partie
        initial_sync_state = self.config.initial_sync_mapping[parity]

        self.current_game = BaccaratGame(
            game_number=1,
            burn_cards_count=count,
            burn_parity=parity,
            initial_sync_state=initial_sync_state,
            current_sync_state=initial_sync_state
        )

        self.burn_initialized = True

        # Mettre à jour affichage
        self._update_display()

        self.logger.info(f"Brûlage initialisé: {count} cartes {parity} → {initial_sync_state}")
        messagebox.showinfo("Brûlage Initialisé",
                          f"Brûlage: {count} cartes {parity}\nÉtat initial: {initial_sync_state}")

    def _process_hand(self, result: str, parity_code: str):
        """
        Traite une main avec résultat et parité

        Args:
            result: 'PLAYER', 'BANKER', 'TIE'
            parity_code: 'pair_4', 'pair_6', 'impair_5'
        """
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser le brûlage")
            return

        if self.current_game.is_complete():
            messagebox.showinfo("Partie Terminée",
                              f"Partie complète: {self.config.max_manches_per_game} manches P/B atteintes")
            return

        try:
            # Convertir parity_code en nombre de cartes
            cards_mapping = {
                'pair_4': (2, 2),    # Player: 2, Banker: 2
                'pair_6': (3, 3),    # Player: 3, Banker: 3
                'impair_5': (2, 3)   # Player: 2, Banker: 3 (ou inverse)
            }

            player_cards, banker_cards = cards_mapping.get(parity_code, (2, 2))

            # Traiter la main avec le moteur de comptage
            hand = self.counting_engine.process_hand(
                self.current_game, result, player_cards, banker_cards
            )

            # Obtenir prédictions de tous les clusters
            cluster_results = self.cluster_manager.process_all_clusters(self.current_game)

            # Mettre à jour affichage
            self._update_display(cluster_results)

            # Log de la main traitée
            self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")

        except Exception as e:
            self.logger.error(f"Erreur traitement main: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du traitement: {e}")

    def _update_display(self, cluster_results: Dict = None):
        """Met à jour l'affichage de l'interface"""
        if not self.current_game:
            return

        # Mettre à jour statistiques de partie
        self.game_stats_var.set(
            f"Partie: {self.current_game.pb_hands}/{self.config.max_manches_per_game} | "
            f"Total: {self.current_game.total_hands} | TIE: {self.current_game.tie_hands}"
        )

        # Mettre à jour prédictions si disponibles
        if cluster_results and 'consensus' in cluster_results:
            consensus = cluster_results['consensus']
            prediction = consensus.get('consensus_prediction', 'WAIT')
            confidence = consensus.get('consensus_confidence', 0.0)

            self.prediction_var.set(prediction)
            self.confidence_var.set(f"{confidence:.1%}")

            # Mettre à jour statistiques détaillées
            self._update_stats_display(cluster_results)
        else:
            self.prediction_var.set("En attente...")
            self.confidence_var.set("0.0%")

    def _update_stats_display(self, cluster_results: Dict):
        """Met à jour l'affichage des statistiques détaillées"""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)

        # Consensus
        consensus = cluster_results.get('consensus', {})
        self.stats_text.insert(tk.END, f"🎯 CONSENSUS: {consensus.get('consensus_prediction', 'N/A')} "
                                      f"({consensus.get('consensus_confidence', 0.0):.1%})\n")

        # Détails par cluster
        self.stats_text.insert(tk.END, "\n📊 DÉTAILS PAR CLUSTER:\n")
        cluster_data = cluster_results.get('cluster_results', {})

        for cluster_id in sorted(cluster_data.keys()):
            result = cluster_data[cluster_id]
            if 'error' not in result and 'prediction' in result:
                pred_data = result['prediction']
                cluster_name = result.get('cluster_name', f'C{cluster_id}')
                prediction = pred_data.get('next_hand_prediction', 'N/A')
                confidence = pred_data.get('prediction_confidence', 0.0)

                self.stats_text.insert(tk.END, f"  {cluster_name}: {prediction} ({confidence:.1%})\n")
            else:
                self.stats_text.insert(tk.END, f"  Cluster {cluster_id}: ERREUR\n")

        # Performance globale
        perf_summary = self.cluster_manager.get_cluster_performance_summary()
        global_stats = perf_summary.get('global_stats', {})

        self.stats_text.insert(tk.END, f"\n⚡ PERFORMANCE GLOBALE:\n")
        self.stats_text.insert(tk.END, f"  Prédictions totales: {global_stats.get('total_predictions', 0)}\n")
        self.stats_text.insert(tk.END, f"  Précision moyenne: {global_stats.get('average_accuracy', 0.0):.1%}\n")
        self.stats_text.insert(tk.END, f"  Clusters actifs: {global_stats.get('active_clusters', 0)}/8\n")
        self.stats_text.insert(tk.END, f"  Temps traitement: {global_stats.get('avg_processing_time', 0.0):.3f}s\n")

        self.stats_text.config(state=tk.DISABLED)
        self.stats_text.see(tk.END)

    def _save_game(self):
        """Sauvegarde la partie actuelle"""
        if not self.current_game:
            messagebox.showwarning("Attention", "Aucune partie en cours")
            return

        try:
            # Créer données de sauvegarde
            save_data = {
                'game': {
                    'game_number': self.current_game.game_number,
                    'burn_cards_count': self.current_game.burn_cards_count,
                    'burn_parity': self.current_game.burn_parity,
                    'initial_sync_state': self.current_game.initial_sync_state,
                    'hands': [
                        {
                            'hand_number': hand.hand_number,
                            'pb_hand_number': hand.pb_hand_number,
                            'cards_distributed': hand.cards_distributed,
                            'cards_parity': hand.cards_parity,
                            'cards_category': hand.cards_category,
                            'sync_state': hand.sync_state,
                            'combined_state': hand.combined_state,
                            'result': hand.result,
                            'so_conversion': hand.so_conversion,
                            'timestamp': hand.timestamp.isoformat()
                        }
                        for hand in self.current_game.hands
                    ]
                },
                'cluster_performance': self.cluster_manager.get_cluster_performance_summary(),
                'save_timestamp': datetime.now().isoformat()
            }

            # Sauvegarder dans fichier JSON
            filename = f"azr_baccarat_game_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Sauvegarde", f"Partie sauvegardée: {filename}")
            self.logger.info(f"Partie sauvegardée: {filename}")

        except Exception as e:
            self.logger.error(f"Erreur sauvegarde: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _new_game(self):
        """Démarre une nouvelle partie"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Nouvelle Partie",
                                     "Voulez-vous vraiment démarrer une nouvelle partie ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        # Réinitialiser état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.prediction_var.set("Nouvelle partie - Initialisez le brûlage")
        self.confidence_var.set("0.0%")
        self.game_stats_var.set("Partie: 0/60")

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "🆕 Nouvelle partie initialisée\n")
        self.stats_text.insert(tk.END, "Veuillez initialiser le brûlage pour commencer\n")
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Nouvelle partie initialisée")

    def _show_statistics(self):
        """Affiche les statistiques détaillées dans une fenêtre séparée"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Statistiques Détaillées AZR")
        stats_window.geometry("800x600")

        # Text widget avec scrollbar
        text_frame = ttk.Frame(stats_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        stats_text = tk.Text(text_frame, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)

        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        stats_text.config(yscrollcommand=scrollbar.set)

        # Générer statistiques complètes
        perf_summary = self.cluster_manager.get_cluster_performance_summary()

        stats_content = "🧠 STATISTIQUES COMPLÈTES AZR BACCARAT\n"
        stats_content += "=" * 50 + "\n\n"

        # Statistiques globales
        global_stats = perf_summary.get('global_stats', {})
        stats_content += "📈 PERFORMANCE GLOBALE:\n"
        stats_content += f"  • Prédictions totales: {global_stats.get('total_predictions', 0)}\n"
        stats_content += f"  • Précision moyenne: {global_stats.get('average_accuracy', 0.0):.2%}\n"
        stats_content += f"  • Clusters actifs: {global_stats.get('active_clusters', 0)}/8\n"
        stats_content += f"  • Temps traitement moyen: {global_stats.get('avg_processing_time', 0.0):.3f}s\n\n"

        # Détails par cluster
        cluster_summaries = perf_summary.get('cluster_summaries', {})
        stats_content += "🎯 PERFORMANCE PAR CLUSTER:\n"

        for cluster_id in sorted(cluster_summaries.keys()):
            cluster_data = cluster_summaries[cluster_id]
            metrics = cluster_data.get('metrics', {})

            stats_content += f"\n  Cluster {cluster_id} - {cluster_data.get('cluster_name', 'Unknown')}:\n"
            stats_content += f"    Spécialisation: {cluster_data.get('specialization', 'N/A')}\n"
            stats_content += f"    Prédictions: {metrics.get('predictions_made', 0)}\n"
            stats_content += f"    Correctes: {metrics.get('correct_predictions', 0)}\n"
            stats_content += f"    Précision: {metrics.get('accuracy', 0.0):.2%}\n"
            stats_content += f"    Confiance moy.: {metrics.get('avg_confidence', 0.0):.2%}\n"
            stats_content += f"    Actif: {'✅' if cluster_data.get('is_active', False) else '❌'}\n"

        # Partie actuelle
        if self.current_game:
            stats_content += f"\n🎲 PARTIE ACTUELLE:\n"
            stats_content += f"  • Numéro: {self.current_game.game_number}\n"
            stats_content += f"  • Brûlage: {self.current_game.burn_cards_count} cartes {self.current_game.burn_parity}\n"
            stats_content += f"  • État initial: {self.current_game.initial_sync_state}\n"
            stats_content += f"  • État actuel: {self.current_game.current_sync_state}\n"
            stats_content += f"  • Manches P/B: {self.current_game.pb_hands}/{self.config.max_manches_per_game}\n"
            stats_content += f"  • Total mains: {self.current_game.total_hands}\n"
            stats_content += f"  • TIE: {self.current_game.tie_hands}\n"
            stats_content += f"  • Conversions S/O: {self.current_game.so_conversions}\n"

        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)

    def _quit_application(self):
        """Quitte l'application proprement"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Quitter",
                                     "Voulez-vous vraiment quitter ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        self.logger.info("Fermeture de l'application")

        # Arrêt propre du cluster manager
        self.cluster_manager.shutdown()

        # Fermer interface
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Lance l'interface graphique"""
        self.logger.info("Démarrage de l'interface graphique")

        # Gestionnaire de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self._quit_application)

        # Démarrer boucle principale
        self.root.mainloop()

################################################################################
#                                                                              #
#  🚀 SECTION 7 : FONCTION PRINCIPALE ET POINT D'ENTRÉE                       #
#                                                                              #
################################################################################

def main():
    """
    Fonction principale du programme AZR Baccarat Refonte

    Initialise tous les composants et lance l'interface graphique
    """
    print("🧠 AZR BACCARAT PREDICTOR - REFONTE COMPLÈTE")
    print("=" * 50)
    print("Initialisation du système...")

    try:
        # Initialiser configuration
        config = AZRConfig()

        # Afficher informations système
        system_limits = config.get_system_limits()
        print(f"📊 Configuration système:")
        print(f"  • Clusters: {system_limits['nb_clusters']}")
        print(f"  • Rollouts par cluster: {system_limits['nb_rollouts_per_cluster']}")
        print(f"  • Total rollouts: {system_limits['total_rollouts']}")
        print(f"  • Cœurs CPU: {system_limits['nb_cores']}")
        print(f"  • Mémoire max: {system_limits['max_memory_gb']}GB")
        print(f"  • Mémoire par cluster: {system_limits['memory_per_cluster_mb']}MB")

        # Initialiser et lancer interface
        print("\n🎮 Lancement de l'interface graphique...")
        interface = AZRBaccaratInterface(config)
        interface.run()

    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}", exc_info=True)
    finally:
        print("\n👋 Arrêt du programme")

if __name__ == "__main__":
    main()

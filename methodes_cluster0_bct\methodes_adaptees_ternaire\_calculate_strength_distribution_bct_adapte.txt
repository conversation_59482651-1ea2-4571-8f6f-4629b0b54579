MÉTHODE : _calculate_strength_distribution
LIGNE DÉBUT : 8170
SIGNATURE : def _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict:
================================================================================

    def _calculate_strength_distribution(self, individual_strengths: Dict, weights: Dict) -> Dict:
"""
    ADAPTATION BCT - _calculate_strength_distribution.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la distribution pondérée des forces"""

        total_weighted = sum(strength * weights.get(strength_type, 1.0)
                           for strength_type, strength in individual_strengths.items())

        if total_weighted == 0:
            return {}

        distribution = {}
        for strength_type, strength in individual_strengths.items():
            weight = weights.get(strength_type, 1.0)
            weighted_strength = strength * weight
            distribution[strength_type] = weighted_strength / total_weighted

        return distribution


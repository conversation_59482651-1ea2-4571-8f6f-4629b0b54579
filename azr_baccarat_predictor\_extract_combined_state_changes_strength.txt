# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10536 à 10555
# Type: Méthode de la classe AZRCluster

    def _extract_combined_state_changes_strength(self, combined_impacts: Dict) -> float:
        """Extrait la force des impacts de changements d'états combinés (focus P/B et S/O)"""

        if not combined_impacts:
            return 0.0

        # Chercher métriques de force dans change_strength_metrics
        if 'change_strength_metrics' in combined_impacts:
            metrics = combined_impacts['change_strength_metrics']
            return metrics.get('average_transition_strength', 0.0)

        # Sinon analyser les types de transitions
        strength_metrics = []

        if 'transition_types_analysis' in combined_impacts:
            for transition_type, transition_data in combined_impacts['transition_types_analysis'].items():
                if 'type_strength' in transition_data:
                    strength_metrics.append(transition_data['type_strength'])

        return sum(strength_metrics) / len(strength_metrics) if strength_metrics else 0.0
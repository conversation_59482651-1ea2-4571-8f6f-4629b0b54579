MÉTHODE : _rollout_predictor
LIGNE DÉBUT : 3308
SIGNATURE : def _rollout_predictor(self, generator_result: Dict, analyzer_report: Dict) -> Dict:
================================================================================

    def _rollout_predictor(self, generator_result: Dict, analyzer_report: Dict) -> Dict:
"""
    ADAPTATION BCT - _rollout_predictor.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Rollout 3 Prédicteur - Sélection séquence optimale finale

        Évalue et sélectionne la meilleure séquence parmi les candidates.
        Utilise critères S/O prioritaires et intelligence de sélection.

        Args:
            generator_result: Résultat structuré du générateur (dictionnaire)
            analyzer_report: Rapport original de l'analyseur

        Returns:
            Dict: Prédiction finale avec confiance et justification
        """
        try:
            # Extraire les séquences du résultat structuré
            generated_sequences = generator_result.get('sequences', [])
            generation_metadata = generator_result.get('generation_metadata', {})
            generation_stats = generator_result.get('generation_stats', {})

            if not generated_sequences:
                return {
                    'prediction': None,
                    'confidence': self.config.rollout3_default_confidence,
                    'error': 'Aucune séquence candidate disponible',
                    'generator_metadata': generation_metadata
                }

            # Évaluation détaillée de chaque séquence candidate
            evaluated_sequences = []

            for i, sequence in enumerate(generated_sequences):
                # Créer un wrapper dictionnaire pour les listes AVANT l'évaluation
                if isinstance(sequence, list):
                    # Extraire les informations du résumé d'enrichissement s'il existe
                    summary = {}
                    actual_sequence_data = []

                    for item in sequence:
                        if isinstance(item, dict) and item.get('type') == 'sequence_enrichment_summary':
                            summary = item
                        elif isinstance(item, dict) and 'predicted_pbt' in item:
                            # Extraire seulement la chaîne P/B
                            pbt_value = item.get('predicted_pbt', 'B')
                            if isinstance(pbt_value, str) and pbt_value in ['P', 'B']:
                                actual_sequence_data.append(pbt_value)
                            else:
                                actual_sequence_data.append('B')  # Fallback
                        elif isinstance(item, str):
                            # S'assurer que c'est P ou B
                            if item in ['P', 'B']:
                                actual_sequence_data.append(item)
                            else:
                                actual_sequence_data.append('B')  # Fallback
                        elif isinstance(item, dict):
                            # Dictionnaire sans predicted_pbt, essayer d'autres clés
                            for key in ['pbt_result', 'result']:
                                if key in item:
                                    value = item[key]
                                    if isinstance(value, str) and value in ['P', 'B']:
                                        actual_sequence_data.append(value)
                                        break
                            else:
                                # Aucune clé valide trouvée, utiliser fallback
                                actual_sequence_data.append('B')

                    sequence_wrapper = {
                        'sequence_data': actual_sequence_data,
                        'strategy': summary.get('strategy_source', f'strategy_{i}'),
                        'justification': f'Séquence enrichie par cluster {self.cluster_id}',
                        'estimated_probability': summary.get('avg_global_confidence', self.config.rollout3_fallback_probability),
                        'enrichment_summary': summary
                    }
                else:
                    sequence_wrapper = sequence

                # Maintenant évaluer le wrapper correctement formaté
                evaluation = self._evaluate_sequence_quality(sequence_wrapper, analyzer_report)
                sequence_wrapper['evaluation'] = evaluation

                evaluated_sequences.append(sequence_wrapper)

            # Sélection de la meilleure séquence
            best_sequence = self._select_best_sequence(evaluated_sequences)

            # Calcul confiance finale du cluster (méthode calibrée AZR)
            cluster_confidence = self._calculate_cluster_confidence_azr_calibrated(best_sequence, analyzer_report)
            self.shared_memory['cluster_confidence'] = cluster_confidence

            # Conversion de la séquence P/B en séquence S/O
            pb_sequence = best_sequence.get('sequence_data', [])
            so_sequence = self._convert_pb_sequence_to_so(pb_sequence, analyzer_report)

            # Prédiction finale : séquence S/O simple
            final_prediction = {
                'sequence': so_sequence,  # Séquence S/O simple ['S', 'O', 'S']
                'strategy': best_sequence.get('strategy', 'unknown'),
                'justification': best_sequence.get('justification', 'Prédiction générée'),
                'estimated_probability': best_sequence.get('estimated_probability', self.config.rollout3_fallback_probability),
                'evaluation_score': best_sequence.get('evaluation', {}).get('total_score', self.config.probability_neutral),
                'cluster_confidence': cluster_confidence,
                'next_hand_prediction': so_sequence[0] if so_sequence else 'S',  # Premier élément S/O
                # NOUVEAU : Métadonnées du pipeline complet
                'pipeline_metadata': {
                    'generator_metadata': generation_metadata,
                    'generation_stats': generation_stats,
                    'sequences_evaluated': len(evaluated_sequences),
                    'best_sequence_index': evaluated_sequences.index(best_sequence) if best_sequence in evaluated_sequences else -1,
                    'original_pb_sequence': pb_sequence  # Garder trace de la séquence P/B originale
                }
            }

            return final_prediction

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Erreur rollout predictor cluster {self.cluster_id}: {type(e).__name__}: {e}")
            logger.error(f"Détails erreur rollout predictor cluster {self.cluster_id}: {error_details}")
            return {
                'prediction': None,
                'confidence': 0.0,
                'error': str(e)
            }

    # ========================================================================
    # 🎯 MÉTHODES MANQUANTES ROLLOUT 3 - SÉLECTION INTELLIGENTE
    # ========================================================================


# MÉTHODES DE SUPPORT ADAPTÉES POUR BCT.PY
# Source: Diverses méthodes de support AZR
# Adaptation: Système de comptage BCT avec INDEX détaillés

def _build_complete_sequence_for_rollout_bct(self) -> Dict[str, Any]:
    """
    Construit la séquence complète depuis le brûlage pour les rollouts BCT
    
    ADAPTATION BCT - ACCÈS COMPLET AVEC INDEX DÉTAILLÉS :
    - Brûlage initial (parité, état sync/desync)
    - Toutes les mains avec catégories BCT (pair_4, impair_5, pair_6)
    - États combinés BCT (pair_4_sync, impair_5_desync, etc.)
    - Tous les résultats PLAYER/BANKER/TIE
    - Toutes les conversions S/O
    """
    # Initialisation avec données de brûlage BCT
    burn_data = self._get_burn_data_for_rollout_bct()
    
    # Séquences complètes BCT
    cards_category_sequence = []        # pair_4, impair_5, pair_6
    cards_parity_sequence = []          # PAIR, IMPAIR
    sync_desync_sequence = []           # SYNC, DESYNC
    combined_states_sequence = []       # pair_4_sync, impair_5_desync, etc.
    result_sequence = []                # PLAYER, BANKER, TIE
    so_conversions_sequence = []        # S, O, --
    
    # Index pour tracking BCT
    global_hand_index = self.config.zero_value
    pb_hand_index = self.config.zero_value
    
    # Construire les séquences pour toutes les mains BCT
    for hand in self.hands_history:
        global_hand_index += self.config.one_value
        
        # Catégorie de cartes BCT (pair_4, impair_5, pair_6)
        cards_category_sequence.append(hand.cards_category)
        
        # Parité des cartes (PAIR, IMPAIR)
        cards_parity_sequence.append(hand.cards_parity)
        
        # État SYNC/DESYNC
        sync_desync_sequence.append(hand.sync_state)
        
        # État combiné BCT détaillé
        combined_states_sequence.append(hand.combined_state)
        
        # Résultat complet
        result_sequence.append(hand.result)
        
        # Conversion S/O
        so_conversions_sequence.append(hand.so_conversion)
        
        # Comptage mains P/B (exclut TIE)
        if hand.result in ['PLAYER', 'BANKER']:
            pb_hand_index += self.config.one_value
    
    # Construire la structure complète BCT
    complete_sequence = {
        'burn_data': burn_data,
        'sequences': {
            'cards_category': cards_category_sequence,      # BCT spécifique
            'cards_parity': cards_parity_sequence,          # BCT spécifique
            'sync_desync': sync_desync_sequence,
            'combined_states': combined_states_sequence,    # BCT détaillé
            'results': result_sequence,                     # BCT complet
            'so_conversions': so_conversions_sequence
        },
        'indexes': {
            'total_hands': global_hand_index,
            'pb_hands': pb_hand_index,
            'tie_hands': global_hand_index - pb_hand_index,
            'so_conversions_count': len([so for so in so_conversions_sequence if so in ['S', 'O']]),
            'bct_categories_count': {
                'pair_4': cards_category_sequence.count('pair_4'),
                'impair_5': cards_category_sequence.count('impair_5'),
                'pair_6': cards_category_sequence.count('pair_6')
            }
        },
        'statistics': self._calculate_sequence_statistics_bct(
            cards_category_sequence, cards_parity_sequence, sync_desync_sequence,
            combined_states_sequence, so_conversions_sequence
        ),
        'bct_metadata': {
            'system_conformity': True,
            'categories_used': self.config.bct_categories,
            'combined_states_used': self.config.bct_combined_states
        }
    }
    
    return complete_sequence

def _get_burn_data_for_rollout_bct(self) -> Dict[str, Any]:
    """Récupère les données de brûlage pour les rollouts BCT"""
    return {
        'burn_parity': getattr(self, 'burn_parity', self.config.default_burn_parity),
        'initial_sync_state': getattr(self, 'initial_sync_state', self.config.default_sync_state),
        'burn_cards_count': getattr(self, 'burn_cards_count', self.config.zero_value),
        'bct_burn_category': self._determine_burn_category_bct()
    }

def _determine_burn_category_bct(self) -> str:
    """Détermine la catégorie BCT du brûlage selon le nombre de cartes"""
    burn_cards = getattr(self, 'burn_cards_count', self.config.zero_value)
    
    if burn_cards == 4:
        return 'pair_4'
    elif burn_cards == 5:
        return 'impair_5'
    elif burn_cards == 6:
        return 'pair_6'
    else:
        # Fallback basé sur la parité
        burn_parity = getattr(self, 'burn_parity', self.config.default_burn_parity)
        return 'pair_4' if burn_parity == 'PAIR' else 'impair_5'

def _calculate_sequence_statistics_bct(self, cards_category_seq: List[str], 
                                      cards_parity_seq: List[str],
                                      sync_desync_seq: List[str],
                                      combined_states_seq: List[str],
                                      so_conversions_seq: List[str]) -> Dict[str, Any]:
    """Calcule les statistiques de séquence spécifiques BCT"""
    total_hands = len(cards_category_seq)
    
    if total_hands == self.config.zero_value:
        return {'total_hands': self.config.zero_value}
    
    # Statistiques catégories BCT
    pair_4_count = cards_category_seq.count('pair_4')
    impair_5_count = cards_category_seq.count('impair_5')
    pair_6_count = cards_category_seq.count('pair_6')
    
    # Statistiques états combinés BCT
    combined_stats = {}
    for state in self.config.bct_combined_states:
        combined_stats[state] = combined_states_seq.count(state)
    
    # Statistiques S/O
    s_count = so_conversions_seq.count('S')
    o_count = so_conversions_seq.count('O')
    so_total = s_count + o_count
    
    return {
        'total_hands': total_hands,
        'bct_categories': {
            'pair_4_count': pair_4_count,
            'impair_5_count': impair_5_count,
            'pair_6_count': pair_6_count,
            'pair_4_ratio': pair_4_count / total_hands,
            'impair_5_ratio': impair_5_count / total_hands,
            'pair_6_ratio': pair_6_count / total_hands
        },
        'bct_combined_states': combined_stats,
        'so_statistics': {
            's_count': s_count,
            'o_count': o_count,
            'so_total': so_total,
            's_ratio': s_count / max(so_total, self.config.one_value),
            'o_ratio': o_count / max(so_total, self.config.one_value)
        },
        'sync_desync_stats': {
            'sync_count': sync_desync_seq.count('SYNC'),
            'desync_count': sync_desync_seq.count('DESYNC'),
            'sync_ratio': sync_desync_seq.count('SYNC') / total_hands,
            'desync_ratio': sync_desync_seq.count('DESYNC') / total_hands
        }
    }

def _analyze_bct_categories_patterns(self, cards_category_sequence: List[str]) -> Dict[str, Any]:
    """Analyse les patterns des catégories BCT (pair_4, impair_5, pair_6)"""
    if len(cards_category_sequence) < self.config.rollout1_min_hands_quality:
        return {'insufficient_data': True}
    
    # Analyse consécutifs par catégorie
    consecutive_analysis = {}
    for category in self.config.bct_categories:
        consecutive_count = self._count_consecutive_bct(cards_category_sequence, category)
        consecutive_analysis[f'{category}_consecutive'] = consecutive_count
    
    # Analyse alternances spécifiques BCT
    pair_4_to_6_alternations = self._count_alternations_bct(cards_category_sequence, 'pair_4', 'pair_6')
    impair_5_isolation = self._analyze_impair_5_isolation_bct(cards_category_sequence)
    
    # Calcul des biais BCT
    total_hands = len(cards_category_sequence)
    pair_4_ratio = cards_category_sequence.count('pair_4') / total_hands
    impair_5_ratio = cards_category_sequence.count('impair_5') / total_hands
    pair_6_ratio = cards_category_sequence.count('pair_6') / total_hands
    
    # Détection biais significatifs
    impair_5_bias_detected = impair_5_ratio > self.config.rollout1_impair5_consecutive_rare
    pair_alternation_bias = pair_4_to_6_alternations > self.config.rollout1_pair46_alternation_threshold
    
    return {
        'consecutive_analysis': consecutive_analysis,
        'alternation_analysis': {
            'pair_4_to_6_alternations': pair_4_to_6_alternations,
            'impair_5_isolation_score': impair_5_isolation
        },
        'category_ratios': {
            'pair_4_ratio': pair_4_ratio,
            'impair_5_ratio': impair_5_ratio,
            'pair_6_ratio': pair_6_ratio
        },
        'bias_detection': {
            'impair_5_bias_detected': impair_5_bias_detected,
            'pair_alternation_bias_detected': pair_alternation_bias,
            'combined_rare_states_detected': self._detect_combined_rare_states_bct(cards_category_sequence)
        },
        'exploitation_potential': {
            'impair_5_exploitation': impair_5_ratio * self.config.rollout1_impair5_bias_weight,
            'pair_alternation_exploitation': pair_alternation_bias * self.config.rollout1_pair46_alternation_threshold,
            'overall_bct_bias_strength': max(impair_5_ratio, pair_alternation_bias) * self.config.bct_exploitation_base_confidence
        }
    }

def _count_consecutive_bct(self, sequence: List[str], target_category: str) -> int:
    """Compte les occurrences consécutives d'une catégorie BCT"""
    max_consecutive = self.config.zero_value
    current_consecutive = self.config.zero_value
    
    for item in sequence:
        if item == target_category:
            current_consecutive += self.config.one_value
            max_consecutive = max(max_consecutive, current_consecutive)
        else:
            current_consecutive = self.config.zero_value
    
    return max_consecutive

def _count_alternations_bct(self, sequence: List[str], category_a: str, category_b: str) -> int:
    """Compte les alternances entre deux catégories BCT"""
    alternations = self.config.zero_value
    
    for i in range(len(sequence) - self.config.one_value):
        current = sequence[i]
        next_item = sequence[i + self.config.one_value]
        
        if ((current == category_a and next_item == category_b) or 
            (current == category_b and next_item == category_a)):
            alternations += self.config.one_value
    
    return alternations

def _analyze_impair_5_isolation_bct(self, sequence: List[str]) -> float:
    """Analyse l'isolation des impair_5 (entourés de pair_4/pair_6)"""
    impair_5_positions = [i for i, cat in enumerate(sequence) if cat == 'impair_5']
    
    if not impair_5_positions:
        return self.config.zero_value
    
    isolation_score = self.config.zero_value
    for pos in impair_5_positions:
        # Vérifier si entouré de PAIR
        before_is_pair = (pos > self.config.zero_value and 
                         sequence[pos - self.config.one_value] in ['pair_4', 'pair_6'])
        after_is_pair = (pos < len(sequence) - self.config.one_value and 
                        sequence[pos + self.config.one_value] in ['pair_4', 'pair_6'])
        
        if before_is_pair and after_is_pair:
            isolation_score += self.config.one_value
    
    return isolation_score / len(impair_5_positions) if impair_5_positions else self.config.zero_value

def _detect_combined_rare_states_bct(self, cards_category_sequence: List[str]) -> bool:
    """Détecte la présence d'états combinés rares BCT"""
    # Cette méthode nécessiterait l'accès aux états combinés complets
    # Pour l'instant, approximation basée sur les catégories
    impair_5_count = cards_category_sequence.count('impair_5')
    total_hands = len(cards_category_sequence)
    
    if total_hands == self.config.zero_value:
        return False
    
    impair_5_ratio = impair_5_count / total_hands
    return impair_5_ratio > self.config.rollout1_combined_rare_threshold

# ================================================================
# MÉTHODES DE FUSION ET CALCUL CONFIANCE BCT
# ================================================================

def _fuse_rollout_analyses_bct(self, bct_categories_analysis: Dict, 
                              sync_desync_analysis: Dict,
                              combined_states_analysis: Dict,
                              temporal_analysis: Dict) -> Dict[str, Any]:
    """Fusionne toutes les analyses BCT pour la synthèse finale"""
    
    # Poids spécifiques BCT (centralisés dans config)
    weights = {
        'bct_categories': self.config.rollout1_impair5_bias_weight,      # Priorité aux catégories BCT
        'combined_states': self.config.rollout1_combined_rare_threshold,  # États combinés
        'sync_desync': self.config.rollout1_sync_high_threshold,         # SYNC/DESYNC
        'temporal': self.config.rollout1_phase_early_ratio               # Patterns temporels
    }
    
    # Normalisation des poids
    total_weight = sum(weights.values())
    normalized_weights = {k: v / total_weight for k, v in weights.items()}
    
    # Extraction des scores d'exploitation
    bct_exploitation = bct_categories_analysis.get('exploitation_potential', {}).get('overall_bct_bias_strength', self.config.zero_value)
    combined_exploitation = combined_states_analysis.get('exploitation_confidence', self.config.zero_value)
    sync_exploitation = sync_desync_analysis.get('sync_bias_strength', self.config.zero_value)
    temporal_exploitation = temporal_analysis.get('temporal_bias_strength', self.config.zero_value)
    
    # Fusion pondérée BCT
    fused_exploitation = (
        bct_exploitation * normalized_weights['bct_categories'] +
        combined_exploitation * normalized_weights['combined_states'] +
        sync_exploitation * normalized_weights['sync_desync'] +
        temporal_exploitation * normalized_weights['temporal']
    )
    
    return {
        'fused_exploitation_score': fused_exploitation,
        'strongest_bias_source': max(
            ('bct_categories', bct_exploitation),
            ('combined_states', combined_exploitation),
            ('sync_desync', sync_exploitation),
            ('temporal', temporal_exploitation),
            key=lambda x: x[1]
        )[0],
        'exploitation_confidence': min(fused_exploitation, self.config.rollout1_maximum_confidence_value),
        'bct_specific_metrics': {
            'impair_5_bias_strength': bct_categories_analysis.get('bias_detection', {}).get('impair_5_bias_detected', False),
            'pair_alternation_strength': bct_categories_analysis.get('bias_detection', {}).get('pair_alternation_bias_detected', False),
            'combined_rare_detected': bct_categories_analysis.get('bias_detection', {}).get('combined_rare_states_detected', False)
        }
    }

# ================================================================
# CONFIGURATION BCT CENTRALISÉE POUR MÉTHODES DE SUPPORT
# ================================================================
# Tous les paramètres utilisés dans ces méthodes doivent être
# définis dans AZRConfigBCT :
# - Seuils de détection de biais BCT
# - Poids des catégories BCT
# - Facteurs d'exploitation BCT
# - Limites et valeurs par défaut BCT
# ================================================================

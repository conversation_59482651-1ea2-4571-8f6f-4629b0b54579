# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10056 à 10108
# Type: Méthode de la classe AZRCluster

    def _calculate_phase_sync_desync_so_correlation(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict:
        """Calcule corrélations SYNC/DESYNC → S/O pour une phase"""

        if not desync_sync_seq or not so_seq:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        # Aligner séquences (S/O commence à manche 2)
        min_length = min(len(desync_sync_seq) - 1, len(so_seq))
        if min_length < 1:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        aligned_sync = desync_sync_seq[1:min_length + 1]
        aligned_so = so_seq[:min_length]

        # Calculer corrélations SYNC/DESYNC → S/O
        sync_positions = [i for i, val in enumerate(aligned_sync) if val == 'SYNC']
        desync_positions = [i for i, val in enumerate(aligned_sync) if val == 'DESYNC']

        sync_to_s = sum(1 for pos in sync_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        sync_to_o = sum(1 for pos in sync_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')
        desync_to_s = sum(1 for pos in desync_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        desync_to_o = sum(1 for pos in desync_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')

        # Force de corrélation S/O
        sync_total = sync_to_s + sync_to_o
        desync_total = desync_to_s + desync_to_o

        sync_strength = 0.0
        desync_strength = 0.0

        if sync_total > 0:
            sync_s_ratio = sync_to_s / sync_total
            sync_strength = abs(sync_s_ratio - 0.5)

        if desync_total > 0:
            desync_s_ratio = desync_to_s / desync_total
            desync_strength = abs(desync_s_ratio - 0.5)

        # Force globale pondérée
        total_sample = sync_total + desync_total
        if total_sample > 0:
            correlation_strength = (sync_strength * sync_total + desync_strength * desync_total) / total_sample
        else:
            correlation_strength = 0.0

        return {
            'correlation_strength': correlation_strength,
            'sync_strength': sync_strength,
            'desync_strength': desync_strength,
            'sample_size': total_sample,
            'sync_sample': sync_total,
            'desync_sample': desync_total
        }
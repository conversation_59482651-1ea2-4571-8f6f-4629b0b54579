MÉTHODE : _calculate_pair_commonality_score
LIGNE DÉBUT : 6078
SIGNATURE : def _calculate_pair_commonality_score(self, pair_consecutive: int) -> float:
================================================================================

    def _calculate_pair_commonality_score(self, pair_consecutive: int) -> float:
"""
    ADAPTATION BCT - _calculate_pair_commonality_score.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule score de commonalité pour séquences PAIR

        Basé sur la fréquence : 20,000 parties pures PAIR sur toutes possibilités
        """
        if pair_consecutive <= 3:
            return self.config.rollout1_pair_consecutive_very_common  # Très commun
        elif pair_consecutive <= 6:
            return self.config.rollout1_pair_consecutive_common  # Commun
        elif pair_consecutive <= 9:
            return self.config.rollout1_pair_consecutive_fairly_common  # Assez commun
        elif pair_consecutive <= 12:
            return self.config.rollout1_pair_consecutive_less_common  # Moins commun
        else:  # 13+
            return self.config.rollout1_coherence_threshold_excellent  # Rare même pour PAIR


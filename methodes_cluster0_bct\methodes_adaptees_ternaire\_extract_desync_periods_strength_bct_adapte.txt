MÉTHODE : _extract_desync_periods_strength
LIGNE DÉBUT : 8042
SIGNATURE : def _extract_desync_periods_strength(self, desync_impacts: Dict) -> float:
================================================================================

    def _extract_desync_periods_strength(self, desync_impacts: Dict) -> float:
"""
    ADAPTATION BCT - _extract_desync_periods_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Extrait la force des impacts de périodes de désynchronisation (focus P/B et S/O)"""

        if not desync_impacts:
            return 0.0

        strength_metrics = []

        # Analyser impacts des périodes de désynchronisation
        for period_key, period_data in desync_impacts.items():
            if isinstance(period_data, dict):

                # Force P/B (sans TIE)
                if 'pb_impact' in period_data:
                    pb_impact = period_data['pb_impact']
                    if 'pb_bias_strength' in pb_impact:
                        strength_metrics.append(pb_impact['pb_bias_strength'])

                # Force S/O
                if 'so_impact' in period_data:
                    so_impact = period_data['so_impact']
                    if 'so_bias_strength' in so_impact:
                        strength_metrics.append(so_impact['so_bias_strength'])

                # Force globale de la période
                if 'period_strength' in period_data:
                    strength_metrics.append(period_data['period_strength'])

        return sum(strength_metrics) / len(strength_metrics) if strength_metrics else 0.0


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 18964 à 19031
# Type: Méthode

def demo_training_and_testing():
    """Démonstration complète d'entraînement et de test du modèle AZR"""
    print("🎯 DÉMONSTRATION COMPLÈTE DU MODÈLE AZR INTÉGRÉ")
    print("=" * 60)

    # C<PERSON>er le prédicteur
    predictor = create_azr_predictor()

    print("\n🎓 Phase 1: Entraînement sur données générées")
    print("-" * 40)

    # Entraîner sur 200 parties générées
    training_metrics = predictor.train_on_generated_data(
        num_games=200,
        hands_per_game=60,
        save_data=True,
        filename="azr_demo_training_data.json"
    )

    print(f"\n📊 Résultats d'entraînement:")
    print(f"   Précision finale: {training_metrics['final_accuracy']:.3f}")
    print(f"   Amélioration: {training_metrics['improvement']:+.3f}")
    print(f"   Parties traitées: {training_metrics['games_processed']}")

    print("\n🧪 Phase 2: Test sur données fraîches")
    print("-" * 40)

    # Tester sur 100 nouvelles parties
    test_metrics = predictor.test_on_generated_data(
        num_games=100,
        hands_per_game=60
    )

    print(f"\n📊 Résultats de test:")
    print(f"   Précision de test: {test_metrics['test_accuracy']:.3f}")
    print(f"   Écart-type: {test_metrics['accuracy_std']:.3f}")
    print(f"   Précision min/max: {test_metrics['min_game_accuracy']:.3f}/{test_metrics['max_game_accuracy']:.3f}")

    print("\n🎮 Phase 3: Simulation d'utilisation en temps réel")
    print("-" * 40)

    # Réinitialiser pour simulation temps réel
    predictor.reset_session()

    # Simuler quelques manches
    test_hands = [
        {'pb_hand_number': 1, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': '--'},
        {'pb_hand_number': 2, 'result': 'BANKER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'O'},
        {'pb_hand_number': 3, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': 'O'},
        {'pb_hand_number': 4, 'result': 'PLAYER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'S'},
        {'pb_hand_number': 5, 'result': 'BANKER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': 'O'}
    ]

    print("\n📊 Prédictions en temps réel:")
    for hand in test_hands:
        prediction = predictor.receive_hand_data(hand)
        print(f"   Manche {hand['pb_hand_number']}: {hand['result']} {hand['parity']} {hand['sync_state']} → Prédiction: {prediction}")

    # Statistiques finales
    final_stats = predictor.get_statistics()
    print(f"\n📈 Statistiques du modèle:")
    print(f"   Précision actuelle: {final_stats['performance']['current_accuracy']:.3f}")
    print(f"   Total prédictions: {final_stats['performance']['total_predictions']}")
    print(f"   Séquence analysée: {final_stats['sequence_info']['sequence_length']} éléments")
    print(f"   État sync actuel: {final_stats['sequence_info']['current_sync_state']}")

    print(f"\n✅ Démonstration terminée - Modèle AZR prêt pour utilisation!")
    return predictor
MÉTHODE : calculate_rollout2_diversity_score
LIGNE DÉBUT : 4288
SIGNATURE : def calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float:
================================================================================

    def calculate_rollout2_diversity_score(self, sequences: List[Dict]) -> float:
"""
    ADAPTATION BCT - calculate_rollout2_diversity_score.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule le score de diversité des séquences générées

        Args:
            sequences: Liste des séquences générées

        Returns:
            float: Score de diversité (0-1)
        """
        if len(sequences) < self.config.two_value:
            return self.config.zero_value

        # Extraire les données de séquences
        sequence_data_list = []
        for sequence in sequences:
            if isinstance(sequence, dict):
                sequence_data = sequence.get('sequence_data', [])
                if sequence_data:
                    # Convertir en chaîne pour comparaison
                    sequence_str = ''.join(str(item) for item in sequence_data)
                    sequence_data_list.append(sequence_str)

        if len(sequence_data_list) < self.config.two_value:
            return self.config.zero_value

        # Calculer la diversité (pourcentage de séquences uniques)
        unique_sequences = len(set(sequence_data_list))
        total_sequences = len(sequence_data_list)

        diversity_score = unique_sequences / total_sequences

        return diversity_score

    # ========================================================================
    # 🏆 SYSTÈME DE RÉCOMPENSES ROLLOUT 3 - FORMULES TRR++
    # ========================================================================


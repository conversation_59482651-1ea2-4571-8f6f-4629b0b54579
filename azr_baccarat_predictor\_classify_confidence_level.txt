# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7164 à 7177
# Type: Méthode de la classe AZRCluster

    def _classify_confidence_level(self, signal_confidence: float) -> str:
        """
        Classifie le niveau de confiance d'un signal

        NOUVEAU : Utilise les paramètres centralisés pour classification
        """
        if signal_confidence > self.config.rollout2_signal_confidence_high:
            return self.config.rollout2_confidence_level_excellent
        elif signal_confidence > self.config.rollout2_signal_confidence_medium:
            return self.config.rollout2_confidence_level_good
        elif signal_confidence > self.config.rollout2_signal_confidence_low:
            return self.config.rollout2_confidence_level_acceptable
        else:
            return self.config.rollout2_confidence_level_poor
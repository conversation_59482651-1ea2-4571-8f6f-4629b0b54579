# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14175 à 14225
# Type: Méthode de la classe AZRBaccaratInterface

    def _create_columns_section(self, parent):
        """Crée les 3 colonnes principales (Player/Banker/Tie)"""
        columns_frame = ttk.Frame(parent)
        columns_frame.pack(expand=True, fill=tk.BOTH, pady=20)

        # Configuration des colonnes
        columns_frame.grid_columnconfigure(0, weight=1)
        columns_frame.grid_columnconfigure(1, weight=1)
        columns_frame.grid_columnconfigure(2, weight=1)

        # Colonne 1: PLAYER (Bleu foncé)
        player_frame = ttk.LabelFrame(columns_frame, text="PLAYER", padding="20")
        player_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        player_pair_btn = tk.<PERSON><PERSON>(player_frame, text="Player Pair", width=15, bg="#1E3A8A", fg="white",
                                   font=("Arial", 10, "bold"), relief="raised", bd=3,
                                   command=lambda: self.process_hand('PLAYER', 'PAIR'))
        player_pair_btn.pack(pady=10)

        player_impair_btn = tk.Button(player_frame, text="Player Impair", width=15, bg="#1E3A8A", fg="white",
                                     font=("Arial", 10, "bold"), relief="raised", bd=3,
                                     command=lambda: self.process_hand('PLAYER', 'IMPAIR'))
        player_impair_btn.pack(pady=10)

        # Colonne 2: BANKER (Rouge foncé)
        banker_frame = ttk.LabelFrame(columns_frame, text="BANKER", padding="20")
        banker_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        banker_pair_btn = tk.Button(banker_frame, text="Banker Pair", width=15, bg="#B91C1C", fg="white",
                                   font=("Arial", 10, "bold"), relief="raised", bd=3,
                                   command=lambda: self.process_hand('BANKER', 'PAIR'))
        banker_pair_btn.pack(pady=10)

        banker_impair_btn = tk.Button(banker_frame, text="Banker Impair", width=15, bg="#B91C1C", fg="white",
                                     font=("Arial", 10, "bold"), relief="raised", bd=3,
                                     command=lambda: self.process_hand('BANKER', 'IMPAIR'))
        banker_impair_btn.pack(pady=10)

        # Colonne 3: TIE (Vert foncé)
        tie_frame = ttk.LabelFrame(columns_frame, text="TIE", padding="20")
        tie_frame.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")

        tie_pair_btn = tk.Button(tie_frame, text="Tie Pair", width=15, bg="#166534", fg="white",
                                font=("Arial", 10, "bold"), relief="raised", bd=3,
                                command=lambda: self.process_hand('TIE', 'PAIR'))
        tie_pair_btn.pack(pady=10)

        tie_impair_btn = tk.Button(tie_frame, text="Tie Impair", width=15, bg="#166534", fg="white",
                                  font=("Arial", 10, "bold"), relief="raised", bd=3,
                                  command=lambda: self.process_hand('TIE', 'IMPAIR'))
        tie_impair_btn.pack(pady=10)
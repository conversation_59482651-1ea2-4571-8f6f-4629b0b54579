🧠 AZR BACCARAT PREDICTOR - REFONTE COMPLÈTE
==================================================
Initialisation du système...
📊 Configuration système:
  • Clusters: 8
  • Rollouts par cluster: 3
  • Total rollouts: 24
  • Cœurs CPU: 8
  • Mémoire max: 28GB
  • Mémoire par cluster: 3584MB

🎮 Lancement de l'interface graphique...
2025-06-08 02:19:32,747 - __main__.Cluster0 - INFO - Cluster 0 initialisé: Reference
2025-06-08 02:19:32,747 - __main__.Cluster1 - INFO - Cluster 1 initialisé: Reference_Backup
2025-06-08 02:19:32,747 - __main__.Cluster2 - INFO - Cluster 2 initialisé: Short_Patterns
2025-06-08 02:19:32,747 - __main__.Cluster3 - INFO - Cluster 3 initialisé: Medium_Patterns
2025-06-08 02:19:32,747 - __main__.Cluster4 - INFO - Cluster 4 initialisé: Long_Patterns
2025-06-08 02:19:32,747 - __main__.Cluster5 - INFO - Cluster 5 initialisé: Correlations
2025-06-08 02:19:32,747 - __main__.Cluster6 - INFO - Cluster 6 initialisé: Sync_Desync
2025-06-08 02:19:32,748 - __main__.Cluster7 - INFO - Cluster 7 initialisé: Adaptive
2025-06-08 02:19:32,748 - __main__.ClusterManager - INFO - ClusterManager initialisé: 8 clusters, 8 threads
2025-06-08 02:19:33,412 - __main__.Interface - INFO - Interface graphique initialisée
2025-06-08 02:19:33,412 - __main__.Interface - INFO - Démarrage de l'interface graphique
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 88: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1748, in <lambda>
    command=lambda c=count: self._initialize_burn(c, 'PAIR'))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1897, in _initialize_burn
    self.logger.info(f"Brûlage initialisé: {count} cartes {parity} → {initial_sync_state}")
Message: 'Brûlage initialisé: 8 cartes PAIR → SYNC'
Arguments: ()
2025-06-08 02:19:56,055 - __main__.Interface - INFO - Brûlage initialisé: 8 cartes PAIR → SYNC
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 82: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: BANKER pair_4 → pair_4_sync --'
Arguments: ()
2025-06-08 02:20:01,136 - __main__.Interface - INFO - Main traitée: BANKER pair_4 → pair_4_sync --
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 82: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: PLAYER pair_4 → pair_4_sync O'
Arguments: ()
2025-06-08 02:20:03,712 - __main__.Interface - INFO - Main traitée: PLAYER pair_4 → pair_4_sync O
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 82: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: PLAYER pair_4 → pair_4_sync S'
Arguments: ()
2025-06-08 02:20:09,985 - __main__.Interface - INFO - Main traitée: PLAYER pair_4 → pair_4_sync S
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 82: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: PLAYER pair_6 → pair_6_sync S'
Arguments: ()
2025-06-08 02:20:11,315 - __main__.Interface - INFO - Main traitée: PLAYER pair_6 → pair_6_sync S
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 84: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: PLAYER impair_5 → impair_5_desync S'
Arguments: ()
2025-06-08 02:20:13,040 - __main__.Interface - INFO - Main traitée: PLAYER impair_5 → impair_5_desync S
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 84: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: BANKER impair_5 → impair_5_sync O'
Arguments: ()
2025-06-08 02:20:14,116 - __main__.Interface - INFO - Main traitée: BANKER impair_5 → impair_5_sync O
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 82: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: BANKER pair_6 → pair_6_sync S'
Arguments: ()
2025-06-08 02:20:14,929 - __main__.Interface - INFO - Main traitée: BANKER pair_6 → pair_6_sync S
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: TIE pair_6 → pair_6_sync --'
Arguments: ()
2025-06-08 02:20:16,117 - __main__.Interface - INFO - Main traitée: TIE pair_6 → pair_6_sync --
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: TIE pair_4 → pair_4_sync --'
Arguments: ()
2025-06-08 02:20:16,797 - __main__.Interface - INFO - Main traitée: TIE pair_4 → pair_4_sync --
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2192' in position 81: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2215, in <module>
    main()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2204, in main
    interface.run()
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 2169, in run
    self.root.mainloop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 1599, in mainloop
    self.tk.mainloop(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2068, in __call__
    return self.func(*args)
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1839, in <lambda>
    command=lambda r=result, p=parity_code: self._process_hand(r, p))
  File "C:\Users\<USER>\Desktop\A\azr_baccarat_refonte.py", line 1940, in _process_hand
    self.logger.info(f"Main traitée: {result} {parity_code} → {hand.combined_state} {hand.so_conversion}")
Message: 'Main traitée: TIE impair_5 → impair_5_desync --'
Arguments: ()
2025-06-08 02:20:17,649 - __main__.Interface - INFO - Main traitée: TIE impair_5 → impair_5_desync --
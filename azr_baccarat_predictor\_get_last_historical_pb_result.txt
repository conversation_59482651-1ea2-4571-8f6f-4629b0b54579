# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6610 à 6636
# Type: Méthode de la classe AZRCluster

    def _get_last_historical_pb_result(self, analyzer_report: Dict) -> str:
        """
        Récupère le dernier résultat P/B de l'historique pour conversion S/O

        Returns:
            'P' ou 'B' ou None si pas d'historique
        """
        # Essayer d'obtenir depuis l'analyse PBT
        indices_analysis = analyzer_report.get('indices_analysis', {})
        pbt_analysis = indices_analysis.get('pbt', {})
        pbt_sequence = pbt_analysis.get('pbt_sequence', [])

        if pbt_sequence:
            # Trouver le dernier résultat P/B (ignorer les Ties)
            for result in reversed(pbt_sequence):
                if result in ['P', 'B']:
                    return result

        # Fallback : essayer depuis quick_access
        quick_access = analyzer_report.get('quick_access', {})
        last_hand = quick_access.get('last_hand_analysis', {})
        last_result = last_hand.get('result')

        if last_result in ['P', 'B']:
            return last_result

        return None  # Pas de référence trouvée
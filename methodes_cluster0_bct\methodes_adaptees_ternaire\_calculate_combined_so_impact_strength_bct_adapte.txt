MÉTHODE : _calculate_combined_so_impact_strength
LIGNE DÉBUT : 6179
SIGNATURE : def _calculate_combined_so_impact_strength(self, impact_analysis: Dict) -> float:
================================================================================

    def _calculate_combined_so_impact_strength(self, impact_analysis: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_combined_so_impact_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule la force globale d'impact des états combinés sur S/O

        IMPORTANT: Analyse seulement S/O, pas les TIE
        Les prédictions ciblent S/O pour les stratégies de mise

        Args:
            impact_analysis: Dictionnaire avec analyse d'impact pour chaque état combiné

        Returns:
            Score de force d'impact S/O (0.0 à 1.0)
        """

        if not impact_analysis or len(impact_analysis) == 0:
            return 0.0

        total_so_strength = 0.0
        valid_so_states = 0
        total_so_sample = 0

        for state, data in impact_analysis.items():
            # Vérifier données S/O disponibles (TIE exclus)
            if 'to_s_ratio' in data and 'to_o_ratio' in data and 'total_occurrences' in data:

                # Force S/O = écart par rapport à 50/50 S/O
                s_ratio = data['to_s_ratio']
                o_ratio = data['to_o_ratio']

                # Vérification cohérence S/O (doit sommer à 1.0)
                if abs((s_ratio + o_ratio) - self.config.one_value) > self.config.coherence_threshold_strict:
                    continue  # Ignorer données incohérentes

                # Déviation S/O par rapport à l'équilibre 50/50
                so_deviation = abs(s_ratio - 0.5)

                # Pondération par échantillon S/O
                so_sample_size = data['total_occurrences']
                so_sample_weight = min(so_sample_size / 10.0, 1.0)  # Poids max à 10+ S/O

                # Force S/O pondérée
                so_state_strength = so_deviation * so_sample_weight

                total_so_strength += so_state_strength
                total_so_sample += so_sample_size
                valid_so_states += 1

        if valid_so_states == 0:
            return 0.0

        # Force S/O moyenne
        average_so_strength = total_so_strength / valid_so_states

        # Bonus diversité états S/O (4 états = max fiabilité)
        so_diversity_bonus = min(valid_so_states / 4.0, 1.0)

        # Bonus échantillon S/O global
        so_sample_bonus = min(total_so_sample / 20.0, 1.0)  # 20+ conversions S/O

        # Score S/O final
        final_so_strength = average_so_strength * so_diversity_bonus * so_sample_bonus

        return min(final_so_strength, 1.0)


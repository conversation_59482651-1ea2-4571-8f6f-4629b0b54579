MÉTHODE : _find_consecutive_sequences
LIGNE DÉBUT : 5996
SIGNATURE : def _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]:
================================================================================

    def _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]:
"""
    ADAPTATION BCT - _find_consecutive_sequences.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Trouve toutes les séquences consécutives d'un pattern"""
        consecutive_lengths = []
        current_length = 0

        for item in sequence:
            if item == pattern:
                current_length += 1
            else:
                if current_length > 0:
                    consecutive_lengths.append(current_length)
                    current_length = 0

        # Ajouter la dernière séquence si elle se termine par le pattern
        if current_length > 0:
            consecutive_lengths.append(current_length)

        return consecutive_lengths





    # ========================================================================
    # MÉTHODES D'ANALYSE ASYMÉTRIQUE IMPAIR/PAIR
    # ========================================================================


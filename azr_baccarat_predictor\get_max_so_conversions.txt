# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 12807 à 12820
# Type: Méthode de la classe AZRCluster

    def get_max_so_conversions(self, mode: str = "real") -> int:
        """
        Retourne la limite de conversions S/O selon le mode

        Args:
            mode: "real" pour mode réel (59 S/O), "training" pour entraînement (cut card - 1)

        Returns:
            Limite maximale de conversions S/O pour la partie
        """
        if mode == "training":
            return self.config.max_sequence_length_training - 1  # Cut card - 1
        else:  # mode == "real"
            return self.config.max_so_conversions_real           # 59 S/O max
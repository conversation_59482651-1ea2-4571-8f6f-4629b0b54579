# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17369 à 17375
# Type: Méthode de la classe AZRBaccaratPredictor

    def _estimate_memory_per_game(self) -> float:
        """Estime l'utilisation mémoire par partie (en MB)"""
        # Estimation basée sur structure de données
        base_memory_per_hand = self.config.rollout_base_memory_per_hand  # KB par manche
        average_hands_per_game = self.config.rollout_average_hands_per_game
        memory_per_game_kb = base_memory_per_hand * average_hands_per_game
        return memory_per_game_kb / self.config.rollout_kb_to_mb_conversion  # Conversion en MB
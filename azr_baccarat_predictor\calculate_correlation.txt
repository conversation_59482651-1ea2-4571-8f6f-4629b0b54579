# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 2188 à 2216
# Type: Méthode de la classe UtilitairesMathematiquesAZR

    def calculate_correlation(seq1: List, seq2: List) -> float:
        """
        Calcule la corrélation entre deux séquences

        Args:
            seq1: Première séquence
            seq2: Deuxième séquence

        Returns:
            float: Coefficient de corrélation (-1 à 1)
        """
        if len(seq1) != len(seq2) or len(seq1) == 0:
            return self.config.zero_value

        try:
            # Conversion en arrays numpy pour calcul optimisé
            # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
            config = AZRConfig()  # Instance temporaire pour accès aux paramètres
            arr1 = np.array([config.correlation_player_value if x == 'P'
                           else config.correlation_banker_value if x == 'B'
                           else config.correlation_tie_value for x in seq1])
            arr2 = np.array([config.correlation_impair_value if x == 'IMPAIR'
                           else config.correlation_pair_value for x in seq2])

            correlation = np.corrcoef(arr1, arr2)[0, 1]
            return correlation if not np.isnan(correlation) else config.default_return_value
        except:
            config = AZRConfig()
            return config.default_return_value
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 7224 à 7295
# Type: Méthode de la classe AZRCluster

    def _generate_all_possible_sequences(self, generation_space: Dict) -> List[Dict]:
        """
        Génère toutes les 8 possibilités de séquences P/B de longueur 3
        et les convertit en séquences S/O pour éviter les doublons

        CORRIGÉ : 3 P/B + dernier historique = 3 S/O, 4 séquences uniques

        Returns:
            List[Dict]: 4 séquences S/O uniques avec métadonnées
        """
        import itertools

        # Obtenir le dernier résultat P/B historique
        analyzer_report = {
            'indices_analysis': generation_space.get('indices_analysis', {}),
            'quick_access': generation_space
        }
        last_pb = self._get_last_historical_pb_result(analyzer_report)

        if not last_pb:
            last_pb = 'P'  # Fallback

        # G<PERSON><PERSON>rer toutes les 8 possibilités : 2^3 = 8
        all_pb_possibilities = []
        all_so_possibilities = []

        for sequence_tuple in itertools.product(['P', 'B'], repeat=self.config.rollout2_fixed_length):
            sequence = list(sequence_tuple)

            # Convertir en séquence S/O avec le dernier historique
            so_sequence = self._convert_pb_sequence_to_so_with_history(sequence, last_pb)
            so_pattern = ''.join(so_sequence)

            # Calculer la probabilité de cette séquence
            probability = self._calculate_sequence_probability(sequence, generation_space)

            # Calculer des métriques de qualité
            quality_metrics = self._calculate_sequence_quality_metrics(sequence, generation_space)

            pb_data = {
                'sequence_data_pb': sequence,
                'sequence_data_so': so_sequence,
                'estimated_probability': probability,
                'quality_metrics': quality_metrics,
                'pb_pattern': ''.join(sequence),
                'so_pattern': so_pattern,
                'strategy': f"pattern_{sequence_tuple}",
                'justification': f"Séquence P/B {sequence} → S/O {so_sequence} - Probabilité: {probability:.3f}",
                'last_historical_pb': last_pb
            }

            all_pb_possibilities.append(pb_data)

            # Vérifier si cette séquence S/O est déjà présente
            if so_pattern not in [item['so_pattern'] for item in all_so_possibilities]:
                all_so_possibilities.append(pb_data)

        # Trier par probabilité décroissante
        all_so_possibilities.sort(key=lambda x: x['estimated_probability'], reverse=True)

        # Sélectionner les 4 meilleures séquences S/O uniques
        top_4_sequences = all_so_possibilities[:self.config.rollout2_sequences_count]

        # Ajouter des métadonnées de classement
        for i, sequence_data in enumerate(top_4_sequences):
            sequence_data['rank'] = i + 1
            sequence_data['percentile'] = ((self.config.rollout2_sequences_count - i) / self.config.rollout2_sequences_count) * 100
            sequence_data['confidence_level'] = self._classify_confidence_level(sequence_data['estimated_probability'])
            # Le rollout 3 travaille avec les séquences S/O
            sequence_data['sequence_data'] = sequence_data['sequence_data_so']

        return top_4_sequences
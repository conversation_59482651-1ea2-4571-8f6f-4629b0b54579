# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 16036 à 16092
# Type: Méthode de la classe AZRBaccaratPredictor

    def _analyze_combined_states_to_so(self, combined_states: List[str], so_conversions: List[str]) -> float:
        """
        🔬 MODE EXPLORATION AVEUGLE - DÉCOUVERTES MASQUÉES

        Les rollouts explorent sans connaître les patterns découverts pour tester
        leur capacité à redécouvrir indépendamment les règles révolutionnaires.
        """
        # 🔬 EXPLORATION AVEUGLE : Les rollouts ne connaissent pas les découvertes
        if not self.config.use_combined_index_discovery:
            # Analyse basique sans utiliser les découvertes révolutionnaires
            if len(combined_states) < self.config.two_value:
                return self.config.probability_neutral

            # Les rollouts doivent découvrir les patterns par eux-mêmes
            # Analyse simple basée sur la fréquence récente
            recent_states = combined_states[-self.config.five_value:] if len(combined_states) >= self.config.five_value else combined_states
            recent_so = [so for so in so_conversions[-len(recent_states):] if so in ['S', 'O']]

            if recent_so:
                s_ratio = recent_so.count('S') / len(recent_so)
                # Légère influence basée sur observation récente (sans connaître les règles)
                return self.config.probability_neutral + (s_ratio - self.config.probability_neutral) * self.config.small_increment
            else:
                return self.config.probability_neutral

        # 🔬 CODE ORIGINAL MASQUÉ PENDANT EXPLORATION AVEUGLE
        # Les rollouts ne doivent PAS connaître les découvertes révolutionnaires !
        # Ils doivent les redécouvrir par eux-mêmes pour valider leur capacité d'apprentissage

        if len(combined_states) < self.config.two_value:
            return self.config.probability_neutral

        # État combiné actuel (dernier état)
        current_combined_state = combined_states[-self.config.one_value]

        # 🧠 DÉCOUVERTE AUTONOME PURE - INFLUENCES FIXES ÉLIMINÉES
        # Les rollouts découvrent maintenant les patterns par eux-mêmes
        # sans contamination par des analyses de parties spécifiques

        if self.config.disable_fixed_influences:
            # Mode découverte autonome : retourner probabilité neutre
            # Les rollouts vont découvrir les vrais patterns par apprentissage
            return self.config.probability_neutral

        # 🔬 ANCIEN SYSTÈME (DÉSACTIVÉ) - Influences fixes contaminantes
        # Ces règles étaient basées sur parties purement paires/impaires
        # et contaminaient l'apprentissage des rollouts
        if current_combined_state == 'PAIR_SYNC':
            return self.config.probability_neutral - self.config.rollout1_combined_pair_sync_influence
        elif current_combined_state == 'IMPAIR_SYNC':
            return self.config.probability_neutral + self.config.rollout1_combined_impair_sync_influence
        elif current_combined_state == 'PAIR_DESYNC':
            return self.config.probability_neutral - self.config.rollout1_combined_pair_desync_influence
        elif current_combined_state == 'IMPAIR_DESYNC':
            return self.config.probability_neutral - self.config.rollout1_combined_impair_desync_influence
        else:
            return self.config.probability_neutral
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4200 à 4249
# Type: Méthode de la classe AZRCluster

    def _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict:
        """Corrèle les biais structurels avec les variations P/B"""
        correlation = {
            'pb_correlation_strength': 0.0,
            'impair_pb_influence': 0.0,
            'sync_pb_influence': 0.0,
            'combined_pb_influence': 0.0,
            'dominant_pb_pattern': 'P'
        }

        if not hands_data:
            return correlation

        # Analyser les variations P/B
        pb_sequence = [hand.pbt_result for hand in hands_data if hasattr(hand, 'pbt_result') and hand.pbt_result in ['P', 'B']]

        if len(pb_sequence) < 2:
            return correlation

        # Calculer l'influence des biais IMPAIR sur P/B
        impair_strength = impair_bias.get('exploitation_confidence', 0.0)
        correlation['impair_pb_influence'] = impair_strength * self.config.confidence_multiplier_02

        # Calculer l'influence des biais SYNC sur P/B
        sync_strength = sync_bias.get('exploitation_confidence', 0.0)
        correlation['sync_pb_influence'] = sync_strength * self.config.confidence_multiplier_03

        # Calculer l'influence des biais COMBINÉS sur P/B
        combined_strength = combined_bias.get('exploitation_confidence', 0.0)
        correlation['combined_pb_influence'] = combined_strength * self.config.confidence_multiplier_04

        # Force de corrélation globale
        total_influence = (
            correlation['impair_pb_influence'] +
            correlation['sync_pb_influence'] +
            correlation['combined_pb_influence']
        ) / 3

        correlation['pb_correlation_strength'] = min(self.config.one_value, total_influence)

        # Pattern dominant P/B
        p_count = pb_sequence.count('P')
        b_count = pb_sequence.count('B')

        if p_count > b_count:
            correlation['dominant_pb_pattern'] = 'P'
        else:
            correlation['dominant_pb_pattern'] = 'B'

        return correlation
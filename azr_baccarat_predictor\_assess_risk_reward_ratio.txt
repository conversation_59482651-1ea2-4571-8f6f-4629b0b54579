# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6077 à 6121
# Type: Méthode de la classe AZRCluster

    def _assess_risk_reward_ratio(self, sequence: Dict, analyzer_report: Dict) -> float:
        """
        Évalue le ratio risque/récompense d'une séquence

        FOCUS : Équilibrer le potentiel de gain avec le risque d'erreur
        """
        estimated_probability = sequence.get('estimated_probability', 0.5)

        # Récupérer la qualité d'analyse du Rollout 1
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', 0.5)

        # Récupérer les zones de haute confiance
        high_confidence_zones = synthesis.get('high_confidence_zones', [])

        # 1. Score de récompense basé sur la probabilité et la qualité d'analyse
        reward_score = estimated_probability * analysis_quality

        # 2. Score de risque basé sur l'incertitude
        uncertainty = 1.0 - analysis_quality
        risk_score = uncertainty * (1.0 - estimated_probability)

        # 3. Bonus pour les séquences alignées avec les zones de haute confiance
        confidence_bonus = 0.0
        sequence_strategy = sequence.get('strategy', '').lower()

        for zone in high_confidence_zones:
            zone_pattern = zone.get('pattern', '').lower()
            zone_confidence = zone.get('confidence', 0.0)

            # Vérifier si la séquence exploite une zone de haute confiance
            if any(keyword in zone_pattern for keyword in ['impair', 'pair', 'sync', 'same', 'opposite']):
                if any(keyword in sequence_strategy for keyword in ['impair', 'pair', 'sync', 'so']):
                    confidence_bonus += zone_confidence * self.config.rollout2_consistency_weight

        # 4. Calcul du ratio risque/récompense
        total_reward = reward_score + confidence_bonus
        total_risk = max(0.1, risk_score)  # Éviter division par zéro

        risk_reward_ratio = total_reward / total_risk

        # Normaliser entre 0 et 1
        normalized_ratio = min(1.0, risk_reward_ratio / 3.0)  # Ratio de 3.0 = score parfait

        return normalized_ratio
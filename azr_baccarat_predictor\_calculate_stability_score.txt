# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15132 à 15143
# Type: Méthode de la classe AZRBaccaratPredictor

    def _calculate_stability_score(self) -> float:
        """Calcule un score de stabilité basé sur la variance de précision"""
        if len(self.accuracy_history) < 10:
            return 0.0

        recent_accuracy = self.accuracy_history[-20:]  # 20 dernières mesures
        variance = np.var(recent_accuracy)

        # Score de stabilité inversement proportionnel à la variance
        # Plus la variance est faible, plus le score est élevé
        stability_score = max(0.0, 1.0 - (variance * 10))  # Normalisation
        return min(1.0, stability_score)
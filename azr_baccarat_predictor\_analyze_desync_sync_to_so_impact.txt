# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 8142 à 8168
# Type: Méthode de la classe AZRCluster

    def _analyze_desync_sync_to_so_impact(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict:
        """Analyse impact DESYNC/SYNC → S/O (NOUVEAU)"""
        if len(so_seq) == 0:
            return {'no_data': True}

        # Alignement des séquences (S/O commence à la manche 2)
        aligned_desync_sync = desync_sync_seq[1:len(so_seq)+1] if len(desync_sync_seq) > len(so_seq) else desync_sync_seq[:len(so_seq)]

        # Calcul corrélations SYNC/DESYNC → S/O
        sync_s_count = sum(1 for ds, so in zip(aligned_desync_sync, so_seq) if ds == 'SYNC' and so == 'S')
        sync_o_count = sum(1 for ds, so in zip(aligned_desync_sync, so_seq) if ds == 'SYNC' and so == 'O')
        desync_s_count = sum(1 for ds, so in zip(aligned_desync_sync, so_seq) if ds == 'DESYNC' and so == 'S')
        desync_o_count = sum(1 for ds, so in zip(aligned_desync_sync, so_seq) if ds == 'DESYNC' and so == 'O')

        total_sync = sync_s_count + sync_o_count
        total_desync = desync_s_count + desync_o_count

        return {
            'sync_to_s_ratio': sync_s_count / max(1, total_sync),
            'sync_to_o_ratio': sync_o_count / max(1, total_sync),
            'desync_to_s_ratio': desync_s_count / max(1, total_desync),
            'desync_to_o_ratio': desync_o_count / max(1, total_desync),
            'dominant_pattern': self._identify_dominant_desync_sync_so_pattern(
                sync_s_count, sync_o_count, desync_s_count, desync_o_count
            ),
            'impact_strength': abs((sync_s_count / max(1, total_sync)) - (desync_s_count / max(1, total_desync)))
        }
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4251 à 4291
# Type: Méthode de la classe AZRCluster

    def _correlate_bias_to_so_variations(self, pb_correlation: Dict, hands_data: List) -> Dict:
        """Corrèle les biais avec les variations S/O"""
        correlation = {
            'so_correlation_strength': 0.0,
            'pb_so_influence': 0.0,
            'predicted_so_tendency': 'S',
            'so_confidence': 0.0
        }

        if not hands_data:
            return correlation

        # Analyser les conversions S/O
        so_sequence = [getattr(hand, 'so_conversion', None) for hand in hands_data]
        so_sequence = [so for so in so_sequence if so in ['S', 'O']]

        if len(so_sequence) < 2:
            return correlation

        # Influence des corrélations P/B sur S/O
        pb_strength = pb_correlation.get('pb_correlation_strength', 0.0)
        correlation['pb_so_influence'] = pb_strength * self.config.confidence_multiplier_05

        # Force de corrélation S/O
        correlation['so_correlation_strength'] = min(self.config.one_value, correlation['pb_so_influence'])

        # Tendance S/O prédite
        s_count = so_sequence.count('S')
        o_count = so_sequence.count('O')

        if s_count > o_count:
            correlation['predicted_so_tendency'] = 'S'
        else:
            correlation['predicted_so_tendency'] = 'O'

        # Confiance S/O
        if len(so_sequence) > 0:
            dominant_ratio = max(s_count, o_count) / len(so_sequence)
            correlation['so_confidence'] = abs(dominant_ratio - self.config.rollout_analyzer_normality_threshold)

        return correlation
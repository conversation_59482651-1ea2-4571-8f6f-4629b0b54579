MÉTHODE : _generate_so_pattern_sequence
LIGNE DÉBUT : 9787
SIGNATURE : def _generate_so_pattern_sequence(self, generation_space: Dict) -> List[Dict]:
================================================================================

    def _generate_so_pattern_sequence(self, generation_space: Dict) -> List[Dict]:
"""
    ADAPTATION BCT - _generate_so_pattern_sequence.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Génère séquence basée sur exploitation patterns S/O (Same/Opposite)

        IMPORTANT: Focus prioritaire sur S/O (index 5), P/B dérivé
        Exploite les patterns de répétition vs changement pour optimiser les prédictions S/O

        Args:
            generation_space: Espace de génération complet avec contraintes et guidance

        Returns:
            Liste de mains prédites avec stratégie S/O optimisée
        """

        sequence_length = self.config.rollout2_fixed_length  # Longueur fixe selon spécifications AZR
        sequence = []

        # ================================================================
        # 1. EXTRACTION DES ZONES DE CONFIANCE S/O
        # ================================================================

        confidence_zones = generation_space.get('confidence_zones', {})
        cross_impact_guidance = generation_space.get('cross_impact_guidance', {})

        # Zone de confiance S/O prioritaire
        best_so_zone = confidence_zones.get('best_so_zone', {})

        # Guidance impacts croisés vers S/O
        impair_pair_so_guidance = cross_impact_guidance.get('impair_pair_so', {})
        sync_desync_so_guidance = cross_impact_guidance.get('sync_desync_so', {})
        combined_so_guidance = cross_impact_guidance.get('combined_so', {})

        # ================================================================
        # 2. DÉTERMINATION STRATÉGIE S/O DOMINANTE
        # ================================================================

        # Analyser exploitabilité des zones S/O
        so_zone_exploitability = best_so_zone.get('exploitability', 'MEDIUM')
        so_zone_confidence = best_so_zone.get('confidence', 0.5)
        so_zone_pattern = best_so_zone.get('pattern', '')

        # Analyser priorités des guidance S/O
        impair_pair_so_priority = impair_pair_so_guidance.get('exploitation_priority', 'MEDIUM')
        sync_desync_so_priority = sync_desync_so_guidance.get('exploitation_priority', 'MEDIUM')
        combined_so_priority = combined_so_guidance.get('exploitation_priority', 'MEDIUM')

        # Déterminer stratégie S/O dominante
        if so_zone_exploitability == 'VERY_HIGH':
            so_strategy = 'ZONE_EXPLOITATION'
            base_so_confidence = so_zone_confidence
        elif combined_so_priority in ['VERY_HIGH', 'HIGH']:
            so_strategy = 'COMBINED_SO_FOCUS'
            base_so_confidence = combined_so_guidance.get('strength', 0.7)
        elif impair_pair_so_priority in ['VERY_HIGH', 'HIGH']:
            so_strategy = 'IMPAIR_PAIR_SO_FOCUS'
            base_so_confidence = impair_pair_so_guidance.get('strength', 0.7)
        elif sync_desync_so_priority in ['VERY_HIGH', 'HIGH']:
            so_strategy = 'SYNC_DESYNC_SO_FOCUS'
            base_so_confidence = sync_desync_so_guidance.get('strength', 0.7)
        else:
            so_strategy = 'BALANCED_SO'
            base_so_confidence = self.config.rollout2_confidence_value_standard

        # ================================================================
        # 3. GÉNÉRATION SÉQUENCE OPTIMISÉE S/O
        # ================================================================

        # Pattern attendu de base (alternance P/B)
        expected_pattern = ['P', 'B']

        # Historique S/O simulé pour logique de continuité
        simulated_so_history = []

        for i in range(sequence_length):
            hand_number = i + 1
            position_type = 'impair_5' if hand_number % 2 == 1 else ['pair_4', 'pair_6']

            # ================================================================
            # 3.1. PRÉDICTION S/O PRIORITAIRE
            # ================================================================

            predicted_so = 'S'  # Défaut
            so_confidence = base_so_confidence

            if so_strategy == 'ZONE_EXPLOITATION':
                # Exploiter zone de confiance S/O
                if '→S' in so_zone_pattern:
                    predicted_so = 'S'
                elif '→O' in so_zone_pattern:
                    predicted_so = 'O'

                # Ajuster selon position
                if position_type == 'impair_5' and 'IMPAIR→S' in so_zone_pattern:
                    predicted_so = 'S'
                elif position_type == ['pair_4', 'pair_6'] and 'PAIR→O' in so_zone_pattern:
                    predicted_so = 'O'

                so_confidence = so_zone_confidence

            elif so_strategy == 'COMBINED_SO_FOCUS':
                # Exploiter guidance combinée → S/O
                combined_pattern = combined_so_guidance.get('pattern', '')

                if 'IMpair_4_sync, pair_6_sync→S' in combined_pattern and position_type == 'impair_5':
                    predicted_so = 'S'
                elif 'pair_4_desync, pair_6_desync→O' in combined_pattern and position_type == ['pair_4', 'pair_6']:
                    predicted_so = 'O'
                elif 'IMpair_4_desync, pair_6_desync→O' in combined_pattern and position_type == 'impair_5':
                    predicted_so = 'O'
                elif 'pair_4_sync, pair_6_sync→S' in combined_pattern and position_type == ['pair_4', 'pair_6']:
                    predicted_so = 'S'
                else:
                    # Logique par défaut
                    predicted_so = 'S' if i % 2 == 0 else 'O'

                so_confidence = combined_so_guidance.get('strength', 0.7)

            elif so_strategy == 'IMPAIR_PAIR_SO_FOCUS':
                # Exploiter guidance IMPAIR/PAIR → S/O
                impair_pair_pattern = impair_pair_so_guidance.get('pattern', '')

                if position_type == 'impair_5':
                    if 'IMPAIR→S' in impair_pair_pattern:
                        predicted_so = 'S'
                    elif 'IMPAIR→O' in impair_pair_pattern:
                        predicted_so = 'O'
                elif position_type == ['pair_4', 'pair_6']:
                    if 'PAIR→S' in impair_pair_pattern:
                        predicted_so = 'S'
                    elif 'PAIR→O' in impair_pair_pattern:
                        predicted_so = 'O'

                so_confidence = impair_pair_so_guidance.get('strength', 0.7)

            elif so_strategy == 'SYNC_DESYNC_SO_FOCUS':
                # Exploiter guidance SYNC/DESYNC → S/O
                sync_desync_pattern = sync_desync_so_guidance.get('pattern', '')

                # Déterminer état sync probable
                expected_outcome = expected_pattern[i % len(expected_pattern)]
                probable_sync = 'SYNC' if i % 3 != 0 else 'DESYNC'  # Majorité SYNC

                if probable_sync == 'SYNC':
                    if 'SYNC→S' in sync_desync_pattern:
                        predicted_so = 'S'
                    elif 'SYNC→O' in sync_desync_pattern:
                        predicted_so = 'O'
                elif probable_sync == 'DESYNC':
                    if 'DESYNC→S' in sync_desync_pattern:
                        predicted_so = 'S'
                    elif 'DESYNC→O' in sync_desync_pattern:
                        predicted_so = 'O'

                so_confidence = sync_desync_so_guidance.get('strength', 0.7)

            else:  # BALANCED_SO
                # Stratégie équilibrée S/O
                if i < sequence_length // 2:
                    # Première moitié : favoriser S (continuité)
                    predicted_so = 'S'
                    so_confidence = self.config.rollout2_confidence_value_medium_high
                else:
                    # Seconde moitié : alternance S/O
                    predicted_so = 'S' if i % 2 == 0 else 'O'
                    so_confidence = self.config.rollout2_confidence_value_standard

            # ================================================================
            # 3.2. DÉRIVATION P/B BASÉE SUR S/O
            # ================================================================

            # Logique de dérivation P/B depuis S/O
            expected_outcome = expected_pattern[i % len(expected_pattern)]

            if i == 0:
                # Première main : suivre pattern attendu
                predicted_pb = expected_outcome
                sync_state = 'SYNC'
            else:
                # Mains suivantes : dériver depuis S/O
                previous_pb = sequence[i-1]['predicted_pbt'] if sequence else expected_pattern[0]

                if predicted_so == 'S':
                    # Same : répéter résultat précédent
                    predicted_pb = previous_pb
                    sync_state = 'SYNC' if predicted_pb == expected_outcome else 'DESYNC'
                else:
                    # Opposite : changer résultat
                    predicted_pb = 'B' if previous_pb == 'P' else 'P'
                    sync_state = 'SYNC' if predicted_pb == expected_outcome else 'DESYNC'

            # ================================================================
            # 3.3. CALCUL CONFIANCES
            # ================================================================

            # Confiance P/B dérivée de S/O (moins prioritaire)
            pb_confidence = so_confidence * self.config.rollout2_base_confidence_high  # P/B dérivé = 80% confiance S/O

            # Confiance sync basée sur cohérence P/B
            sync_confidence = pb_confidence if sync_state == 'SYNC' else (1.0 - pb_confidence)

            # Ajustements selon évolution temporelle
            temporal_hints = generation_space.get('temporal_evolution_hints', {})
            if 'strong_evolution' in temporal_hints:
                evolution_strength = temporal_hints['strong_evolution']
                so_confidence = min(so_confidence + evolution_strength * self.config.rollout2_adjustment_large, 0.95)
                pb_confidence = min(pb_confidence + evolution_strength * self.config.rollout2_adjustment_small, 0.9)

            # ================================================================
            # 3.4. CONSTRUCTION MAIN PRÉDITE
            # ================================================================

            # Confiance globale pondérée (S/O poids 4, P/B poids 1 car dérivé)
            global_confidence = (pb_confidence * 1 + so_confidence * 4) / 5

            hand = {
                'hand_number': hand_number,
                'position_type': position_type,
                'predicted_pbt': predicted_pb,
                'predicted_so': predicted_so,
                'sync_state': sync_state,
                'pb_confidence': pb_confidence,
                'so_confidence': so_confidence,
                'sync_confidence': sync_confidence,
                'global_confidence': global_confidence,
                'strategy_source': 'so_pattern',
                'so_strategy': so_strategy,
                'expected_outcome': expected_outcome,
                'pb_derivation': 'derived_from_so',
                'guidance_used': {
                    'best_so_zone': bool(best_so_zone),
                    'impair_pair_so': bool(impair_pair_so_guidance),
                    'sync_desync_so': bool(sync_desync_so_guidance),
                    'combined_so': bool(combined_so_guidance)
                },
                'priorities': {
                    'so_zone_exploitability': so_zone_exploitability,
                    'impair_pair_so_priority': impair_pair_so_priority,
                    'sync_desync_so_priority': sync_desync_so_priority,
                    'combined_so_priority': combined_so_priority
                }
            }

            sequence.append(hand)
            simulated_so_history.append(predicted_so)

        return sequence


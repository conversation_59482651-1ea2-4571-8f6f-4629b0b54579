# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15441 à 15487
# Type: Méthode de la classe AZRBaccaratPredictor

    def _analyze_sync_patterns(self) -> Optional[Dict[str, Any]]:
        """Analyse les patterns de synchronisation SYNC/DESYNC"""
        if len(self.hands_history) < 3:
            return None

        # Analyse des transitions de synchronisation
        sync_transitions = []
        for i in range(1, len(self.hands_history)):
            prev_sync = self.hands_history[i-1].sync_state
            curr_sync = self.hands_history[i].sync_state
            so_result = self.hands_history[i].so_conversion

            if so_result in ['S', 'O']:
                sync_transitions.append({
                    'from': prev_sync,
                    'to': curr_sync,
                    'result': so_result
                })

        if not sync_transitions:
            return None

        # Calcul des probabilités conditionnelles
        current_sync = self.current_sync_state
        same_prob = 0.0
        total_count = 0

        for transition in sync_transitions:
            if transition['from'] == current_sync:
                total_count += self.config.cluster_count_increment
                if transition['result'] == 'S':
                    same_prob += self.config.cluster_same_increment

        if total_count == 0:
            return None

        same_prob /= total_count
        confidence = abs(same_prob - self.config.cluster_probability_neutral) * self.config.cluster_confidence_multiplier  # Distance de 50%

        prediction = 'S' if same_prob > self.config.cluster_probability_neutral else 'O'

        return {
            'prediction': prediction,
            'confidence': confidence,
            'probability': same_prob,
            'sample_size': total_count
        }
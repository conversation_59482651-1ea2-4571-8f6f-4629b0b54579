# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 8778 à 8832
# Type: Méthode de la classe AZRCluster

    def _identify_dominant_impair_pair_so_pattern(self, impair_s_count: int, impair_o_count: int,
                                                pair_s_count: int, pair_o_count: int) -> str:
        """
        Identifie le pattern dominant IMPAIR/PAIR → S/O

        IMPORTANT: Analyse seulement S/O, pas les TIE
        Focus sur les patterns IMPAIR/PAIR les plus prédictifs pour S/O

        Args:
            impair_s_count: Nombre de IMPAIR → S
            impair_o_count: Nombre de IMPAIR → O
            pair_s_count: Nombre de PAIR → S
            pair_o_count: Nombre de PAIR → O

        Returns:
            Pattern dominant sous forme de string
        """

        # Calcul des ratios pour S/O uniquement
        total_impair_so = impair_s_count + impair_o_count    # Seulement S et O pour IMPAIR
        total_pair_so = pair_s_count + pair_o_count          # Seulement S et O pour PAIR

        if total_impair_so == 0 and total_pair_so == 0:
            return 'NO_IMPAIR_PAIR_SO_DATA'

        # Ratios IMPAIR → S/O (TIE exclus)
        impair_s_ratio = impair_s_count / max(1, total_impair_so) if total_impair_so > 0 else 0
        impair_o_ratio = impair_o_count / max(1, total_impair_so) if total_impair_so > 0 else 0

        # Ratios PAIR → S/O (TIE exclus)
        pair_s_ratio = pair_s_count / max(1, total_pair_so) if total_pair_so > 0 else 0
        pair_o_ratio = pair_o_count / max(1, total_pair_so) if total_pair_so > 0 else 0

        # Patterns IMPAIR/PAIR → S/O seulement
        impair_pair_so_patterns = [
            ('IMPAIR_TO_S', abs(impair_s_ratio - 0.5), impair_s_ratio, total_impair_so),
            ('IMPAIR_TO_O', abs(impair_o_ratio - 0.5), impair_o_ratio, total_impair_so),
            ('PAIR_TO_S', abs(pair_s_ratio - 0.5), pair_s_ratio, total_pair_so),
            ('PAIR_TO_O', abs(pair_o_ratio - 0.5), pair_o_ratio, total_pair_so)
        ]

        # Filtrer échantillons S/O suffisants (minimum 3 conversions S/O)
        valid_impair_pair_patterns = [(name, strength, ratio, sample) for name, strength, ratio, sample in impair_pair_so_patterns if sample >= 3]

        if not valid_impair_pair_patterns:
            return 'INSUFFICIENT_IMPAIR_PAIR_SO_DATA'

        # Pattern IMPAIR/PAIR → S/O le plus fort
        dominant_impair_pair_so = max(valid_impair_pair_patterns, key=lambda x: x[1])

        # Seuil S/O significatif (60% vers S ou O)
        if dominant_impair_pair_so[2] < 0.6:
            return 'NO_DOMINANT_IMPAIR_PAIR_SO_PATTERN'

        return dominant_impair_pair_so[0]
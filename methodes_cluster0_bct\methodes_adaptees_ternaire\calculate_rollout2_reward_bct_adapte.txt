MÉTHODE : calculate_rollout2_reward
LIGNE DÉBUT : 4178
SIGNATURE : def calculate_rollout2_reward(self, sequence_quality: float, diversity_score: float, difficulty_factor: float) -> Dict:
================================================================================

    def calculate_rollout2_reward(self, sequence_quality: float, diversity_score: float, difficulty_factor: float) -> Dict:
"""
    ADAPTATION BCT - calculate_rollout2_reward.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule la récompense du Rollout 2 selon les formules AZR officielles

        Basé sur :
        - Zone de développement proximal (Vygotsky + AZR)
        - Récompense de learnability
        - Facteur de diversité
        - Bonus/malus selon qualité

        Args:
            sequence_quality: Qualité des séquences générées (0-1)
            diversity_score: Score de diversité des séquences (0-1)
            difficulty_factor: Facteur de difficulté de la tâche (0-1)

        Returns:
            Dict: Récompense détaillée avec composants
        """
        # 1. SCORE DE BASE (Zone de développement proximal)
        optimal_difficulty = self.config.rollout2_rewards['optimal_difficulty']
        min_difficulty = self.config.rollout2_rewards['min_difficulty']
        max_difficulty = self.config.rollout2_rewards['max_difficulty']

        if difficulty_factor <= min_difficulty or difficulty_factor >= max_difficulty:
            learnability_base = self.config.zero_value  # Trop facile ou impossible
        else:
            # Formule AZR : distance de l'optimal
            distance_from_optimal = abs(difficulty_factor - optimal_difficulty)
            learnability_base = max(self.config.zero_value, self.config.one_value - (distance_from_optimal / self.config.weight_40_percent))

        # 2. FACTEUR DE DIVERSITÉ (Seuil AZR = 0.5)
        diversity_threshold = self.config.rollout2_rewards['diversity_threshold']

        if diversity_score > diversity_threshold:
            diversity_multiplier = self.config.rollout2_rewards['diversity_bonus']  # 1.0
        else:
            diversity_multiplier = self.config.rollout2_rewards['diversity_malus']  # 0.5

        # 3. BONUS/MALUS SELON QUALITÉ
        excellence_threshold = self.config.rollout2_rewards['excellence_threshold']
        acceptable_threshold = self.config.rollout2_rewards['acceptable_threshold']
        weak_threshold = self.config.rollout2_rewards['weak_threshold']

        if sequence_quality >= excellence_threshold:
            quality_bonus = self.config.rollout2_rewards['excellence_bonus']  # 1.15 (+15%)
        elif sequence_quality >= acceptable_threshold:
            quality_bonus = self.config.rollout2_rewards['neutral_multiplier']  # 1.0 (neutre)
        elif sequence_quality >= weak_threshold:
            quality_bonus = self.config.rollout2_rewards['weak_malus']  # 0.85 (-15%)
        else:
            quality_bonus = self.config.rollout2_rewards['poor_malus']  # 0.7 (-30%)

        # 4. RÉCOMPENSE FINALE
        final_reward = learnability_base * diversity_multiplier * quality_bonus

        return {
            'reward': final_reward,
            'components': {
                'learnability_base': learnability_base,
                'diversity_multiplier': diversity_multiplier,
                'quality_bonus': quality_bonus
            },
            'thresholds_used': {
                'difficulty_factor': difficulty_factor,
                'diversity_score': diversity_score,
                'sequence_quality': sequence_quality
            }
        }


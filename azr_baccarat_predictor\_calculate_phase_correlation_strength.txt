# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10110 à 10134
# Type: Méthode de la classe AZRCluster

    def _calculate_phase_correlation_strength(self, impair_pb_corr: Dict, impair_so_corr: Dict,
                                            sync_pb_corr: Dict, sync_so_corr: Dict) -> float:
        """Calcule la force globale des corrélations pour une phase"""

        # Pondération : S/O plus important que P/B (plus exploitable)
        pb_weight = 2.0
        so_weight = 3.0

        # Récupérer forces de corrélation
        impair_pb_strength = impair_pb_corr.get('correlation_strength', 0.0)
        impair_so_strength = impair_so_corr.get('correlation_strength', 0.0)
        sync_pb_strength = sync_pb_corr.get('correlation_strength', 0.0)
        sync_so_strength = sync_so_corr.get('correlation_strength', 0.0)

        # Calculer force pondérée
        total_strength = (
            impair_pb_strength * pb_weight +
            impair_so_strength * so_weight +
            sync_pb_strength * pb_weight +
            sync_so_strength * so_weight
        )

        total_weight = 2 * pb_weight + 2 * so_weight  # 4 corrélations

        return total_strength / total_weight if total_weight > 0 else 0.0
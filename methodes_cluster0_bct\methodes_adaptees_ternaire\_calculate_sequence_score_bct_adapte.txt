MÉTHODE : _calculate_sequence_score
LIGNE DÉBUT : 3720
SIGNATURE : def _calculate_sequence_score(self, evaluation: Dict) -> float:
================================================================================

    def _calculate_sequence_score(self, evaluation: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_sequence_score.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule le score total pondéré d'une séquence

        FOCUS : Pondération optimale des différents critères d'évaluation
        """
        # Pondérations optimisées pour AZR Baccarat
        weights = {
            'signal_alignment_score': 0.35,    # Le plus important : alignement avec Rollout 1
            'consistency_score': self.config.rollout2_correlation_weight,         # Cohérence interne
            'risk_reward_ratio': 0.25,         # Équilibre risque/récompense
            'logic_validation_score': 0.15     # Validation logique
        }

        total_score = 0.0

        for criterion, weight in weights.items():
            score = evaluation.get(criterion, 0.0)
            total_score += score * weight

        # Bonus pour les séquences avec facteur de confiance élevé
        confidence_factor = evaluation.get('confidence_factor', self.config.default_confidence)
        if confidence_factor > self.config.confidence_high_threshold:
            total_score *= (self.config.one_value + self.config.bonus_high_confidence)  # Bonus de 10%
        elif confidence_factor < self.config.confidence_low_threshold:
            total_score *= (self.config.one_value - self.config.malus_low_confidence)  # Malus de 10%

        return min(1.0, total_score)


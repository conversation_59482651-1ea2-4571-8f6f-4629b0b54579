# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10228 à 10243
# Type: Méthode de la classe AZRCluster

    def _calculate_temporal_consistency(self, phase_strengths: Dict) -> float:
        """Calcule la consistance temporelle des corrélations"""

        values = list(phase_strengths.values())
        if len(values) < 2:
            return 0.0

        # Consistance = 1 - coefficient de variation
        mean_strength = sum(values) / len(values)
        if mean_strength == 0:
            return 0.0

        std_dev = (self._calculate_variance(values)) ** 0.5
        cv = std_dev / mean_strength

        return max(0.0, 1.0 - cv)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 SCRIPT D'ADAPTATION AUTOMATIQUE BINAIRE → TERNAIRE BCT
================================================================================
Objectif: Transformer automatiquement les 140 méthodes cluster 0 
         du système binaire PAIR/IMPAIR vers le système ternaire BCT

Date: 08/06/2025 23:00
Auteur: Assistant Augment
"""

import re
import os
from typing import Dict, List, Tuple

class AdaptateurTernaireBCT:
    """
    Adaptateur automatique pour transformer les méthodes AZR
    du système binaire vers le système ternaire BCT
    """
    
    def __init__(self):
        # Mapping fondamental binaire → ternaire
        self.mapping_rules = {
            # Références directes
            "'PAIR'": "['pair_4', 'pair_6']",
            "'IMPAIR'": "'impair_5'",
            '"PAIR"': '["pair_4", "pair_6"]',
            '"IMPAIR"': '"impair_5"',
            
            # Variables et conditions
            'position_type == "PAIR"': 'position_type in ["pair_4", "pair_6"]',
            'position_type == "IMPAIR"': 'position_type == "impair_5"',
            "position_type == 'PAIR'": "position_type in ['pair_4', 'pair_6']",
            "position_type == 'IMPAIR'": "position_type == 'impair_5'",
            
            # États combinés
            'PAIR_SYNC': 'pair_4_sync, pair_6_sync',
            'PAIR_DESYNC': 'pair_4_desync, pair_6_desync',
            'IMPAIR_SYNC': 'impair_5_sync',
            'IMPAIR_DESYNC': 'impair_5_desync',
            
            # Compteurs et séquences
            'pair_count': 'pair_4_count + pair_6_count',
            'impair_count': 'impair_5_count',
            
            # Méthodes spécialisées
            '_analyze_pair_': '_analyze_pair4_pair6_',
            '_analyze_impair_': '_analyze_impair5_',
            '_correlate_pair_': '_correlate_pair4_pair6_',
            '_correlate_impair_': '_correlate_impair5_'
        }
        
        # Patterns de remplacement complexes
        self.complex_patterns = [
            # Calcul position_type binaire → ternaire
            (
                r"position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'",
                """total_cards = get_total_cards_distributed(hand_data)
if total_cards == 4: position_type = 'pair_4'
elif total_cards == 5: position_type = 'impair_5'  
elif total_cards == 6: position_type = 'pair_6'"""
            ),
            
            # Conditions binaires → ternaires
            (
                r"if.*position_type.*==.*['\"]PAIR['\"]",
                "if position_type in ['pair_4', 'pair_6']"
            ),
            (
                r"if.*position_type.*==.*['\"]IMPAIR['\"]",
                "if position_type == 'impair_5'"
            ),
            
            # Seuils asymétriques (impair_5 30x plus rare)
            (
                r"impair_alert_threshold_medium.*=.*3",
                "impair5_alert_threshold_medium = 2  # Plus sensible (30x plus rare)"
            ),
            (
                r"pair_alert_threshold_medium.*=.*5",
                "pair46_alert_threshold_medium = 6  # Moins sensible"
            )
        ]
        
        # Adaptations spécifiques BCT
        self.bct_adaptations = {
            'card_count_categories': {
                'pair_4': 4,    # Aucune 3ème carte
                'pair_6': 6,    # Deux 3èmes cartes  
                'impair_5': 5   # Une 3ème carte
            },
            'combined_states': [
                'pair_4_sync', 'pair_4_desync',
                'pair_6_sync', 'pair_6_desync',
                'impair_5_sync', 'impair_5_desync'
            ],
            'asymmetric_weights': {
                'impair_5_weight': 30.0,  # 30x plus significatif
                'pair_4_weight': 1.0,     # Poids normal
                'pair_6_weight': 1.0      # Poids normal
            }
        }
    
    def adapter_methode(self, code_source: str, nom_methode: str) -> str:
        """
        Adapte une méthode du système binaire vers le système ternaire BCT
        
        Args:
            code_source: Code source de la méthode
            nom_methode: Nom de la méthode pour logging
            
        Returns:
            Code adapté au système ternaire BCT
        """
        code_adapte = code_source
        
        print(f"🔧 Adaptation de {nom_methode}...")
        
        # 1. Remplacements simples
        for ancien, nouveau in self.mapping_rules.items():
            if ancien in code_adapte:
                code_adapte = code_adapte.replace(ancien, nouveau)
                print(f"  ✅ Remplacé: {ancien} → {nouveau}")
        
        # 2. Patterns complexes avec regex
        for pattern, remplacement in self.complex_patterns:
            if re.search(pattern, code_adapte):
                code_adapte = re.sub(pattern, remplacement, code_adapte)
                print(f"  ✅ Pattern adapté: {pattern[:30]}...")
        
        # 3. Adaptations spécifiques BCT
        code_adapte = self._adapter_logique_bct(code_adapte, nom_methode)
        
        # 4. Ajout documentation adaptation
        code_adapte = self._ajouter_documentation_adaptation(code_adapte, nom_methode)
        
        print(f"✅ {nom_methode} adaptée au système ternaire BCT")
        return code_adapte
    
    def _adapter_logique_bct(self, code: str, nom_methode: str) -> str:
        """Adaptations spécifiques à la logique BCT"""
        
        # Adaptation des boucles sur catégories
        if 'for category in' in code and 'PAIR' in code:
            code = code.replace(
                "for category in ['PAIR', 'IMPAIR']:",
                "for category in ['pair_4', 'impair_5', 'pair_6']:"
            )
        
        # Adaptation des corrélations
        if '_correlate_' in nom_methode:
            # Séparer logique PAIR en pair_4 et pair_6
            if 'PAIR' in code:
                code = self._separer_logique_pair(code)
        
        # Adaptation asymétrique pour méthodes d'analyse
        if '_analyze_' in nom_methode and 'impair' in nom_methode.lower():
            code = self._adapter_asymetrie_impair5(code)
        
        return code
    
    def _separer_logique_pair(self, code: str) -> str:
        """Sépare la logique PAIR en pair_4 et pair_6"""
        
        # Template de séparation
        separation_template = """
        # Logique séparée pour pair_4 et pair_6 (ancien PAIR)
        for pair_category in ['pair_4', 'pair_6']:
            # Traitement spécifique à chaque catégorie pair
            if pair_category == 'pair_4':
                # Aucune 3ème carte - logique spécifique
                pass
            elif pair_category == 'pair_6':
                # Deux 3èmes cartes - logique spécifique  
                pass
        """
        
        # Remplacer les boucles PAIR génériques
        if 'if category == "PAIR":' in code:
            code = code.replace(
                'if category == "PAIR":',
                'if category in ["pair_4", "pair_6"]:'
            )
        
        return code
    
    def _adapter_asymetrie_impair5(self, code: str) -> str:
        """Adapte la logique asymétrique pour impair_5 (30x plus rare)"""
        
        # Seuils asymétriques
        adaptations_asymetriques = {
            'threshold = 3': 'threshold = 2  # impair_5 plus sensible',
            'threshold = 5': 'threshold = 3  # impair_5 plus sensible',
            'alert_level = 1': 'alert_level = 2  # impair_5 plus critique',
            'weight = 1.0': 'weight = 30.0  # impair_5 30x plus significatif'
        }
        
        for ancien, nouveau in adaptations_asymetriques.items():
            if ancien in code:
                code = code.replace(ancien, nouveau)
        
        return code
    
    def _ajouter_documentation_adaptation(self, code: str, nom_methode: str) -> str:
        """Ajoute documentation de l'adaptation BCT"""
        
        doc_adaptation = f'''"""
    ADAPTATION BCT - {nom_methode}
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """'''
        
        # Insérer après la signature de fonction
        if 'def ' in code:
            lines = code.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith('def '):
                    # Trouver la fin de la signature
                    j = i
                    while j < len(lines) and not lines[j].strip().endswith(':'):
                        j += 1
                    # Insérer documentation après la signature
                    lines.insert(j + 1, doc_adaptation)
                    break
            code = '\n'.join(lines)
        
        return code
    
    def adapter_fichier_methodes(self, chemin_source: str, chemin_destination: str):
        """
        Adapte un fichier complet de méthodes
        
        Args:
            chemin_source: Fichier source avec méthodes binaires
            chemin_destination: Fichier destination avec méthodes ternaires BCT
        """
        print(f"📁 Adaptation fichier: {chemin_source}")
        
        try:
            with open(chemin_source, 'r', encoding='utf-8') as f:
                contenu_source = f.read()
            
            # Adapter le contenu complet
            contenu_adapte = self.adapter_methode(contenu_source, os.path.basename(chemin_source))
            
            # Sauvegarder le fichier adapté
            with open(chemin_destination, 'w', encoding='utf-8') as f:
                f.write(contenu_adapte)
            
            print(f"✅ Fichier adapté sauvegardé: {chemin_destination}")
            
        except Exception as e:
            print(f"❌ Erreur adaptation {chemin_source}: {e}")
    
    def adapter_dossier_complet(self, dossier_source: str, dossier_destination: str):
        """
        Adapte tous les fichiers d'un dossier
        
        Args:
            dossier_source: Dossier avec méthodes binaires
            dossier_destination: Dossier avec méthodes ternaires BCT
        """
        print(f"📂 Adaptation dossier complet: {dossier_source}")
        
        # Créer dossier destination si nécessaire
        os.makedirs(dossier_destination, exist_ok=True)
        
        # Adapter tous les fichiers .txt
        for fichier in os.listdir(dossier_source):
            if fichier.endswith('.txt'):
                chemin_source = os.path.join(dossier_source, fichier)
                nom_adapte = fichier.replace('.txt', '_bct_adapte.txt')
                chemin_destination = os.path.join(dossier_destination, nom_adapte)
                
                self.adapter_fichier_methodes(chemin_source, chemin_destination)
        
        print(f"✅ Adaptation dossier terminée: {dossier_destination}")

# 🚀 UTILISATION DU SCRIPT
if __name__ == "__main__":
    adaptateur = AdaptateurTernaireBCT()
    
    # Adapter le dossier des méthodes extraites
    adaptateur.adapter_dossier_complet(
        "baseder/methodes_extraites/",
        "methodes_cluster0_bct/methodes_adaptees_ternaire/"
    )
    
    print("🎉 Adaptation automatique terminée !")
    print("📁 Méthodes adaptées disponibles dans: methodes_cluster0_bct/methodes_adaptees_ternaire/")

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10693 à 10701
# Type: Méthode de la classe AZRCluster

    def _calculate_statistical_significance(self, individual_strengths: Dict, variations_impact: Dict) -> float:
        """Calcule la significativité statistique globale"""

        # Significativité basée sur force et échantillons
        strength_component = sum(individual_strengths.values()) / len(individual_strengths) if individual_strengths else 0
        sample_component = self._assess_sample_size_adequacy(variations_impact)

        # Moyenne pondérée (force 60%, échantillons 40%)
        return strength_component * self.config.rollout2_confidence_value_standard + sample_component * self.config.rollout3_quality_bonus_medium
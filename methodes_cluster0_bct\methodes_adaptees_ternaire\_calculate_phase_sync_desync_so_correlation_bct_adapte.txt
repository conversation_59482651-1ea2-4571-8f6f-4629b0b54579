MÉTHODE : _calculate_phase_sync_desync_so_correlation
LIGNE DÉBUT : 7592
SIGNATURE : def _calculate_phase_sync_desync_so_correlation(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict:
================================================================================

    def _calculate_phase_sync_desync_so_correlation(self, desync_sync_seq: List[str], so_seq: List[str]) -> Dict:
"""
    ADAPTATION BCT - _calculate_phase_sync_desync_so_correlation.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule corrélations SYNC/DESYNC → S/O pour une phase"""

        if not desync_sync_seq or not so_seq:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        # Aligner séquences (S/O commence à manche 2)
        min_length = min(len(desync_sync_seq) - 1, len(so_seq))
        if min_length < 1:
            return {'correlation_strength': 0.0, 'sample_size': 0}

        aligned_sync = desync_sync_seq[1:min_length + 1]
        aligned_so = so_seq[:min_length]

        # Calculer corrélations SYNC/DESYNC → S/O
        sync_positions = [i for i, val in enumerate(aligned_sync) if val == 'SYNC']
        desync_positions = [i for i, val in enumerate(aligned_sync) if val == 'DESYNC']

        sync_to_s = sum(1 for pos in sync_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        sync_to_o = sum(1 for pos in sync_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')
        desync_to_s = sum(1 for pos in desync_positions if pos < len(aligned_so) and aligned_so[pos] == 'S')
        desync_to_o = sum(1 for pos in desync_positions if pos < len(aligned_so) and aligned_so[pos] == 'O')

        # Force de corrélation S/O
        sync_total = sync_to_s + sync_to_o
        desync_total = desync_to_s + desync_to_o

        sync_strength = 0.0
        desync_strength = 0.0

        if sync_total > 0:
            sync_s_ratio = sync_to_s / sync_total
            sync_strength = abs(sync_s_ratio - 0.5)

        if desync_total > 0:
            desync_s_ratio = desync_to_s / desync_total
            desync_strength = abs(desync_s_ratio - 0.5)

        # Force globale pondérée
        total_sample = sync_total + desync_total
        if total_sample > 0:
            correlation_strength = (sync_strength * sync_total + desync_strength * desync_total) / total_sample
        else:
            correlation_strength = 0.0

        return {
            'correlation_strength': correlation_strength,
            'sync_strength': sync_strength,
            'desync_strength': desync_strength,
            'sample_size': total_sample,
            'sync_sample': sync_total,
            'desync_sample': desync_total
        }


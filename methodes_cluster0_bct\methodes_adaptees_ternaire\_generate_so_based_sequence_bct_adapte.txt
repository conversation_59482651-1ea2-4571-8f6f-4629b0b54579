MÉTHODE : _generate_so_based_sequence
LIGNE DÉBUT : 4719
SIGNATURE : def _generate_so_based_sequence(self, target_outcome: str, sequence_length: int, generation_space: Dict) -> List[str]:
================================================================================

    def _generate_so_based_sequence(self, target_outcome: str, sequence_length: int, generation_space: Dict) -> List[str]:
"""
    ADAPTATION BCT - _generate_so_based_sequence.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Génère une séquence basée sur prédiction S/O (Same/Opposite)

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            target_outcome: 'S' pour Same, 'O' pour Opposite
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Récupérer le dernier résultat P/B du contexte
        last_pb_result = None
        pbt_sequence = generation_space.get('indices_analysis', {}).get('pbt', {}).get('pbt_sequence', [])

        if pbt_sequence:
            # Trouver le dernier résultat P/B (ignorer les Ties)
            for result in reversed(pbt_sequence):
                if result in ['P', 'B']:
                    last_pb_result = result
                    break

        if not last_pb_result:
            last_pb_result = 'P'  # Valeur par défaut

        # Générer la séquence selon la prédiction S/O (longueur fixe 4)
        for i in range(self.config.rollout2_fixed_length):
            if target_outcome == 'S':
                # Same : répéter le même résultat
                next_result = last_pb_result
            else:  # target_outcome == 'O'
                # Opposite : alterner
                next_result = 'B' if last_pb_result == 'P' else 'P'

            sequence.append(next_result)
            last_pb_result = next_result  # Mise à jour pour la prochaine itération

        return sequence


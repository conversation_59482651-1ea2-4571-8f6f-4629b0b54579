MÉTHODE : _correlate_bias_to_pb_variations
LIGNE DÉBUT : 2374
SIGNATURE : def _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict:
================================================================================

    def _correlate_bias_to_pb_variations(self, impair_bias: Dict, sync_bias: Dict, combined_bias: Dict, hands_data: List) -> Dict:
"""
    ADAPTATION BCT - _correlate_bias_to_pb_variations.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Corrèle les biais structurels avec les variations P/B

        LOGIQUE ANTI-MOYENNES :
        - Mesure l'impact des biais sur les variations P/B
        - Utilise les écart-types pour quantifier l'influence
        - Génère des signaux prédictifs pour P/B
        """
        pb_correlation = {
            'impair_to_pb_correlation': 0.0,
            'sync_to_pb_correlation': 0.0,
            'combined_to_pb_correlation': 0.0,
            'strongest_pb_signal': {},
            'pb_prediction_confidence': 0.0
        }

        # Extraction P/B seulement
        pb_outcomes = []
        for hand in hands_data:
            if hand.pbt_result in ['P', 'B']:
                pb_outcomes.append(hand.pbt_result)

        if len(pb_outcomes) < 3:
            return pb_correlation

        # Corrélation impair → P/B
        impair_pb_impact = impair_bias.get('pb_impact_after_impairs', {})
        if impair_pb_impact:
            deviation = impair_pb_impact.get('deviation_strength', 0.0)
            samples = impair_pb_impact.get('total_samples', 0)
            if samples > 2:
                pb_correlation['impair_to_pb_correlation'] = deviation * min(1.0, samples / 5.0)

        # Corrélation sync → P/B
        sync_pb_impact = sync_bias.get('pb_impact_after_sync_breaks', {})
        if sync_pb_impact:
            deviation = sync_pb_impact.get('deviation_strength', 0.0)
            samples = sync_pb_impact.get('total_samples', 0)
            if samples > 2:
                pb_correlation['sync_to_pb_correlation'] = deviation * min(1.0, samples / 5.0)

        # Corrélation combinée
        combined_strength = combined_bias.get('reinforced_signal_strength', 0.0)
        multiplier = combined_bias.get('exploitation_multiplier', 1.0)
        pb_correlation['combined_to_pb_correlation'] = combined_strength * multiplier

        # Signal P/B le plus fort
        correlations = {
            'impair': pb_correlation['impair_to_pb_correlation'],
            'sync': pb_correlation['sync_to_pb_correlation'],
            'combined': pb_correlation['combined_to_pb_correlation']
        }

        strongest_type = max(correlations, key=correlations.get)
        strongest_value = correlations[strongest_type]

        pb_correlation['strongest_pb_signal'] = {
            'signal_type': strongest_type,
            'signal_strength': strongest_value,
            'exploitation_ready': strongest_value > 0.3
        }

        # Confiance prédiction P/B
        pb_correlation['pb_prediction_confidence'] = min(1.0, strongest_value)

        return pb_correlation


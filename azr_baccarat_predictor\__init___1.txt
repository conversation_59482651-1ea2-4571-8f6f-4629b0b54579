# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 13441 à 13484
# Type: Méthode de la classe AZRMaster
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config: AZRConfig):
        self.config = config
        self.clusters = []
        self.num_clusters = self.config.cpu_cores  # 1 par cœur CPU

        # Initialisation du système d'optimisation
        try:
            from cluster_optimization_strategies import ClusterOptimizationStrategies
            self.optimizer = ClusterOptimizationStrategies()
            optimization_enabled = True
        except ImportError:
            logger.warning("Module d'optimisation non trouvé - utilisation configuration standard")
            self.optimizer = None
            optimization_enabled = False

        # Initialisation des clusters SPÉCIALISÉS
        for cluster_id in range(self.num_clusters):
            if optimization_enabled and self.optimizer:
                # Configuration spécialisée pour ce cluster
                specialized_config = self.optimizer.get_cluster_config(cluster_id, config)
                cluster = AZRCluster(cluster_id, specialized_config, predictor_instance=self)
                logger.info(f"Cluster {cluster_id} initialisé avec spécialisation : {specialized_config.cluster_specialization.get('name', 'STANDARD')}")
            else:
                # Configuration standard
                cluster = AZRCluster(cluster_id, config, predictor_instance=self)
                logger.info(f"Cluster {cluster_id} initialisé avec configuration standard")

            self.clusters.append(cluster)

        # Communication inter-clusters (message passing)
        self.global_shared_memory = {
            'cluster_results': [],
            'consensus_prediction': None,
            'global_confidence': 0.0,
            'timing_metrics': {}
        }

        # Métriques système global
        self.system_metrics = {
            'total_predictions': 0,
            'consensus_achieved': 0,
            'average_cluster_agreement': 0.0,
            'system_efficiency': 0.0
        }
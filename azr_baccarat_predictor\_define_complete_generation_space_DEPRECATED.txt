# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 11344 à 11629
# Type: Méthode de la classe AZRCluster

    def _define_complete_generation_space_DEPRECATED(self, indices_analysis: Dict, synthesis: Dict, sequence_metadata: Dict) -> Dict:
        """
        Définit l'espace de génération COMPLET basé sur l'analyse complète des 5 indices

        IMPORTANT: Focus sur P/B et S/O, exclusion des TIE
        Version avancée qui utilise toute l'intelligence de l'analyse complète

        Args:
            indices_analysis: Analyse complète des 5 indices
            synthesis: Synthèse enrichie avec impacts croisés
            sequence_metadata: Métadonnées de la séquence

        Returns:
            Dictionnaire avec espace de génération complet
        """

        complete_generation_space = {
            'sequence_length': self.config.rollout2_fixed_length,
            'forbidden_patterns': [],
            'preferred_correlations': [],
            'transition_opportunities': [],
            'enhanced_constraints': {},
            'cross_impact_guidance': {},
            'temporal_evolution_hints': {},
            'confidence_zones': {}
        }

        # ================================================================
        # 1. ANALYSE DES CONTRAINTES BASÉES SUR INDICES 1-3
        # ================================================================

        # Contraintes IMPAIR/PAIR
        if 'impair_pair' in indices_analysis:
            impair_pair_data = indices_analysis['impair_pair']

            # Séquences consécutives critiques
            consecutive_patterns = impair_pair_data.get('consecutive_patterns', {})
            impair_consecutive = consecutive_patterns.get('max_impair_consecutive', 0)
            pair_consecutive = consecutive_patterns.get('max_pair_consecutive', 0)

            # Contraintes IMPAIR (très sensibles)
            if impair_consecutive >= 3:  # 3+ IMPAIR = ultra-rare
                complete_generation_space['forbidden_patterns'].append('IMPAIR_extension')
                complete_generation_space['enhanced_constraints']['force_pair_bias'] = 0.8
            elif impair_consecutive >= 2:  # 2+ IMPAIR = rare
                complete_generation_space['enhanced_constraints']['impair_caution'] = 0.6

            # Contraintes PAIR (moins sensibles)
            if pair_consecutive >= 12:  # 12+ PAIR = rare même pour PAIR
                complete_generation_space['forbidden_patterns'].append('PAIR_excessive_extension')
                complete_generation_space['enhanced_constraints']['force_impair_break'] = 0.3

            # Corrélations IMPAIR/PAIR → P/B (sans TIE)
            correlations = impair_pair_data.get('correlations', {})
            impair_to_p = correlations.get('impair_to_player', 0)
            impair_to_b = correlations.get('impair_to_banker', 0)
            pair_to_p = correlations.get('pair_to_player', 0)
            pair_to_b = correlations.get('pair_to_banker', 0)

            # Normaliser P/B (exclure TIE)
            impair_pb_total = impair_to_p + impair_to_b
            pair_pb_total = pair_to_p + pair_to_b

            if impair_pb_total > 0:
                impair_p_ratio = impair_to_p / impair_pb_total
                if abs(impair_p_ratio - 0.5) > 0.2:  # Corrélation forte
                    preferred_outcome = 'P' if impair_p_ratio > 0.5 else 'B'
                    complete_generation_space['preferred_correlations'].append({
                        'type': 'impair_to_pb',
                        'pattern': f'IMPAIR→{preferred_outcome}',
                        'strength': abs(impair_p_ratio - 0.5),
                        'confidence': min(correlations.get('total_impair_hands', 0) / 10.0, 1.0)
                    })

            if pair_pb_total > 0:
                pair_p_ratio = pair_to_p / pair_pb_total
                if abs(pair_p_ratio - 0.5) > 0.2:  # Corrélation forte
                    preferred_outcome = 'P' if pair_p_ratio > 0.5 else 'B'
                    complete_generation_space['preferred_correlations'].append({
                        'type': 'pair_to_pb',
                        'pattern': f'PAIR→{preferred_outcome}',
                        'strength': abs(pair_p_ratio - 0.5),
                        'confidence': min(correlations.get('total_pair_hands', 0) / 10.0, 1.0)
                    })

        # Contraintes SYNC/DESYNC
        if 'desync_sync' in indices_analysis:
            desync_sync_data = indices_analysis['desync_sync']

            # Taux de synchronisation global
            global_sync_rate = desync_sync_data.get('global_sync_rate', 0.5)

            if global_sync_rate > self.config.rollout2_confidence_value_high:  # Synchronisation élevée
                complete_generation_space['enhanced_constraints']['maintain_sync'] = global_sync_rate
                complete_generation_space['transition_opportunities'].append({
                    'type': 'sync_exploitation',
                    'description': 'Exploiter synchronisation élevée',
                    'strength': global_sync_rate
                })
            elif global_sync_rate < self.config.rollout3_quality_bonus_small:  # Désynchronisation élevée
                complete_generation_space['enhanced_constraints']['expect_chaos'] = 1.0 - global_sync_rate
                complete_generation_space['transition_opportunities'].append({
                    'type': 'desync_adaptation',
                    'description': 'Adapter à désynchronisation',
                    'strength': 1.0 - global_sync_rate
                })

            # Périodes de désynchronisation
            desync_periods = desync_sync_data.get('desync_periods', [])
            if desync_periods:
                avg_desync_length = sum(p.get('length', 0) for p in desync_periods) / len(desync_periods)
                complete_generation_space['enhanced_constraints']['desync_pattern_length'] = avg_desync_length

        # ================================================================
        # 2. GUIDANCE BASÉE SUR IMPACTS CROISÉS
        # ================================================================

        if 'cross_index_impacts' in synthesis:
            cross_impacts = synthesis['cross_index_impacts']

            # Impact IMPAIR/PAIR → S/O
            if 'impair_pair_to_so' in cross_impacts:
                impair_pair_so = cross_impacts['impair_pair_to_so']
                impact_strength = impair_pair_so.get('impact_strength', 0)

                if impact_strength > self.config.rollout2_adjustment_large:  # Impact significatif
                    dominant_pattern = impair_pair_so.get('dominant_pattern', '')
                    complete_generation_space['cross_impact_guidance']['impair_pair_so'] = {
                        'pattern': dominant_pattern,
                        'strength': impact_strength,
                        'exploitation_priority': 'HIGH' if impact_strength > 0.3 else 'MEDIUM'
                    }

            # Impact SYNC/DESYNC → S/O
            if 'desync_sync_to_so' in cross_impacts:
                sync_so = cross_impacts['desync_sync_to_so']
                impact_strength = sync_so.get('impact_strength', 0)

                if impact_strength > self.config.rollout2_adjustment_large:  # Impact significatif
                    dominant_pattern = sync_so.get('dominant_pattern', '')
                    complete_generation_space['cross_impact_guidance']['sync_desync_so'] = {
                        'pattern': dominant_pattern,
                        'strength': impact_strength,
                        'exploitation_priority': 'HIGH' if impact_strength > 0.3 else 'MEDIUM'
                    }

            # Impact COMBINÉ → P/B (sans TIE)
            if 'combined_to_pbt' in cross_impacts:
                combined_pbt = cross_impacts['combined_to_pbt']
                strongest_pattern = combined_pbt.get('strongest_pattern', '')
                impact_value = combined_pbt.get('strongest_impact_value', 0)

                if impact_value > self.config.rollout2_zone_confidence_threshold:  # Impact fort
                    complete_generation_space['cross_impact_guidance']['combined_pb'] = {
                        'pattern': strongest_pattern,
                        'strength': impact_value,
                        'exploitation_priority': 'VERY_HIGH' if impact_value > 0.35 else 'HIGH'
                    }

            # Impact COMBINÉ → S/O
            if 'combined_to_so' in cross_impacts:
                combined_so = cross_impacts['combined_to_so']
                strongest_pattern = combined_so.get('strongest_pattern', '')
                impact_strength = combined_so.get('overall_impact_strength', 0)

                if impact_strength > self.config.rollout2_zone_confidence_threshold:  # Impact fort
                    complete_generation_space['cross_impact_guidance']['combined_so'] = {
                        'pattern': strongest_pattern,
                        'strength': impact_strength,
                        'exploitation_priority': 'VERY_HIGH' if impact_strength > 0.35 else 'HIGH'
                    }

        # ================================================================
        # 3. HINTS D'ÉVOLUTION TEMPORELLE
        # ================================================================

        if 'variations_impact' in synthesis:
            variations = synthesis['variations_impact']

            # Évolution temporelle des corrélations
            if 'temporal_correlation_evolution' in variations:
                temporal_data = variations['temporal_correlation_evolution']

                # Phases optimales
                if 'optimal_prediction_phases' in temporal_data:
                    optimal_phases = temporal_data['optimal_prediction_phases']
                    best_phase = optimal_phases.get('best_overall_phase', 'unknown')

                    complete_generation_space['temporal_evolution_hints']['optimal_phase'] = best_phase
                    complete_generation_space['temporal_evolution_hints']['phase_confidence'] = optimal_phases.get('correlation_stability', 0)

                # Métriques de force temporelle
                if 'temporal_strength_metrics' in temporal_data:
                    temporal_metrics = temporal_data['temporal_strength_metrics']
                    evolution_strength = temporal_metrics.get('evolution_strength', 0)

                    if evolution_strength > self.config.rollout3_quality_bonus_small:  # Évolution significative
                        complete_generation_space['temporal_evolution_hints']['strong_evolution'] = evolution_strength
                        complete_generation_space['enhanced_constraints']['temporal_adaptation'] = evolution_strength

            # Force globale des variations
            if 'variation_strength_analysis' in variations:
                variation_analysis = variations['variation_strength_analysis']
                global_strength = variation_analysis.get('global_variation_strength', 0)

                if global_strength > self.config.rollout3_neutral_evaluation_value:  # Variations fortes
                    complete_generation_space['enhanced_constraints']['high_variation_mode'] = global_strength

                    # Type de variation dominant
                    weighted_analysis = variation_analysis.get('weighted_analysis', {})
                    dominant_type = weighted_analysis.get('dominant_variation_type', '')

                    if dominant_type:
                        complete_generation_space['temporal_evolution_hints']['dominant_variation'] = dominant_type

        # ================================================================
        # 4. ZONES DE CONFIANCE ENRICHIES
        # ================================================================

        if 'high_confidence_zones' in synthesis:
            confidence_zones = synthesis['high_confidence_zones']

            # Analyser zones par catégorie
            zone_categories = {}
            for zone in confidence_zones:
                if isinstance(zone, dict) and 'category' in zone:
                    category = zone['category']
                    if category not in zone_categories:
                        zone_categories[category] = []
                    zone_categories[category].append(zone)

            # Zones de corrélation S/O (priorité maximale)
            if 'so_correlation_zones' in zone_categories:
                so_zones = zone_categories['so_correlation_zones']
                best_so_zone = max(so_zones, key=lambda z: z.get('confidence', 0))

                complete_generation_space['confidence_zones']['best_so_zone'] = {
                    'pattern': best_so_zone.get('pattern', ''),
                    'confidence': best_so_zone.get('confidence', 0),
                    'exploitability': best_so_zone.get('exploitability', 'MEDIUM')
                }

            # Zones tri-dimensionnelles (ultra-haute priorité)
            if 'tri_dimensional_zones' in zone_categories:
                tri_zones = zone_categories['tri_dimensional_zones']
                best_tri_zone = max(tri_zones, key=lambda z: z.get('confidence', 0))

                complete_generation_space['confidence_zones']['best_tri_zone'] = {
                    'pattern': best_tri_zone.get('pattern', ''),
                    'confidence': best_tri_zone.get('confidence', 0),
                    'exploitability': 'ULTRA_HIGH'
                }

        # ================================================================
        # 5. AJUSTEMENT LONGUEUR SÉQUENCE
        # ================================================================

        # Ajustement basé sur complexité détectée
        complexity_factors = []

        # Facteur 1 : Variations fortes
        if complete_generation_space['enhanced_constraints'].get('high_variation_mode', 0) > 0.5:
            complexity_factors.append(1.2)  # Séquences plus longues pour variations

        # Facteur 2 : Zones tri-dimensionnelles
        if 'best_tri_zone' in complete_generation_space['confidence_zones']:
            complexity_factors.append(1.3)  # Séquences plus longues pour tri-dimensionnel

        # Facteur 3 : Impacts croisés multiples
        cross_impact_count = len(complete_generation_space['cross_impact_guidance'])
        if cross_impact_count >= 3:
            complexity_factors.append(1.1)  # Légèrement plus long pour impacts multiples

        # Application facteurs
        if complexity_factors:
            avg_factor = sum(complexity_factors) / len(complexity_factors)
            complete_generation_space['sequence_length'] = int(
                complete_generation_space['sequence_length'] * avg_factor
            )
            # Limiter à maximum raisonnable
            complete_generation_space['sequence_length'] = min(
                complete_generation_space['sequence_length'],
                self.config.extended_sequence_length
            )

        return complete_generation_space
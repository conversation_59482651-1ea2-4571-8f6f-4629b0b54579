MÉTHODE : _calculate_confidence_level
LIGNE DÉBUT : 8111
SIGNATURE : def _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str:
================================================================================

    def _calculate_confidence_level(self, global_strength: float, valid_strengths: int) -> str:
"""
    ADAPTATION BCT - _calculate_confidence_level.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule le niveau de confiance basé sur la force globale et le nombre de types valides"""

        if global_strength >= 0.8 and valid_strengths >= 4:
            return 'VERY_HIGH'
        elif global_strength >= 0.6 and valid_strengths >= 3:
            return 'HIGH'
        elif global_strength >= 0.4 and valid_strengths >= 2:
            return 'MEDIUM'
        elif global_strength >= 0.2:
            return 'LOW'
        else:
            return 'VERY_LOW'


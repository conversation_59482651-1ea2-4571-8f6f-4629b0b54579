# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 20556 à 20600
# Type: <PERSON>éth<PERSON>

def display_statistical_summary(detailed_stats: Dict, sequence_patterns: Dict):
    """
    Affiche un résumé des statistiques clés

    Args:
        detailed_stats: Statistiques détaillées
        sequence_patterns: Analyse des séquences
    """
    print(f"\n📊 RÉSUMÉ STATISTIQUE CLÉS")
    print("="*50)

    # Statistiques SYNC/DESYNC
    sync_stats = detailed_stats['sync_states']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    sync_s_rate = sync_stats['SYNC']['S_rate']
    s_bias = desync_s_rate - sync_s_rate
    total_bias = abs(s_bias) + abs((1 - desync_s_rate) - (1 - sync_s_rate))

    print(f"\n⚖️ Distribution:")
    print(f"   SYNC: {sync_stats['SYNC']['total']/detailed_stats['total_hands']*100:.1f}%")
    print(f"   DESYNC: {sync_stats['DESYNC']['total']/detailed_stats['total_hands']*100:.1f}%")

    print(f"\n🔄 Conversions S/O:")
    print(f"   SYNC → S:{sync_stats['SYNC']['S_rate']:.1%} O:{sync_stats['SYNC']['O_rate']:.1%}")
    print(f"   DESYNC → S:{sync_stats['DESYNC']['S_rate']:.1%} O:{sync_stats['DESYNC']['O_rate']:.1%}")

    print(f"\n📈 Biais détecté:")
    print(f"   Biais S: {s_bias:+.1%}")
    print(f"   Biais total: {total_bias:.1%}")

    if sequence_patterns:
        summary = sequence_patterns.get('long_sequences_summary', {})
        print(f"\n🔍 Séquences prolongées:")
        print(f"   SYNC: {summary.get('sync_sequences_count', 0)} séquences")
        print(f"   DESYNC: {summary.get('desync_sequences_count', 0)} séquences")
        print(f"   Max SYNC: {summary.get('longest_sync', 0)} manches")
        print(f"   Max DESYNC: {summary.get('longest_desync', 0)} manches")

    print(f"\n💡 Évaluation:")
    if total_bias > self.config.rollout2_adjustment_medium:
        print("   🚀 POTENTIEL ÉLEVÉ - Biais exploitable détecté!")
    elif total_bias > self.config.rollout2_adjustment_small:
        print("   ⚡ POTENTIEL MODÉRÉ - Ajustements recommandés")
    else:
        print("   📊 Biais faibles - Surveillance continue")
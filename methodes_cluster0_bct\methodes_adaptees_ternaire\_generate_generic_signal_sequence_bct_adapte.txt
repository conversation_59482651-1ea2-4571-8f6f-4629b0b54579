MÉTHODE : _generate_generic_signal_sequence
LIGNE DÉBUT : 5134
SIGNATURE : def _generate_generic_signal_sequence(self, signal: Dict, sequence_length: int, generation_space: Dict) -> List[str]:
================================================================================

    def _generate_generic_signal_sequence(self, signal: Dict, sequence_length: int, generation_space: Dict) -> List[str]:
"""
    ADAPTATION BCT - _generate_generic_signal_sequence.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Génère une séquence générique basée sur un signal non spécialisé

        LONGUEUR FIXE : Toujours 4 P/B selon spécifications AZR

        Args:
            signal: Signal source avec métadonnées
            sequence_length: Ignoré - longueur fixe à 4 P/B
            generation_space: Espace de génération avec contexte
        """
        sequence = []

        # Extraire les informations du signal
        signal_strength = signal.get('strength', 0.5)
        signal_name = signal.get('signal_name', '')

        # Stratégie basée sur la force du signal
        if signal_strength > self.config.confidence_high_threshold:
            # Signal fort : séquence agressive
            if 'PLAYER' in signal_name.upper():
                base_outcome = 'P'
            elif 'BANKER' in signal_name.upper():
                base_outcome = 'B'
            else:
                base_outcome = 'P'  # Par défaut

            # Générer séquence avec dominance du base_outcome (longueur fixe 4)
            for i in range(self.config.rollout2_fixed_length):
                if i < self.config.rollout2_fixed_length * self.config.rollout2_confidence_value_high:  # 70% du temps
                    sequence.append(base_outcome)
                else:
                    sequence.append('B' if base_outcome == 'P' else 'P')

        elif signal_strength > self.config.confidence_medium_threshold:
            # Signal modéré : séquence équilibrée avec légère préférence
            preferred = 'P' if 'PLAYER' in signal_name.upper() else 'B'

            for i in range(self.config.rollout2_fixed_length):
                if i % 3 != 2:  # 2/3 du temps
                    sequence.append(preferred)
                else:
                    sequence.append('B' if preferred == 'P' else 'P')

        else:
            # Signal faible : séquence conservative (alternance, longueur fixe 4)
            for i in range(self.config.rollout2_fixed_length):
                sequence.append('P' if i % 2 == 0 else 'B')

        return sequence

    # ========================================================================
    # MÉTHODES D'ANALYSE COMPLÈTE DES 5 INDICES
    # ========================================================================


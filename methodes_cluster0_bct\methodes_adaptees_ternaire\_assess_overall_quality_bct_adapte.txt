MÉTHODE : _assess_overall_quality
LIGNE DÉBUT : 8251
SIGNATURE : def _assess_overall_quality(self, consistency: float, sample_adequacy: float,
================================================================================

    def _assess_overall_quality(self, consistency: float, sample_adequacy: float,
                              statistical_significance: float, pattern_stability: float) -> str:
"""
    ADAPTATION BCT - _assess_overall_quality.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Évalue la qualité globale de l'analyse"""

        overall_score = (consistency + sample_adequacy + statistical_significance + pattern_stability) / 4

        if overall_score >= 0.8:
            return 'EXCELLENT'
        elif overall_score >= 0.6:
            return 'GOOD'
        elif overall_score >= 0.4:
            return 'FAIR'
        elif overall_score >= 0.2:
            return 'POOR'
        else:
            return 'VERY_POOR'


MÉTHODE : _evaluate_signal_alignment
LIGNE DÉBUT : 3473
SIGNATURE : def _evaluate_signal_alignment(self, sequence: Dict, analyzer_report: Dict) -> float:
================================================================================

    def _evaluate_signal_alignment(self, sequence: Dict, analyzer_report: Dict) -> float:
"""
    ADAPTATION BCT - _evaluate_signal_alignment.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Évalue l'alignement de la séquence avec les signaux du Rollout 1

        FOCUS : Vérifier que la séquence exploite bien les découvertes du Rollout 1
        """
        alignment_score = 0.0

        # Récupérer les signaux optimisés du Rollout 1
        signals_summary = analyzer_report.get('signals_summary', {})
        top_signals = signals_summary.get('top_signals', [])

        if not top_signals:
            # Pas de signaux optimisés, utiliser les données détaillées
            return self._evaluate_fallback_alignment(sequence, analyzer_report)

        # Analyser l'alignement avec chaque signal
        signal_alignments = []

        for signal in top_signals:
            signal_name = signal.get('signal_name', '')
            signal_strength = signal.get('strength', 0.0)
            signal_confidence = signal.get('confidence', 0.0)

            # Vérifier si la séquence exploite ce signal
            sequence_strategy = sequence.get('strategy', '')

            alignment = 0.0

            # Alignement IMPAIR/PAIR
            if 'impair_5' in signal_name and 'impair' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence
            elif ['pair_4', 'pair_6'] in signal_name and 'pair' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence

            # Alignement SYNC/DESYNC
            elif 'SYNC' in signal_name and 'sync' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence

            # Alignement S/O
            elif 'SAME' in signal_name or 'OPPOSITE' in signal_name:
                if 'so' in sequence_strategy.lower():
                    alignment = signal_strength * signal_confidence

            # Alignement générique basé sur la stratégie
            elif signal.get('strategy', '') == sequence_strategy:
                alignment = signal_strength * signal_confidence * self.config.rollout2_base_confidence_high  # Bonus pour correspondance exacte

            signal_alignments.append(alignment)

        # Score d'alignement global (moyenne pondérée)
        if signal_alignments:
            alignment_score = sum(signal_alignments) / len(signal_alignments)

        return min(1.0, alignment_score)


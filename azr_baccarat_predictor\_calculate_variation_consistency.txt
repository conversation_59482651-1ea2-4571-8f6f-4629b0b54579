# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10651 à 10667
# Type: Méthode de la classe AZRCluster

    def _calculate_variation_consistency(self, individual_strengths: Dict) -> float:
        """Calcule la consistance entre les différents types de variations"""

        strengths = [s for s in individual_strengths.values() if s > 0]
        if len(strengths) < 2:
            return 0.0

        # Consistance = inverse du coefficient de variation
        mean_strength = sum(strengths) / len(strengths)
        if mean_strength == 0:
            return 0.0

        variance = sum((s - mean_strength) ** 2 for s in strengths) / len(strengths)
        std_dev = variance ** 0.5
        cv = std_dev / mean_strength

        return max(0.0, 1.0 - cv)
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4574 à 4653
# Type: Méthode de la classe AZRCluster

    def _analyze_sync_alternation_bias_c3_specialized(self, hands_data: List) -> Dict:
        """
        🎯 C3 SPÉCIALISÉ - ANALYSE SYNC/DESYNC avec fenêtres récentes optimisées (4 manches)

        LOGIQUE DE BASE (IDENTIQUE C0) + SPÉCIALISATION C3 :
        - Utilise la fenêtre récente spécialisée C3 (4 manches)
        - Focus sur patterns moyens et équilibre réactivité/continuité
        - Vision élargie pour capturer début patterns moyens
        """
        # Utiliser la méthode de base
        sync_bias = self._analyze_sync_alternation_bias(hands_data)

        # ================================================================
        # SPÉCIALISATION C3 : FENÊTRES RÉCENTES OPTIMISÉES
        # ================================================================

        # Récupérer la fenêtre récente spécialisée C3 (4 manches)
        cluster_recent_window = self.config.get_cluster_recent_window_size(self.cluster_id)  # 4 pour C3

        # Analyser les patterns moyens récents avec fenêtre C3
        alternation_breaks = sync_bias.get('sync_alternation_breaks', [])
        if len(alternation_breaks) >= cluster_recent_window:
            recent_breaks = alternation_breaks[-cluster_recent_window:]

            # Bonus spécialisation pour patterns moyens récents
            c3_medium_breaks_bonus = self.config.zero_value
            for break_length in recent_breaks:
                if 4 <= break_length <= 6:  # Patterns moyens (spécialisation C3)
                    c3_medium_breaks_bonus += self.config.multiplier_increment_03

            sync_bias['c3_medium_breaks_bonus'] = min(self.config.one_value, c3_medium_breaks_bonus)
            sync_bias['c3_recent_breaks_count'] = len(recent_breaks)
            sync_bias['c3_recent_window_applied'] = cluster_recent_window

        # ================================================================
        # SPÉCIALISATION C3 : ÉQUILIBRE RÉACTIVITÉ/CONTINUITÉ
        # ================================================================

        # Analyser l'équilibre des états SYNC/DESYNC récents
        if len(hands_data) >= cluster_recent_window:
            recent_hands = hands_data[-cluster_recent_window:]
            sync_states_recent = []

            expected_pattern = ['P', 'B']
            for i, hand in enumerate(recent_hands):
                actual_outcome = hand.pbt_result
                expected = expected_pattern[i % len(expected_pattern)]

                if actual_outcome == expected:
                    sync_states_recent.append('SYNC')
                else:
                    sync_states_recent.append('DESYNC')

            # Mesurer l'équilibre récent (spécialisation C3)
            sync_count = sync_states_recent.count('SYNC')
            desync_count = sync_states_recent.count('DESYNC')
            balance_score = 1.0 - abs(sync_count - desync_count) / cluster_recent_window

            sync_bias['c3_balance_score'] = balance_score
            sync_bias['c3_recent_sync_states'] = sync_states_recent

        # ================================================================
        # CONFIANCE FINALE AVEC BONUS SPÉCIALISATION C3
        # ================================================================

        # Confiance de base
        base_confidence = sync_bias.get('exploitation_confidence', self.config.zero_value)

        # Bonus spécialisation C3
        c3_bonus = (
            sync_bias.get('c3_medium_breaks_bonus', self.config.zero_value) * self.config.confidence_multiplier_03 +
            sync_bias.get('c3_balance_score', self.config.zero_value) * self.config.confidence_multiplier_04
        )

        # Confiance finale avec spécialisation C3
        sync_bias['exploitation_confidence'] = min(self.config.one_value, base_confidence + c3_bonus)
        sync_bias['c3_specialization_applied'] = True
        sync_bias['c3_total_bonus'] = c3_bonus

        return sync_bias
MÉTHODE : _calculate_rupture_probability
LIGNE DÉBUT : 10738
SIGNATURE : def _calculate_rupture_probability(self, impair_4_count + pair_6_count: int, pair_4_count + pair_6_count: int) -> float:
================================================================================

    def _calculate_rupture_probability(self, impair_4_count + pair_6_count: int, pair_4_count + pair_6_count: int) -> float:
"""
    ADAPTATION BCT - _calculate_rupture_probability.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule probabilité de rupture avec analyse asymétrique

        IMPAIR étant 30x plus rare, les seuils et incréments sont différents
        """
        # Analyse IMPAIR (priorité car beaucoup plus rare)
        if impair_4_count + pair_6_count >= self.config.impair_alert_threshold_high:  # 3+
            return min(
                self.config.rupture_probability_impair_base +
                (impair_4_count + pair_6_count - self.config.impair_alert_threshold_high) * self.config.rupture_probability_impair_increment,
                self.config.rupture_probability_max
            )
        elif impair_4_count + pair_6_count >= self.config.impair_alert_threshold_medium:  # 2
            # Même 2 IMPAIR consécutifs est significatif
            return self.config.rupture_probability_impair_base

        # Analyse PAIR (seuils plus élevés car plus commun)
        elif pair_4_count + pair_6_count >= self.config.pair_alert_threshold_high:  # 9+
            return min(
                self.config.rupture_probability_pair_base +
                (pair_4_count + pair_6_count - self.config.pair_alert_threshold_high) * self.config.rupture_probability_pair_increment,
                self.config.rupture_probability_max
            )
        elif pair_4_count + pair_6_count >= self.config.pair_alert_threshold_medium:  # 6+
            return self.config.rupture_probability_pair_base

        else:
            return self.config.rupture_probability_base  # Probabilité neutre


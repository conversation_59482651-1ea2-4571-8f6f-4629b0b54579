# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17567 à 17597
# Type: Méthode de la classe AZRBaccaratPredictor

    def _calculate_pair_context_value(self, pair_position: int, impair_signals: List) -> float:
        """Calcule la valeur contextuelle d'un PAIR en fonction des signaux IMPAIR"""
        if not impair_signals:
            return self.config.rollout_analyzer_context_min_value  # Valeur minimale

        context_value = 0.0

        # Analyser la proximité avec les signaux IMPAIR
        for signal in impair_signals:
            if signal['signal_type'] == 'isolated_impair':
                impair_pos = signal.get('position', 0)
                distance = abs(pair_position - impair_pos)

                # Plus proche = plus de valeur contextuelle
                # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
                if distance == self.config.distance_adjacent:  # Adjacent
                    context_value += self.config.context_value_adjacent
                elif distance <= self.config.distance_close:  # Proche
                    context_value += self.config.context_value_close
                elif distance <= self.config.distance_moderate:  # Modéré
                    context_value += self.config.context_value_moderate

            elif signal['signal_type'] == 'consecutive_impairs':
                impair_positions = signal.get('positions', [])
                for impair_pos in impair_positions:
                    distance = abs(pair_position - impair_pos)
                    # ⚠️ UTILISATION DES PARAMÈTRES CENTRALISÉS AZRConfig
                    if distance <= self.config.distance_very_close:  # Très proche d'une séquence IMPAIR
                        context_value += self.config.context_value_very_close

        return min(self.config.correlation_player_value, context_value)  # Normalisation
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 1666 à 1686
# Type: Méthode de la classe AZRConfig

    def get_cluster_recent_window_size(self, cluster_id: int) -> int:
        """
        Récupère la taille de fenêtre récente spécialisée pour un cluster donné.

        Args:
            cluster_id: ID du cluster (0-7)

        Returns:
            Taille de fenêtre récente optimisée pour ce cluster
        """
        cluster_windows = {
            0: self.cluster0_recent_window_size,  # C0: Standard (3)
            1: self.cluster1_recent_window_size,  # C1: Standard (3)
            2: self.cluster2_recent_window_size,  # C2: Patterns courts (2)
            3: self.cluster3_recent_window_size,  # C3: <PERSON><PERSON><PERSON> moyens (4)
            4: self.cluster4_recent_window_size,  # C4: Pattern<PERSON> longs (6)
            5: self.cluster5_recent_window_size,  # C5: Corrélations (3)
            6: self.cluster6_recent_window_size,  # C6: Sync/Desync (3)
            7: self.cluster7_recent_window_size,  # C7: Adaptatif (5)
        }
        return cluster_windows.get(cluster_id, self.rollout_analyzer_recent_window_size)
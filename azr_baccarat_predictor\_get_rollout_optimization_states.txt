# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15098 à 15130
# Type: Méthode de la classe AZRBaccaratPredictor

    def _get_rollout_optimization_states(self) -> Dict:
        """
        🔄 ÉTAT DES ROLLOUTS OPTIMISÉS

        Returns:
            Configuration optimale découverte pour chaque rollout
        """
        return {
            'rollout_1_analyzer': {
                'optimal_analysis_depth': getattr(self, 'rollout_1_analysis_depth', 10),
                'best_correlation_threshold': getattr(self, 'rollout_1_correlation_threshold', 0.7),
                'discovered_biases': getattr(self, 'rollout_1_biases', {}),
                'analysis_efficiency': getattr(self, 'rollout_1_efficiency', 0.0)
            },
            'rollout_2_generator': {
                'optimal_sequence_length': getattr(self, 'rollout_2_sequence_length', 4),
                'best_generation_strategy': getattr(self, 'rollout_2_strategy', 'probability_based'),
                'generation_accuracy': getattr(self, 'rollout_2_generation_accuracy', 0.0),
                'diversity_score': getattr(self, 'rollout_2_diversity', 0.0)
            },
            'rollout_3_predictor': {
                'optimal_selection_criteria': getattr(self, 'rollout_3_selection_criteria', 'confidence_weighted'),
                'best_confidence_threshold': getattr(self, 'rollout_3_confidence_threshold', 0.6),
                'prediction_accuracy': getattr(self, 'rollout_3_prediction_accuracy', 0.0),
                'consensus_efficiency': getattr(self, 'rollout_3_consensus_efficiency', 0.0)
            },
            'system_optimization': {
                'optimal_cluster_count': 8,
                'best_parallel_configuration': self.config.parallel_rollouts,
                'memory_efficiency': getattr(self, 'memory_efficiency_score', 0.0),
                'cpu_utilization': getattr(self, 'cpu_utilization_score', 0.0)
            }
        }
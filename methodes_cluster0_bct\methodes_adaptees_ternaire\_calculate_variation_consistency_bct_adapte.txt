MÉTHODE : _calculate_variation_consistency
LIGNE DÉBUT : 8187
SIGNATURE : def _calculate_variation_consistency(self, individual_strengths: Dict) -> float:
================================================================================

    def _calculate_variation_consistency(self, individual_strengths: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_variation_consistency.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la consistance entre les différents types de variations"""

        strengths = [s for s in individual_strengths.values() if s > 0]
        if len(strengths) < 2:
            return 0.0

        # Consistance = inverse du coefficient de variation
        mean_strength = sum(strengths) / len(strengths)
        if mean_strength == 0:
            return 0.0

        variance = sum((s - mean_strength) ** 2 for s in strengths) / len(strengths)
        std_dev = variance ** 0.5
        cv = std_dev / mean_strength

        return max(0.0, 1.0 - cv)


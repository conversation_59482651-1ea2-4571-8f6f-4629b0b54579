MÉTHODE : _calculate_combined_pbt_impact_strength
LIGNE DÉBUT : 6243
SIGNATURE : def _calculate_combined_pbt_impact_strength(self, impact_analysis: Dict) -> float:
================================================================================

    def _calculate_combined_pbt_impact_strength(self, impact_analysis: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_combined_pbt_impact_strength.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule la force globale d'impact des états combinés sur P/B

        IMPORTANT: Analyse seulement P/B, pas les TIE
        Les prédictions ciblent P/B pour les stratégies de mise

        Args:
            impact_analysis: Dictionnaire avec analyse d'impact pour chaque état combiné

        Returns:
            Score de force d'impact P/B (0.0 à 1.0)
        """

        if not impact_analysis or len(impact_analysis) == 0:
            return 0.0

        total_pb_strength = 0.0
        valid_pb_states = 0
        total_pb_sample = 0

        for state, data in impact_analysis.items():
            # Vérifier données P/B disponibles (TIE exclus)
            if 'to_player' in data and 'to_banker' in data and 'total_occurrences' in data:

                # Force P/B = écart par rapport à 50/50 P/B
                player_ratio = data['to_player']
                banker_ratio = data['to_banker']
                tie_ratio = data.get('to_tie', 0)

                # Normaliser P/B en excluant TIE
                pb_total = player_ratio + banker_ratio
                if pb_total == 0:
                    continue  # Ignorer si seulement TIE

                # Ratios P/B normalisés (sans TIE)
                normalized_player = player_ratio / pb_total
                normalized_banker = banker_ratio / pb_total

                # Déviation P/B par rapport à l'équilibre 50/50
                pb_deviation = abs(normalized_player - 0.5)

                # Pondération par échantillon P/B (exclure TIE du comptage)
                total_occurrences = data['total_occurrences']
                pb_sample_size = int(total_occurrences * pb_total)  # Approximation P/B seulement
                pb_sample_weight = min(pb_sample_size / 10.0, 1.0)  # Poids max à 10+ P/B

                # Force P/B pondérée
                pb_state_strength = pb_deviation * pb_sample_weight

                total_pb_strength += pb_state_strength
                total_pb_sample += pb_sample_size
                valid_pb_states += 1

        if valid_pb_states == 0:
            return 0.0

        # Force P/B moyenne
        average_pb_strength = total_pb_strength / valid_pb_states

        # Bonus diversité états P/B (4 états = max fiabilité)
        pb_diversity_bonus = min(valid_pb_states / 4.0, 1.0)

        # Bonus échantillon P/B global
        pb_sample_bonus = min(total_pb_sample / 20.0, 1.0)  # 20+ résultats P/B

        # Score P/B final
        final_pb_strength = average_pb_strength * pb_diversity_bonus * pb_sample_bonus

        return min(final_pb_strength, 1.0)


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14611 à 14618
# Type: Méthode de la classe AZRBaccaratInterface

    def _enable_buttons_in_widget(self, widget):
        """Réactive récursivement tous les boutons dans un widget"""
        for child in widget.winfo_children():
            if isinstance(child, tk.Button):
                child.config(state=tk.NORMAL)
            else:
                # Récursion pour les conteneurs
                self._enable_buttons_in_widget(child)
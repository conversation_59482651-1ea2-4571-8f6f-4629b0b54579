MÉTHODE : _generate_bias_generation_guidance_c2
LIGNE DÉBUT : 1351
SIGNATURE : def _generate_bias_generation_guidance_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
================================================================================

    def _generate_bias_generation_guidance_c2(self, bias_synthesis: Dict, c2_specialization: Dict) -> Dict:
"""
    ADAPTATION BCT - _generate_bias_generation_guidance_c2.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        🎯 C2 SPÉCIALISÉ - Guidance génération avec spécialisation patterns courts
        """
        # Utiliser la méthode de base
        base_guidance = self._generate_bias_generation_guidance(bias_synthesis)

        # Ajouter la guidance spécialisée C2
        c2_guidance = {
            'c2_short_patterns_guidance': {
                'focus': 'patterns_courts_2_3_manches',
                'reactivity': 'maximale_micro_changements',
                'fenetre_optimale': c2_specialization.get('fenetre_recente_optimisee', 2),
                'bonus_disponible': c2_specialization.get('specialization_bonus', self.config.zero_value)
            }
        }

        # Fusionner la guidance
        base_guidance.update(c2_guidance)
        base_guidance['specialization_type'] = 'C2_patterns_courts'

        return base_guidance


MÉTHODE : _classify_confidence_level
LIGNE DÉBUT : 4700
SIGNATURE : def _classify_confidence_level(self, signal_confidence: float) -> str:
================================================================================

    def _classify_confidence_level(self, signal_confidence: float) -> str:
"""
    ADAPTATION BCT - _classify_confidence_level.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Classifie le niveau de confiance d'un signal

        NOUVEAU : Utilise les paramètres centralisés pour classification
        """
        if signal_confidence > self.config.rollout2_signal_confidence_high:
            return self.config.rollout2_confidence_level_excellent
        elif signal_confidence > self.config.rollout2_signal_confidence_medium:
            return self.config.rollout2_confidence_level_good
        elif signal_confidence > self.config.rollout2_signal_confidence_low:
            return self.config.rollout2_confidence_level_acceptable
        else:
            return self.config.rollout2_confidence_level_poor

    # ========================================================================
    # 🎲 MÉTHODES DE GÉNÉRATION SPÉCIALISÉES ROLLOUT 2
    # ========================================================================


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 10589 à 10605
# Type: Méthode de la classe AZRCluster

    def _generate_exploitation_recommendation(self, global_strength: float, dominant_type: str,
                                            individual_strengths: Dict) -> str:
        """Génère une recommandation d'exploitation des patterns détectés"""

        if global_strength >= self.config.rollout2_exploitation_threshold_high:
            return 'STRONG_PATTERNS_DETECTED'
        elif global_strength >= self.config.rollout2_exploitation_threshold_medium:
            if dominant_type == 'temporal_evolution_strength':
                return 'TEMPORAL_PATTERNS_EXPLOITABLE'
            elif dominant_type == 'combined_state_changes_strength':
                return 'COMPLEX_STATE_PATTERNS_DETECTED'
            else:
                return 'MODERATE_PATTERNS_DETECTED'
        elif global_strength >= self.config.rollout1_global_strength_threshold:
            return 'WEAK_PATTERNS_USE_WITH_CAUTION'
        else:
            return 'NO_SIGNIFICANT_PATTERNS'
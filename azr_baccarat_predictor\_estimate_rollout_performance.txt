# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17356 à 17367
# Type: Méthode de la classe AZRBaccaratPredictor

    def _estimate_rollout_performance(self) -> float:
        """Estime la performance des rollouts par seconde"""
        base_rollouts_per_second = self.config.rollout_base_performance  # Estimation conservative

        if self.config.parallel_rollouts:
            # Parallélisation sur 8 cœurs avec efficacité configurée
            parallel_efficiency = self.config.rollout_parallel_efficiency
            estimated_performance = base_rollouts_per_second * self.config.cpu_cores * parallel_efficiency
        else:
            estimated_performance = base_rollouts_per_second

        return estimated_performance
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4696 à 4818
# Type: Méthode de la classe AZRCluster

    def _analyze_combined_structural_bias(self, impair_bias: Dict, sync_bias: Dict, hands_data: List) -> Dict:
        """
        PRIORITÉ 3 : Analyse des biais combinés (impair+desync)

        LOGIQUE ANTI-MOYENNES :
        - Identifie les combinaisons ultra-rares (impair+desync)
        - Mesure la force du biais combiné
        - Génère un signal prédictif renforcé
        """
        import statistics

        combined_bias = {
            'rare_combinations_detected': [],
            'combined_bias_strength': self.config.zero_value,
            'reinforced_signal_strength': self.config.zero_value,
            'exploitation_multiplier': self.config.one_value,
            'ultra_rare_events': [],
            'combined_confidence': self.config.zero_value
        }

        # Extraction des données combinées
        position_types = []
        sync_sequence = []
        pb_outcomes = []

        # Pattern attendu pour sync
        expected_pattern = ['P', 'B']

        for i, hand in enumerate(hands_data):
            # Position impair/pair
            hand_number = i + 1
            position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'
            position_types.append(position_type)

            # Sync/desync
            actual_outcome = hand.pbt_result
            expected = expected_pattern[i % len(expected_pattern)]

            if actual_outcome == expected:
                sync_status = 'SYNC'
            elif actual_outcome == 'T':
                sync_status = 'DESYNC_TIE'
            else:
                sync_status = 'DESYNC'

            sync_sequence.append(sync_status)

            # P/B pour corrélation
            if actual_outcome in ['P', 'B']:
                pb_outcomes.append(actual_outcome)

        # Détection des combinaisons rares
        rare_combinations = []
        ultra_rare_events = []

        for i in range(len(position_types)):
            pos_type = position_types[i]
            sync_status = sync_sequence[i]

            # Combinaisons rares identifiées
            if pos_type == 'IMPAIR' and sync_status in ['DESYNC', 'DESYNC_TIE']:
                rare_combinations.append(f"IMPAIR_{sync_status}")

                # Ultra-rare : IMPAIR + DESYNC_TIE
                if sync_status == 'DESYNC_TIE':
                    ultra_rare_events.append(i)

        combined_bias['rare_combinations_detected'] = rare_combinations
        combined_bias['ultra_rare_events'] = ultra_rare_events

        # Mesure de la force du biais combiné
        total_hands = len(position_types)
        rare_count = len(rare_combinations)
        ultra_rare_count = len(ultra_rare_events)

        if total_hands > 0:
            # Fréquence des événements rares
            rare_frequency = rare_count / total_hands
            ultra_rare_frequency = ultra_rare_count / total_hands

            # Force du biais = écart par rapport à la fréquence attendue
            # Attendu : IMPAIR (50%) × DESYNC (variable) = très faible
            expected_rare_frequency = self.config.frequency_expected_rare * self.config.frequency_conservative_factor  # Estimation conservative

            rare_deviation = abs(rare_frequency - expected_rare_frequency)
            combined_bias['combined_bias_strength'] = rare_deviation

        # Signal prédictif renforcé
        if rare_count > 0:
            # Amplification basée sur la rareté
            rarity_amplifier = min(self.config.rarity_amplifier_max, rare_count / max(self.config.one_value, total_hands / self.config.rarity_divisor_base))

            # Combinaison des forces de biais individuels
            impair_strength = impair_bias.get('exploitation_confidence', self.config.zero_value)
            sync_strength = sync_bias.get('exploitation_confidence', self.config.zero_value)

            # Signal renforcé = moyenne pondérée × amplificateur de rareté
            base_signal = (impair_strength + sync_strength) / 2
            combined_bias['reinforced_signal_strength'] = base_signal * rarity_amplifier

        # Multiplicateur d'exploitation
        if ultra_rare_count > 0:
            # Ultra-rare events = multiplicateur élevé
            combined_bias['exploitation_multiplier'] = self.config.one_value + (ultra_rare_count * self.config.half_value)
        elif rare_count > self.config.sample_size_minimum_2:
            # Événements rares multiples = multiplicateur modéré
            combined_bias['exploitation_multiplier'] = self.config.one_value + (rare_count * self.config.multiplier_increment_02)
        else:
            combined_bias['exploitation_multiplier'] = self.config.one_value

        # Confiance combinée
        impair_confidence = impair_bias.get('exploitation_confidence', self.config.zero_value)
        sync_confidence = sync_bias.get('exploitation_confidence', self.config.zero_value)

        if rare_count > 0:
            # Bonus de confiance pour les combinaisons rares
            rarity_bonus = min(self.config.weight_30_percent, rare_count / self.config.normalization_factor_10)
            combined_confidence = ((impair_confidence + sync_confidence) / self.config.normalization_factor_2) + rarity_bonus
            combined_bias['combined_confidence'] = min(self.config.one_value, combined_confidence)
        else:
            combined_bias['combined_confidence'] = (impair_confidence + sync_confidence) / 2

        return combined_bias
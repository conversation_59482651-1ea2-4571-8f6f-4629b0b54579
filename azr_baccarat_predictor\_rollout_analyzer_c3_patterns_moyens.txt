# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 4302 à 4487
# Type: Méthode de la classe AZRCluster

    def _rollout_analyzer_c3_patterns_moyens(self, standardized_sequence: Dict) -> Dict:
        """
        🎯 CLUSTER C3 - ROLLOUT 1 ANALYSEUR PATTERNS MOYENS (4-6 manches)

        LOGIQUE ALIGNÉE SUR RÉFÉRENCE C0 :
        1. Extraction systématique des 5 index (IDENTIQUE à C0)
        2. Analyse des corrélations 1,2,3 → 4,5 (IDENTIQUE à C0)
        3. Spécialisation patterns moyens EN PLUS de la logique de base
        4. Fenêtres récentes spécialisées (4 manches) intégrées dans la logique

        SPÉCIALISATION C3 :
        - Focus sur patterns moyens (4-6 manches)
        - Vision élargie pour capturer début patterns moyens
        - Équilibre réactivité/continuité
        """
        try:
            analysis_start = time.time()

            # Extraction données complètes depuis brûlage (IDENTIQUE C0)
            hands_data = standardized_sequence.get('hands_history', [])
            if not hands_data:
                return {'error': 'Aucune donnée historique disponible'}

            # ================================================================
            # PHASE 1 : EXTRACTION SYSTÉMATIQUE DES 5 INDEX (IDENTIQUE C0)
            # ================================================================

            position_types = []
            sync_states = []
            combined_states = []
            pb_outcomes = []
            so_outcomes = []

            # Extraction complète de TOUS les indices (IDENTIQUE C0)
            for hand_number in range(int(self.config.one_value), len(hands_data) + int(self.config.one_value)):
                hand_data = hands_data[hand_number - int(self.config.rollout_analyzer_sequence_increment)]

                # Index 1 : PAIR/IMPAIR
                position_type = 'IMPAIR' if hand_number % int(self.config.two_value) == int(self.config.one_value) else 'PAIR'
                position_types.append(position_type)

                # Index 2 : SYNC/DESYNC
                sync_state = getattr(hand_data, 'sync_state', 'SYNC')
                sync_states.append(sync_state)

                # Index 3 : COMBINED
                combined_state = getattr(hand_data, 'combined_state', f"{position_type}_{sync_state}")
                combined_states.append(combined_state)

                # Index 4 : P/B/T
                pbt_result = hand_data.pbt_result
                if pbt_result in ['P', 'B']:
                    pb_outcomes.append(pbt_result)

                # Index 5 : S/O
                so_result = getattr(hand_data, 'so_conversion', None)
                if so_result in ['S', 'O']:
                    so_outcomes.append(so_result)

            # ================================================================
            # PHASE 2 : ANALYSE DE BASE (IDENTIQUE C0 avec fenêtres spécialisées)
            # ================================================================

            # PRIORITÉ 1 : ANALYSE COMPLÈTE DES IMPAIRS avec spécialisation C3
            impair_bias_analysis = self._analyze_impair_consecutive_bias_c3_specialized(
                hands_data, position_types, sync_states, combined_states, pb_outcomes, so_outcomes
            )

            # PRIORITÉ 2 : ANALYSE PAIRS SIMPLIFIÉE (compatible C3)
            pair_bias_analysis = {
                'exploitation_confidence': impair_bias_analysis.get('exploitation_confidence', 0.0) * 0.8,
                'pair_context_analysis': 'simplified_for_c3',
                'c3_compatible': True
            }

            # PRIORITÉ 3 : ANALYSE SYNC/DESYNC avec spécialisation C3
            sync_bias_analysis = self._analyze_sync_alternation_bias_c3_specialized(hands_data)

            # PRIORITÉ 4 : ANALYSE BIAIS COMBINÉS (IDENTIQUE C0)
            combined_bias_analysis = self._analyze_combined_structural_bias(
                impair_bias_analysis, sync_bias_analysis, hands_data
            )

            # ================================================================
            # PHASE 3 : CORRÉLATIONS 1,2,3 → 4,5 (IDENTIQUE C0)
            # ================================================================

            pb_correlation_analysis = self._correlate_bias_to_pb_variations(
                impair_bias_analysis, sync_bias_analysis, combined_bias_analysis, hands_data
            )

            so_correlation_analysis = self._correlate_bias_to_so_variations(
                pb_correlation_analysis, hands_data
            )

            # ================================================================
            # PHASE 4 : SPÉCIALISATION C3 PATTERNS MOYENS
            # ================================================================

            # Spécialisation patterns moyens appliquée EN PLUS de la logique de base
            c3_specialization = self._apply_c3_medium_patterns_specialization({
                'impair_bias': impair_bias_analysis,
                'pair_bias': pair_bias_analysis,
                'sync_bias': sync_bias_analysis,
                'combined_bias': combined_bias_analysis,
                'pb_correlation': pb_correlation_analysis,
                'so_correlation': so_correlation_analysis
            })

            # ================================================================
            # PHASE 5 : SYNTHÈSE FINALE AVEC SPÉCIALISATION C3
            # ================================================================

            # Synthèse avec spécialisation C3
            bias_synthesis = {
                'exploitation_quality': (
                    impair_bias_analysis.get('exploitation_confidence', 0.0) +
                    sync_bias_analysis.get('exploitation_confidence', 0.0) +
                    combined_bias_analysis.get('exploitation_confidence', 0.0) +
                    c3_specialization.get('specialization_bonus', 0.0)
                ) / 4,
                'strongest_bias': 'c3_patterns_moyens',
                'exploitation_confidence': min(1.0, c3_specialization.get('specialization_bonus', 0.0) + 0.5),
                'bias_persistence': c3_specialization.get('medium_pattern_score', 0.0)
            }

            # Génération des signaux avec spécialisation C3
            bias_signals_summary = {
                'c3_patterns_moyens': {
                    'signal_strength': c3_specialization.get('specialization_bonus', 0.0),
                    'confidence': bias_synthesis['exploitation_confidence'],
                    'specialization': 'patterns_moyens_4_6_manches'
                }
            }

            bias_generation_guidance = {
                'primary_guidance': 'focus_patterns_moyens',
                'fenetre_optimale': c3_specialization.get('fenetre_recente_optimisee', 4),
                'vision_mode': 'elargie'
            }

            bias_quick_access = {
                'cluster_type': 'C3_patterns_moyens',
                'specialization_bonus': c3_specialization.get('specialization_bonus', 0.0),
                'fenetre_optimisee': c3_specialization.get('fenetre_recente_optimisee', 4)
            }

            # Rapport final OPTIMISÉ pour exploitation de biais avec spécialisation C3
            analyzer_report = {
                # SIGNAUX DE BIAIS EXPLOITABLES avec spécialisation C3
                'bias_signals_summary': bias_signals_summary,
                'bias_generation_guidance': bias_generation_guidance,
                'bias_quick_access': bias_quick_access,

                # ANALYSE DÉTAILLÉE DES BIAIS STRUCTURELS (IDENTIQUE C0)
                'structural_bias_analysis': {
                    'impair_consecutive_bias': impair_bias_analysis,
                    'sync_alternation_bias': sync_bias_analysis,
                    'combined_structural_bias': combined_bias_analysis,
                    'pb_correlation_bias': pb_correlation_analysis,
                    'so_correlation_bias': so_correlation_analysis
                },
                'bias_synthesis': bias_synthesis,

                # SPÉCIALISATION C3
                'c3_specialization': c3_specialization,
                'cluster_specialization_type': 'patterns_moyens_4_6_manches',

                'exploitation_metadata': {
                    'total_hands_analyzed': len(hands_data),
                    'bias_exploitation_quality': bias_synthesis.get('exploitation_quality', self.config.zero_value),
                    'strongest_bias_detected': bias_synthesis.get('strongest_bias', {}),
                    'exploitation_confidence': bias_synthesis.get('exploitation_confidence', self.config.zero_value),
                    'bias_persistence_score': bias_synthesis.get('bias_persistence', self.config.zero_value),
                    'c3_specialization_bonus': c3_specialization.get('specialization_bonus', self.config.zero_value)
                },
                'execution_time_ms': (time.time() - analysis_start) * 1000,
                'cluster_id': self.cluster_id,
                'analysis_type': 'structural_bias_exploitation_c3_patterns_moyens'
            }

            return analyzer_report

        except Exception as e:
            logger.error(f"Erreur rollout analyzer C3 cluster {self.cluster_id}: {e}")
            return {'error': str(e)}
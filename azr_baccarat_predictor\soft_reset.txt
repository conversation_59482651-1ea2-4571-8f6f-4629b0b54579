# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14373 à 14390
# Type: Méthode de la classe AZRBaccaratInterface

    def soft_reset(self):
        """
        🔄 SOFT RESET : Remet à zéro la partie en cours SEULEMENT

        Réinitialise :
        - Les indices de la partie courante (IMPAIR/PAIR, SYNC/DESYNC, COMBINÉ)
        - Les compteurs de manches
        - L'historique de la session courante

        PRÉSERVE :
        - Toute l'intelligence acquise (patterns, baselines, découvertes)
        - Les fichiers de persistance
        - L'apprentissage global du système
        """
        # Utiliser la méthode commune de reset de partie
        self._reset_current_game_only()

        logger.info("🔄 SOFT RESET : Nouvelle partie initialisée - Intelligence préservée")
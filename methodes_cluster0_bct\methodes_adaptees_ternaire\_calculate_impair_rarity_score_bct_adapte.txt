MÉTHODE : _calculate_impair_rarity_score
LIGNE DÉBUT : 6059
SIGNATURE : def _calculate_impair_rarity_score(self, impair_consecutive: int) -> float:
================================================================================

    def _calculate_impair_rarity_score(self, impair_consecutive: int) -> float:
"""
    ADAPTATION BCT - _calculate_impair_rarity_score.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Calcule score de rareté pour séquences IMPAIR

        Basé sur la rareté statistique : 650 parties pures IMPAIR sur toutes possibilités
        """
        if impair_consecutive == 0:
            return 0.0
        elif impair_consecutive == 1:
            return self.config.rollout1_impair_consecutive_common  # Assez commun
        elif impair_consecutive == 2:
            return self.config.rollout1_impair_consecutive_rare  # Rare
        elif impair_consecutive == 3:
            return self.config.rollout1_impair_consecutive_very_rare  # Très rare
        elif impair_consecutive == 4:
            return 0.9  # Ultra-rare
        else:  # 5+
            return 0.95  # Extrêmement rare


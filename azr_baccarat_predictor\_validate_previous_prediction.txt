# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14797 à 14812
# Type: Méthode de la classe AZRBaccaratPredictor

    def _validate_previous_prediction(self, actual_result: str):
        """Valide la prédiction précédente et met à jour les métriques"""
        if len(self.predictions_history) == 0:
            return

        last_prediction = self.predictions_history[-1]
        is_correct = (last_prediction == actual_result)

        if is_correct:
            self.correct_predictions += self.config.one_value

        self.total_predictions += self.config.one_value
        self.current_accuracy = self.correct_predictions / self.total_predictions
        self.accuracy_history.append(self.current_accuracy)

        logger.info(f"✅ Validation: Prédit={last_prediction}, Réel={actual_result}, Correct={is_correct}, Précision={self.current_accuracy:.3f}")
# CLASSE ADAPTÉE POUR BCT.PY
# Source: class_AZRConfig.txt (AZR)
# Adaptation: Configuration centralisée BCT - Cluster 0 uniquement avec INDEX détaillés

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

@dataclass
class AZRConfigBCT:
    """
    🏗️ CONFIGURATION CENTRALISÉE DU SYSTÈME BCT ADAPTÉ AZR
    
    ⚠️ ARCHITECTURE MAÎTRE BCT - TOUS LES PARAMÈTRES CENTRALISÉS
    
    Cette classe centralise TOUS les paramètres du système BCT adapté selon
    l'architecture AZR simplifiée : 1 Cluster × 3 Rollouts = 3 rollouts séquentiels
    
    🎯 ORGANISATION BASÉE SUR L'ARCHITECTURE BCT - 8 SECTIONS :
    
    📊 SECTION A - VALEURS DE BASE ET SYSTÈME BCT
    🔍 SECTION R1 - ROLLOUT 1 (ANALYSEUR BCT) - 0-60ms
    🎯 SECTION R2 - ROLLOUT 2 (GÉNÉRATEUR BCT) - 60-110ms  
    🎲 SECTION R3 - ROLLOUT 3 (PRÉDICTEUR BCT) - 110-170ms
    🏗️ SECTION C0 - CLUSTER 0 UNIQUE BCT
    🎯 SECTION BCT - SPÉCIFICITÉS SYSTÈME COMPTAGE BCT
    📊 SECTION P - PERFORMANCE ET MONITORING BCT
    🔧 SECTION S - CONFIGURATION BACCARAT BCT
    
    ✅ Architecture cohérente BCT : Organisation par composant fonctionnel
    ✅ Maintenance facilitée : Paramètres groupés par rollout BCT
    ✅ Performance optimisée : Accès direct aux paramètres BCT
    ✅ Conformité garantie : Respect système comptage de référence
    ✅ Documentation intégrée : Chaque paramètre documenté avec sa fonction BCT
    
    ARCHITECTURE SYSTÈME BCT :
    =========================
    • 1 Cluster unique (Cluster 0) : C0
    • 3 Rollouts par cluster : Analyseur BCT, Générateur BCT, Prédicteur BCT
    • Pipeline séquentiel : R1 → R2 → R3
    • Communication shared memory locale
    • Exploitation biais structurels BCT
    • INDEX détaillés : pair_4, impair_5, pair_6
    """
    
    # ========================================================================
    # 📊 SECTION A - VALEURS DE BASE ET SYSTÈME BCT
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 A.1 - VALEURS NUMÉRIQUES DE BASE BCT
    # ────────────────────────────────────────────────────────────────────────
    zero_value: float = 0.0                              # Valeur zéro de référence BCT
    one_value: float = 1.0                               # Valeur un de référence BCT
    two_value: float = 2.0                               # Valeur deux de référence BCT
    half_value: float = 0.5                              # Valeur moitié de référence BCT
    version_number: str = "1.0"                          # Version architecture BCT
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 A.2 - CONFIGURATION SYSTÈME BCT
    # ────────────────────────────────────────────────────────────────────────
    cpu_cores: int = 1                                   # Nombre de cœurs utilisés (1 cluster)
    clusters_total: int = 1                              # Nombre total de clusters (0 uniquement)
    rollouts_per_cluster: int = 3                        # Rollouts par cluster (R1, R2, R3)
    rollouts_total: int = 3                              # Total rollouts (1×3=3)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 A.3 - TIMING SYSTÈME BCT (170ms cycle optimal)
    # ────────────────────────────────────────────────────────────────────────
    cycle_total_time_ms: int = 170                       # Temps total cycle optimal (170ms)
    cluster_analysis_time_ms: int = 60                   # Temps Rollout 1 Analyseur BCT (0-60ms)
    cluster_generation_time_ms: int = 50                 # Temps Rollout 2 Générateur BCT (60-110ms)
    cluster_prediction_time_ms: int = 60                 # Temps Rollout 3 Prédicteur BCT (110-170ms)
    cluster_max_execution_time_ms: int = 200             # Temps maximum autorisé (200ms)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 A.4 - VALEURS DE CONVERSION ET FACTEURS
    # ────────────────────────────────────────────────────────────────────────
    milliseconds_conversion_factor: float = 1000.0       # Facteur conversion secondes → millisecondes
    probability_neutral: float = 0.5                     # Probabilité neutre (50%)
    metrics_smoothing_factor: float = 0.1                # Facteur lissage métriques (10%)
    
    # ========================================================================
    # 🔍 SECTION R1 - ROLLOUT 1 (ANALYSEUR BCT) - 0-60ms
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R1.1 - SEUILS DE BIAIS BCT SPÉCIFIQUES
    # ────────────────────────────────────────────────────────────────────────
    bct_bias_detection_threshold: float = 0.3            # Seuil détection biais BCT (30%)
    bct_exploitation_base_confidence: float = 0.6        # Confiance base exploitation BCT (60%)
    
    # IMPAIR_5 (plus rare) - seuils plus sensibles
    rollout1_impair5_consecutive_rare: float = 0.6       # Seuil impair_5 consécutif rare (60%)
    rollout1_impair5_consecutive_very_rare: float = 0.8  # Seuil impair_5 consécutif très rare (80%)
    rollout1_impair5_bias_weight: float = 1.5            # Poids biais impair_5 (×1.5)
    
    # PAIR_4/PAIR_6 - seuils différenciés
    rollout1_pair4_frequency_threshold: float = 0.4      # Seuil fréquence pair_4 (40%)
    rollout1_pair6_frequency_threshold: float = 0.3      # Seuil fréquence pair_6 (30%)
    rollout1_pair46_alternation_threshold: float = 0.5   # Seuil alternance pair_4↔pair_6 (50%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R1.2 - SEUILS ÉTATS COMBINÉS BCT
    # ────────────────────────────────────────────────────────────────────────
    rollout1_combined_rare_threshold: float = 0.2        # Seuil états combinés rares (20%)
    rollout1_impair5_desync_bonus: float = 0.3           # Bonus impair_5_desync (30%)
    rollout1_pair6_sync_bonus: float = 0.2               # Bonus pair_6_sync (20%)
    rollout1_pair4_stability_bonus: float = 0.1          # Bonus stabilité pair_4 (10%)
    
    # ========================================================================
    # 🎯 SECTION R2 - ROLLOUT 2 (GÉNÉRATEUR BCT) - 60-110ms
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R2.1 - CONFIGURATION SÉQUENCES BCT
    # ────────────────────────────────────────────────────────────────────────
    rollout2_max_sequence_length: int = 5                # Longueur max séquences BCT (5)
    rollout2_min_bias_threshold: float = 0.25            # Seuil minimum biais pour génération (25%)
    rollout2_diversity_requirement: float = 0.3          # Exigence diversité séquences (30%)
    rollout2_base_quality_score: float = 0.7             # Score qualité de base (70%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R2.2 - STRATÉGIES GÉNÉRATION BCT
    # ────────────────────────────────────────────────────────────────────────
    rollout2_impair5_strategy_weight: float = 0.4        # Poids stratégie impair_5 (40%)
    rollout2_pair46_strategy_weight: float = 0.3         # Poids stratégie pair_4↔pair_6 (30%)
    rollout2_combined_strategy_weight: float = 0.3       # Poids stratégie états combinés (30%)
    
    # ========================================================================
    # 🎲 SECTION R3 - ROLLOUT 3 (PRÉDICTEUR BCT) - 110-170ms
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R3.1 - CRITÈRES ÉVALUATION BCT
    # ────────────────────────────────────────────────────────────────────────
    rollout3_so_quality_weight: float = 0.7              # Poids qualité S/O (70%)
    rollout3_bct_coherence_weight: float = 0.2           # Poids cohérence BCT (20%)
    rollout3_bias_exploitation_weight: float = 0.1       # Poids exploitation biais (10%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R3.2 - BONUS SPÉCIFIQUES BCT
    # ────────────────────────────────────────────────────────────────────────
    rollout3_impair5_bonus: float = 0.15                 # Bonus exploitation impair_5 (15%)
    rollout3_pair_alternation_bonus: float = 0.1         # Bonus alternance pair_4↔pair_6 (10%)
    rollout3_bias_confidence_factor: float = 0.2         # Facteur confiance biais (20%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 R3.3 - CONFIANCE ET LIMITES
    # ────────────────────────────────────────────────────────────────────────
    rollout3_base_confidence: float = 0.5                # Confiance de base (50%)
    rollout3_max_confidence: float = 0.95                # Confiance maximum (95%)
    rollout3_default_confidence: float = 0.3             # Confiance par défaut (30%)
    rollout3_evaluation_bonus_factor: float = 0.3        # Facteur bonus évaluation (30%)
    
    # ========================================================================
    # 🏗️ SECTION C0 - CLUSTER 0 UNIQUE BCT
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 C0.1 - SPÉCIALISATION CLUSTER 0 BCT
    # ────────────────────────────────────────────────────────────────────────
    cluster0_specialization: str = "structural_bias_exploitation"  # Spécialisation C0
    cluster0_focus_target: str = "impair_5_consecutive"            # Cible principale
    cluster0_secondary_target: str = "pair_4_6_alternation"       # Cible secondaire
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 C0.2 - FENÊTRES ANALYSE BCT
    # ────────────────────────────────────────────────────────────────────────
    cluster0_recent_window_size: int = 5                 # Fenêtre récente (5 mains)
    cluster0_sequence_window: int = 3                    # Fenêtre séquences (3)
    cluster0_accuracy_window: int = 10                   # Fenêtre précision (10)
    
    # ========================================================================
    # 🎯 SECTION BCT - SPÉCIFICITÉS SYSTÈME COMPTAGE BCT
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 BCT.1 - CATÉGORIES DE CARTES BCT
    # ────────────────────────────────────────────────────────────────────────
    bct_categories: List[str] = field(default_factory=lambda: ['pair_4', 'impair_5', 'pair_6'])
    bct_combined_states: List[str] = field(default_factory=lambda: [
        'pair_4_sync', 'pair_4_desync',
        'impair_5_sync', 'impair_5_desync',
        'pair_6_sync', 'pair_6_desync'
    ])
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 BCT.2 - POIDS CATÉGORIES BCT
    # ────────────────────────────────────────────────────────────────────────
    bct_pair_4_weight: float = 0.3                       # Poids pair_4 (30%)
    bct_impair_5_weight: float = 0.5                     # Poids impair_5 (50% - plus rare)
    bct_pair_6_weight: float = 0.2                       # Poids pair_6 (20%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 BCT.3 - SEUILS CONFORMITÉ BCT
    # ────────────────────────────────────────────────────────────────────────
    bct_conformity_threshold: float = 0.9                # Seuil conformité système (90%)
    bct_index_validation_threshold: float = 0.95         # Seuil validation INDEX (95%)
    
    # ========================================================================
    # 📊 SECTION P - PERFORMANCE ET MONITORING BCT
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 P.1 - MÉTRIQUES PERFORMANCE BCT
    # ────────────────────────────────────────────────────────────────────────
    performance_target_accuracy: float = 0.6             # Cible précision (60%)
    performance_min_accuracy: float = 0.5                # Précision minimum (50%)
    performance_excellent_threshold: float = 0.75        # Seuil excellence (75%)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 P.2 - MONITORING BIAIS BCT
    # ────────────────────────────────────────────────────────────────────────
    monitoring_bias_success_threshold: float = 0.7       # Seuil succès exploitation biais (70%)
    monitoring_impair5_rate_target: float = 0.3          # Cible taux impair_5 (30%)
    monitoring_pair46_alternation_target: float = 0.4    # Cible alternance pair_4↔pair_6 (40%)
    
    # ========================================================================
    # 🔧 SECTION S - CONFIGURATION BACCARAT BCT
    # ========================================================================
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 S.1 - PARAMÈTRES JEU BACCARAT
    # ────────────────────────────────────────────────────────────────────────
    max_manches_per_game: int = 80                       # Maximum manches par partie
    cut_card_position: int = 312                         # Position cut card (3/4 du sabot)
    
    # ────────────────────────────────────────────────────────────────────────
    # 🔸 S.2 - VALEURS PAR DÉFAUT BACCARAT
    # ────────────────────────────────────────────────────────────────────────
    default_burn_parity: str = 'PAIR'                    # Parité brûlage par défaut
    default_sync_state: str = 'SYNC'                     # État sync par défaut
    
    def __post_init__(self):
        """Initialisation post-création avec validations BCT"""
        # Validation cohérence timing
        total_rollouts_time = (self.cluster_analysis_time_ms + 
                              self.cluster_generation_time_ms + 
                              self.cluster_prediction_time_ms)
        
        if total_rollouts_time > self.cycle_total_time_ms:
            raise ValueError(f"Timing incohérent BCT: {total_rollouts_time}ms > {self.cycle_total_time_ms}ms")
        
        # Validation poids catégories BCT
        total_weight = (self.bct_pair_4_weight + 
                       self.bct_impair_5_weight + 
                       self.bct_pair_6_weight)
        
        if abs(total_weight - self.one_value) > 0.01:
            raise ValueError(f"Poids catégories BCT incohérents: {total_weight} ≠ 1.0")
        
        # Validation conformité système de référence
        if len(self.bct_categories) != 3:
            raise ValueError("BCT doit avoir exactement 3 catégories: pair_4, impair_5, pair_6")
        
        if len(self.bct_combined_states) != 6:
            raise ValueError("BCT doit avoir exactement 6 états combinés")
    
    def get_cluster_params(self, cluster_id: int) -> Dict[str, Any]:
        """Retourne les paramètres du cluster (uniquement cluster 0 pour BCT)"""
        if cluster_id != 0:
            raise ValueError(f"BCT ne supporte que le cluster 0, reçu: {cluster_id}")
        
        return {
            'name': 'BCT_Cluster_0_Structural_Bias',
            'specialization': self.cluster0_specialization,
            'focus_target': self.cluster0_focus_target,
            'secondary_target': self.cluster0_secondary_target,
            'recent_window_size': self.cluster0_recent_window_size,
            'sequence_window': self.cluster0_sequence_window,
            'accuracy_window': self.cluster0_accuracy_window,
            'bct_categories': self.bct_categories.copy(),
            'bct_combined_states': self.bct_combined_states.copy()
        }

# ================================================================
# CONFIGURATION BCT CENTRALISÉE - RÉSUMÉ
# ================================================================
# Cette configuration centralise TOUS les paramètres BCT :
# - Aucune valeur codée en dur dans les méthodes
# - Organisation par sections fonctionnelles
# - Spécialisation pour exploitation biais structurels BCT
# - Conformité parfaite au système de comptage de référence
# - Architecture simplifiée : 1 cluster × 3 rollouts
# ================================================================

RAPPORT D'EXTRACTION DES MÉTHODES
==================================================

Source : c:\Users\<USER>\Desktop\base\centralisation_methodes\class.txt
Destination : c:\Users\<USER>\Desktop\base\methodes_extraites
Total méthodes : 162

LISTE DES MÉTHODES EXTRAITES :
------------------------------
  1. __init__                                           (ligne    1,  28 lignes)
  2. execute_cluster_pipeline                           (ligne   29,  79 lignes)
  3. _rollout_analyzer                                  (ligne  108, 114 lignes)
  4. _rollout_analyzer_c3_patterns_moyens               (ligne  222, 191 lignes)
  5. _analyze_impair5_consecutive_bias                   (ligne  413, 228 lignes)
  6. _analyze_pair4_pair6_priority_2_autonomous                (ligne  641, 112 lignes)
  7. _analyze_sync_alternation_bias                     (ligne  753, 156 lignes)
  8. _rollout_analyzer_c2_patterns_courts               (ligne  909, 187 lignes)
  9. _analyze_impair5_consecutive_bias_c2_specialized    (ligne 1096, 111 lignes)
 10. _analyze_sync_alternation_bias_c2_specialized      (ligne 1207,  80 lignes)
 11. _apply_c2_short_patterns_specialization            (ligne 1287,  41 lignes)
 12. _generate_bias_signals_summary_c2                  (ligne 1328,  23 lignes)
 13. _generate_bias_generation_guidance_c2              (ligne 1351,  23 lignes)
 14. _generate_bias_quick_access_c2                     (ligne 1374,  28 lignes)
 15. _get_cluster_specialization_params                 (ligne 1402,  66 lignes)
 16. _create_generic_cluster_analyzer                   (ligne 1468, 184 lignes)
 17. _analyze_impair5_bias_specialized                   (ligne 1652,  48 lignes)
 18. _analyze_sync_bias_specialized                     (ligne 1700,  45 lignes)
 19. _apply_cluster_specialization                      (ligne 1745,  42 lignes)
 20. _correlate_impair5_with_sync                        (ligne 1787,  41 lignes)
 21. _correlate_impair5_with_combined                    (ligne 1828,  42 lignes)
 22. _correlate_impair5_with_pb                          (ligne 1870,  40 lignes)
 23. _correlate_impair5_with_so                          (ligne 1910,  40 lignes)
 24. _correlate_bias_to_pb_variations                   (ligne 1950,  51 lignes)
 25. _correlate_bias_to_so_variations                   (ligne 2001,  42 lignes)
 26. _analyze_impair5_consecutive_bias_c3_specialized    (ligne 2043,  85 lignes)
 27. _analyze_sync_alternation_bias_c3_specialized      (ligne 2128,  81 lignes)
 28. _apply_c3_medium_patterns_specialization           (ligne 2209,  41 lignes)
 29. _analyze_combined_structural_bias                  (ligne 2250, 124 lignes)
 30. _correlate_bias_to_pb_variations                   (ligne 2374,  68 lignes)
 31. _correlate_bias_to_so_variations                   (ligne 2442,  70 lignes)
 32. _generate_priority_based_synthesis_autonomous      (ligne 2512, 158 lignes)
 33. _generate_bias_exploitation_synthesis              (ligne 2670, 165 lignes)
 34. _generate_bias_signals_summary                     (ligne 2835,  16 lignes)
 35. _generate_bias_generation_guidance                 (ligne 2851,  13 lignes)
 36. _generate_bias_quick_access                        (ligne 2864,  13 lignes)
 37. _generate_complete_synthesis                       (ligne 2877,  90 lignes)
 38. _calculate_cross_index_impacts                     (ligne 2967, 100 lignes)
 39. _calculate_variations_impact                       (ligne 3067,  54 lignes)
 40. _calculate_global_strength_metrics                 (ligne 3121,  80 lignes)
 41. _rollout_generator                                 (ligne 3201, 107 lignes)
 42. _rollout_predictor                                 (ligne 3308, 129 lignes)
 43. _evaluate_sequence_quality                         (ligne 3437,  36 lignes)
 44. _evaluate_signal_alignment                         (ligne 3473,  56 lignes)
 45. _evaluate_fallback_alignment                       (ligne 3529,  24 lignes)
 46. _analyze_sequence_consistency                      (ligne 3553,  60 lignes)
 47. _assess_risk_reward_ratio                          (ligne 3613,  46 lignes)
 48. _validate_sequence_logic                           (ligne 3659,  61 lignes)
 49. _calculate_sequence_score                          (ligne 3720,  29 lignes)
 50. _select_best_sequence                              (ligne 3749,  39 lignes)
 51. _calculate_cluster_confidence                      (ligne 3788,  45 lignes)
 52. _calculate_cluster_confidence_azr_calibrated       (ligne 3833,  83 lignes)
 53. _calculate_confidence_risk_factors                 (ligne 3916,  59 lignes)
 54. _calculate_epistemic_uncertainty                   (ligne 3975,  38 lignes)
 55. _calculate_rollout_consensus                       (ligne 4013,  49 lignes)
 56. _extract_next_hand_prediction                      (ligne 4062,  34 lignes)
 57. _convert_pb_sequence_to_so                         (ligne 4096,  50 lignes)
 58. _get_last_historical_pb_result                     (ligne 4146,  32 lignes)
 59. calculate_rollout2_reward                          (ligne 4178,  69 lignes)
 60. calculate_rollout2_sequence_quality                (ligne 4247,  41 lignes)
 61. calculate_rollout2_diversity_score                 (ligne 4288,  38 lignes)
 62. calculate_rollout3_reward                          (ligne 4326,  88 lignes)
 63. calculate_rollout3_risk_factor                     (ligne 4414,  41 lignes)
 64. calculate_cluster_total_reward                     (ligne 4455,  76 lignes)
 65. _define_optimized_generation_space                 (ligne 4531,  37 lignes)
 66. _generate_sequences_from_signals                   (ligne 4568,  52 lignes)
 67. _generate_sequence_from_signal                     (ligne 4620,  34 lignes)
 68. _generate_fallback_sequences                       (ligne 4654,  46 lignes)
 69. _classify_confidence_level                         (ligne 4700,  19 lignes)
 70. _generate_so_based_sequence                        (ligne 4719,  41 lignes)
 71. _generate_all_possible_sequences                   (ligne 4760,  73 lignes)
 72. _convert_pb_sequence_to_so_with_history            (ligne 4833,  28 lignes)
 73. _calculate_sequence_probability                    (ligne 4861,  92 lignes)
 74. _calculate_sequence_quality_metrics                (ligne 4953,  47 lignes)
 75. _generate_pb_sequence                              (ligne 5000,  47 lignes)
 76. _generate_pair_sync_sequence                       (ligne 5047,  42 lignes)
 77. _generate_impair_sync_sequence                     (ligne 5089,  45 lignes)
 78. _generate_generic_signal_sequence                  (ligne 5134,  55 lignes)
 79. _analyze_complete_impair_pair_index                (ligne 5189,  70 lignes)
 80. _analyze_complete_desync_sync_index                (ligne 5259,  50 lignes)
 81. _analyze_complete_combined_index                   (ligne 5309,  37 lignes)
 82. _analyze_complete_pbt_index                        (ligne 5346,  48 lignes)
 83. _analyze_complete_so_index                         (ligne 5394,  56 lignes)
 84. _synthesize_complete_analysis                      (ligne 5450,  57 lignes)
 85. _analyze_complete_cross_impacts                    (ligne 5507,  85 lignes)
 86. _analyze_impair5_pair_to_so_impact                  (ligne 5592,  28 lignes)
 87. _analyze_desync_sync_to_pbt_impact                 (ligne 5620,  34 lignes)
 88. _identify_desync_periods                           (ligne 5654,  24 lignes)
 89. _analyze_desync_sync_to_so_impact                  (ligne 5678,  28 lignes)
 90. _analyze_combined_to_pbt_impact                    (ligne 5706,  44 lignes)
 91. _analyze_combined_to_so_impact                     (ligne 5750,  36 lignes)
 92. _analyze_tri_dimensional_impacts                   (ligne 5786,  42 lignes)
 93. _analyze_variations_impact_on_outcomes             (ligne 5828,  65 lignes)
 94. _analyze_consecutive_length_impact                 (ligne 5893,  72 lignes)
 95. _find_consecutive_sequences_with_positions         (ligne 5965,  31 lignes)
 96. _find_consecutive_sequences                        (ligne 5996,  27 lignes)
 97. _calculate_asymmetric_impair_alert_level           (ligne 6023,  18 lignes)
 98. _calculate_asymmetric_pair_alert_level             (ligne 6041,  18 lignes)
 99. _calculate_impair_rarity_score                     (ligne 6059,  19 lignes)
100. _calculate_pair_commonality_score                  (ligne 6078,  17 lignes)
101. _calculate_asymmetric_significance                 (ligne 6095,  28 lignes)
102. _identify_dominant_desync_sync_so_pattern          (ligne 6123,  56 lignes)
103. _calculate_combined_so_impact_strength             (ligne 6179,  64 lignes)
104. _calculate_combined_pbt_impact_strength            (ligne 6243,  71 lignes)
105. _identify_dominant_impair_pair_so_pattern          (ligne 6314,  56 lignes)
106. _calculate_overall_impact_strength                 (ligne 6370,  97 lignes)
107. _analyze_transition_moments_impact                 (ligne 6467, 165 lignes)
108. _calculate_distribution                            (ligne 6632,  52 lignes)
109. _analyze_desync_periods_impact                     (ligne 6684, 256 lignes)
110. _analyze_combined_state_changes_impact             (ligne 6940, 283 lignes)
111. _analyze_temporal_correlation_evolution            (ligne 7223, 201 lignes)
112. _calculate_phase_impair_pair_pb_correlation        (ligne 7424,  57 lignes)
113. _calculate_phase_impair_pair_so_correlation        (ligne 7481,  54 lignes)
114. _calculate_phase_sync_desync_pb_correlation        (ligne 7535,  57 lignes)
115. _calculate_phase_sync_desync_so_correlation        (ligne 7592,  54 lignes)
116. _calculate_phase_correlation_strength              (ligne 7646,  26 lignes)
117. _analyze_correlation_trend                         (ligne 7672,  35 lignes)
118. _calculate_correlation_stability                   (ligne 7707,  13 lignes)
119. _calculate_variance                                (ligne 7720,  11 lignes)
120. _generate_temporal_recommendation                  (ligne 7731,  17 lignes)
121. _calculate_evolution_strength                      (ligne 7748,  16 lignes)
122. _calculate_temporal_consistency                    (ligne 7764,  17 lignes)
123. _calculate_temporal_predictability                 (ligne 7781,  21 lignes)
124. _calculate_variation_strength_analysis             (ligne 7802, 172 lignes)
125. _extract_consecutive_length_strength               (ligne 7974,  37 lignes)
126. _extract_transition_moments_strength               (ligne 8011,  31 lignes)
127. _extract_desync_periods_strength                   (ligne 8042,  30 lignes)
128. _extract_combined_state_changes_strength           (ligne 8072,  21 lignes)
129. _extract_temporal_evolution_strength               (ligne 8093,  18 lignes)
130. _calculate_confidence_level                        (ligne 8111,  14 lignes)
131. _generate_exploitation_recommendation              (ligne 8125,  18 lignes)
132. _identify_best_prediction_context                  (ligne 8143,  27 lignes)
133. _calculate_strength_distribution                   (ligne 8170,  17 lignes)
134. _calculate_variation_consistency                   (ligne 8187,  18 lignes)
135. _assess_sample_size_adequacy                       (ligne 8205,  24 lignes)
136. _calculate_statistical_significance                (ligne 8229,  10 lignes)
137. _calculate_pattern_stability                       (ligne 8239,  12 lignes)
138. _assess_overall_quality                            (ligne 8251,  17 lignes)
139. _identify_enhanced_dominant_correlations           (ligne 8268, 298 lignes)
140. _identify_enhanced_high_confidence_zones           (ligne 8566, 314 lignes)
141. _define_complete_generation_space_DEPRECATED       (ligne 8880, 287 lignes)
142. _generate_impair_pair_optimized_sequence           (ligne 9167, 179 lignes)
143. _generate_sync_based_sequence                      (ligne 9346, 204 lignes)
144. _generate_combined_index_sequence                  (ligne 9550, 237 lignes)
145. _generate_so_pattern_sequence                      (ligne 9787, 247 lignes)
146. _enrich_sequences_with_complete_indexes            (ligne 10034, 257 lignes)
147. _classify_combined_transition_type                 (ligne 10291,  37 lignes)
148. get_max_sequence_length                            (ligne 10328,  15 lignes)
149. get_max_so_conversions                             (ligne 10343,  15 lignes)
150. is_game_complete                                   (ligne 10358,  21 lignes)
151. _generate_signals_summary                          (ligne 10379, 117 lignes)
152. _generate_generation_guidance                      (ligne 10496, 105 lignes)
153. _generate_quick_access                             (ligne 10601, 101 lignes)
154. _update_performance_metrics                        (ligne 10702,  19 lignes)
155. _count_consecutive_pattern                         (ligne 10721,  17 lignes)
156. _calculate_rupture_probability                     (ligne 10738,  30 lignes)
157. _analyze_correlations_std_dev                      (ligne 10768,  34 lignes)
158. _identify_improbability_zones                      (ligne 10802,  47 lignes)
159. _evaluate_sequence_quality                         (ligne 10849,  43 lignes)
160. _select_best_sequence                              (ligne 10892,  14 lignes)
161. _calculate_cluster_confidence                      (ligne 10906,  21 lignes)
162. _extract_next_hand_prediction                      (ligne 10927,  26 lignes)

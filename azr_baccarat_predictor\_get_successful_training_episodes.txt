# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 15011 à 15042
# Type: Méthode de la classe AZRBaccaratPredictor

    def _get_successful_training_episodes(self) -> List[Dict]:
        """
        💾 MÉMOIRE ÉPISODIQUE : Récupère les épisodes d'entraînement réussis

        Returns:
            Liste des séquences d'entraînement avec taux de succès > 60%
        """
        successful_episodes = []

        # Analyser l'historique des prédictions pour identifier les épisodes réussis
        if len(self.predictions_history) >= 10:
            # Découper en épisodes de 10 prédictions
            episode_size = 10
            for i in range(0, len(self.predictions_history) - episode_size + 1, episode_size):
                episode_predictions = self.predictions_history[i:i + episode_size]
                episode_accuracy = self.accuracy_history[i:i + episode_size] if i + episode_size <= len(self.accuracy_history) else []

                if episode_accuracy:
                    avg_accuracy = sum(episode_accuracy) / len(episode_accuracy)

                    # Sauvegarder si taux de succès > seuil configuré
                    if avg_accuracy > self.config.cluster_accuracy_save_threshold:
                        successful_episodes.append({
                            'episode_id': f"episode_{i//episode_size}",
                            'predictions': episode_predictions,
                            'accuracy': avg_accuracy,
                            'size': len(episode_predictions),
                            'timestamp': datetime.now().isoformat()
                        })

        # Limiter à 50 épisodes les plus récents
        return successful_episodes[-50:] if successful_episodes else []
# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 17133 à 17256
# Type: Méthode de la classe AZRBaccaratPredictor

    def benchmark_cpu_performance(self, test_rollouts: int = 100, test_generation: int = 1000) -> Dict[str, Any]:
        """
        Benchmark des optimisations CPU pour valider les performances

        Args:
            test_rollouts: Nombre de rollouts à tester
            test_generation: Nombre de parties à générer pour test

        Returns:
            Dictionnaire avec résultats du benchmark
        """
        logger.info(f"🏁 BENCHMARK CPU - Test des optimisations 8 cœurs + 28GB RAM")

        import time
        benchmark_results = {}

        # Test 1: Performance des rollouts parallèles vs séquentiels
        logger.info("🔄 Test 1: Rollouts parallèles vs séquentiels")

        # Sauvegarder config actuelle
        original_parallel = self.config.parallel_rollouts
        original_n_rollouts = self.config.n_rollouts

        # Test séquentiel
        self.config.parallel_rollouts = False
        self.config.n_rollouts = test_rollouts

        start_time = time.time()
        sequential_hypotheses = self._generate_rollout_hypotheses()
        sequential_time = time.time() - start_time

        # Test parallèle
        self.config.parallel_rollouts = True

        start_time = time.time()
        parallel_hypotheses = self._generate_rollout_hypotheses()
        parallel_time = time.time() - start_time

        # Calculer l'amélioration
        speedup = sequential_time / parallel_time if parallel_time > 0 else 0
        efficiency = speedup / self.config.cpu_cores * 100

        benchmark_results['rollouts_test'] = {
            'sequential_time': sequential_time,
            'parallel_time': parallel_time,
            'speedup': speedup,
            'efficiency_percent': efficiency,
            'rollouts_tested': test_rollouts,
            'sequential_hypotheses_count': len(sequential_hypotheses),
            'parallel_hypotheses_count': len(parallel_hypotheses)
        }

        # Test 2: Génération massive avec monitoring mémoire
        logger.info("🎲 Test 2: Génération massive avec optimisations mémoire")

        # Monitoring mémoire avant
        memory_before = psutil.virtual_memory()

        start_time = time.time()
        test_data = self.generate_massive_data_optimized(test_generation, 30)  # Parties courtes pour test
        generation_time = time.time() - start_time

        # Monitoring mémoire après
        memory_after = psutil.virtual_memory()

        benchmark_results['generation_test'] = {
            'generation_time': generation_time,
            'games_generated': len(test_data['games']),
            'games_per_second': len(test_data['games']) / generation_time if generation_time > 0 else 0,
            'memory_before_gb': memory_before.used / (1024**3),
            'memory_after_gb': memory_after.used / (1024**3),
            'memory_increase_gb': (memory_after.used - memory_before.used) / (1024**3),
            'memory_per_game_mb': ((memory_after.used - memory_before.used) / len(test_data['games'])) / (1024**2) if test_data['games'] else 0
        }

        # Test 3: Performance globale du système
        logger.info("⚡ Test 3: Performance globale système")

        cpu_before = psutil.cpu_percent(interval=1)

        # Simulation d'utilisation intensive
        start_time = time.time()
        for i in range(10):
            prediction = self._generate_azr_prediction()
            if i % 3 == 0:
                gc.collect()  # Test garbage collection

        system_test_time = time.time() - start_time
        cpu_after = psutil.cpu_percent(interval=1)

        benchmark_results['system_test'] = {
            'predictions_time': system_test_time,
            'predictions_per_second': 10 / system_test_time if system_test_time > 0 else 0,
            'cpu_before_percent': cpu_before,
            'cpu_after_percent': cpu_after,
            'cpu_increase': cpu_after - cpu_before
        }

        # Restaurer configuration
        self.config.parallel_rollouts = original_parallel
        self.config.n_rollouts = original_n_rollouts

        # Résumé du benchmark
        benchmark_results['summary'] = {
            'total_benchmark_time': sum([
                benchmark_results['rollouts_test']['sequential_time'],
                benchmark_results['rollouts_test']['parallel_time'],
                benchmark_results['generation_test']['generation_time'],
                benchmark_results['system_test']['predictions_time']
            ]),
            'parallel_efficiency': benchmark_results['rollouts_test']['efficiency_percent'],
            'memory_efficiency': benchmark_results['generation_test']['memory_per_game_mb'],
            'overall_performance': 'Excellent' if speedup > 4 else 'Good' if speedup > 2 else 'Needs Optimization'
        }

        # Logging des résultats
        logger.info(f"🏁 BENCHMARK TERMINÉ:")
        logger.info(f"   Speedup rollouts: {speedup:.2f}x")
        logger.info(f"   Efficacité parallèle: {efficiency:.1f}%")
        logger.info(f"   Parties/seconde: {benchmark_results['generation_test']['games_per_second']:.1f}")
        logger.info(f"   Mémoire/partie: {benchmark_results['generation_test']['memory_per_game_mb']:.2f} MB")
        logger.info(f"   Performance globale: {benchmark_results['summary']['overall_performance']}")

        return benchmark_results
MÉTHODE : _correlate_impair5_with_sync
LIGNE DÉBUT : 1787
SIGNATURE : def _correlate_impair5_with_sync(self, isolated_impairs: List, consecutive_sequences: List, sync_states: List) -> Dict:
================================================================================

    def _correlate_impair5_with_sync(self, isolated_impairs: List, consecutive_sequences: List, sync_states: List) -> Dict:
"""
    ADAPTATION BCT - _correlate_impair_with_sync.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Corrèle les IMPAIRS avec les états SYNC/DESYNC"""
        correlation = {
            'correlation_strength': 0.0,
            'sync_after_impairs': 0,
            'desync_after_impairs': 0,
            'dominant_pattern': 'SYNC'
        }

        # Analyser les états SYNC/DESYNC après chaque IMPAIR
        total_correlations = 0
        desync_count = 0

        # IMPAIRS isolés
        for pos in isolated_impairs:
            if pos - int(self.config.rollout_analyzer_position_offset) < len(sync_states):  # Index 0-based
                if sync_states[pos - int(self.config.rollout_analyzer_position_offset)] == 'DESYNC':
                    desync_count += int(self.config.rollout_reward_valid_sequence_increment)
                total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        # Séquences d'IMPAIRS
        for seq in consecutive_sequences:
            for pos in seq:
                if pos - int(self.config.rollout_analyzer_position_offset) < len(sync_states):
                    if sync_states[pos - int(self.config.rollout_analyzer_position_offset)] == 'DESYNC':
                        desync_count += int(self.config.rollout_reward_valid_sequence_increment)
                    total_correlations += int(self.config.rollout_reward_valid_sequence_increment)

        if total_correlations > 0:
            desync_ratio = desync_count / total_correlations
            correlation['correlation_strength'] = abs(desync_ratio - self.config.rollout_analyzer_normality_threshold)  # Écart à la normalité
            correlation['desync_after_impairs'] = desync_count
            correlation['sync_after_impairs'] = total_correlations - desync_count

            if desync_ratio > self.config.rollout_analyzer_normality_threshold:
                correlation['dominant_pattern'] = 'DESYNC'
            else:
                correlation['dominant_pattern'] = 'SYNC'

        return correlation


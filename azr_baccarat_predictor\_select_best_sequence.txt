# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 6213 à 6250
# Type: Méthode de la classe AZRCluster

    def _select_best_sequence(self, evaluated_sequences: List[Dict]) -> Dict:
        """
        Sélectionne la meilleure séquence parmi les candidates évaluées

        FOCUS : Sélection intelligente basée sur les scores d'évaluation
        """
        if not evaluated_sequences:
            return {
                'sequence_data': ['B'],  # Fallback par défaut
                'strategy': 'fallback_default',
                'justification': 'Aucune séquence candidate disponible',
                'estimated_probability': self.config.rollout3_default_confidence,
                'evaluation': {'total_score': 0.0}
            }

        # Trier les séquences par score total décroissant
        sorted_sequences = sorted(
            evaluated_sequences,
            key=lambda x: x.get('evaluation', {}).get('total_score', 0.0),
            reverse=True
        )

        best_sequence = sorted_sequences[0]

        # Vérification de qualité minimale
        best_score = best_sequence.get('evaluation', {}).get('total_score', 0.0)

        if best_score < self.config.rollout3_minimum_quality_threshold:
            # Score trop faible, utiliser une séquence conservative
            return {
                'sequence_data': ['P', 'B'],  # Alternance conservative
                'strategy': 'conservative_fallback',
                'justification': f'Score qualité insuffisant ({best_score:.3f}), utilisation fallback conservatif',
                'estimated_probability': self.config.rollout3_conservative_probability,
                'evaluation': {'total_score': self.config.rollout3_minimum_quality_threshold}
            }

        return best_sequence
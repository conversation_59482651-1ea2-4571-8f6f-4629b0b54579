# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14038 à 14043
# Type: Méthode de la classe BaccaratDataLoader

    def get_hands_iterator(self, game_number: int) -> Iterator[Dict]:
        """Retourne un itérateur pour parcourir les manches d'une partie"""
        game = self.get_game(game_number)
        if game and 'hands' in game:
            for hand in game['hands']:
                yield hand.copy()
MÉTHODE : _calculate_statistical_significance
LIGNE DÉBUT : 8229
SIGNATURE : def _calculate_statistical_significance(self, individual_strengths: Dict, variations_impact: Dict) -> float:
================================================================================

    def _calculate_statistical_significance(self, individual_strengths: Dict, variations_impact: Dict) -> float:
"""
    ADAPTATION BCT - _calculate_statistical_significance.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """Calcule la significativité statistique globale"""

        # Significativité basée sur force et échantillons
        strength_component = sum(individual_strengths.values()) / len(individual_strengths) if individual_strengths else 0
        sample_component = self._assess_sample_size_adequacy(variations_impact)

        # Moyenne pondérée (force 60%, échantillons 40%)
        return strength_component * self.config.rollout2_confidence_value_standard + sample_component * self.config.rollout3_quality_bonus_medium


# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 14518 à 14573
# Type: Méthode de la classe AZRBaccaratInterface

    def _reset_current_game_only(self):
        """
        🔄 RESET PARTIE COURANTE SEULEMENT

        Réinitialise uniquement les éléments de la partie en cours :
        - Interface (compteurs, boutons)
        - Données de la partie courante
        - Session AZR courante (SANS perdre l'intelligence globale)

        PRÉSERVE :
        - Toute l'intelligence acquise
        - Patterns découverts
        - Baselines adaptatifs
        - Métriques globales
        """
        # Réinitialiser l'interface selon la logique de référence AZR
        self.burn_initialized = False
        self.current_pb_number = self.config.zero_value  # Recommence à 0
        self.current_sync_state = 'SYNC'
        self.last_pb_result = None
        self.last_so_conversion = '--'

        # Réinitialiser les données de la partie
        self.current_game = {
            'game_number': self.config.one_value,
            'initialization': {
                'burn_cards_count': self.config.zero_value,
                'burn_parity': 'PAIR',
                'initial_sync_state': 'SYNC'
            },
            'statistics': {
                'total_hands': self.config.zero_value,
                'pb_hands': self.config.zero_value,
                'tie_hands': self.config.zero_value,
                'so_conversions': self.config.zero_value
            },
            'hands': []
        }

        # RESET SESSION SEULEMENT (préserve l'intelligence globale)
        if self.azr_predictor:
            self.azr_predictor.reset_session()

        # Remettre le compteur à 1 avec limite et réinitialiser la prédiction
        max_manches = self.config.max_manches_per_game
        self.counter_label.config(text=f"Manche P/B: 1 / {max_manches} (reste: {max_manches})")
        self.prediction_label.config(text="Prédiction: Attendre", foreground="blue")

        # Réactiver les boutons de cartes brûlées
        self.burn_pair_btn.config(state=tk.NORMAL)
        self.burn_impair_btn.config(state=tk.NORMAL)

        # Réactiver tous les boutons de jeu
        self._enable_all_buttons()

        logger.info("💾 Partie courante réinitialisée après sauvegarde - Intelligence préservée")
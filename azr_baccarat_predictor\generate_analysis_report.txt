# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 19441 à 19548
# Type: Méthode

def generate_analysis_report(detailed_stats: Dict, test_games: List[Dict], test_metrics: Dict, sequence_patterns: Dict = None) -> Dict:
    """
    Génère un rapport d'analyse complet exploitable

    Args:
        detailed_stats: Statistiques détaillées des désynchronisations
        test_games: Données des parties de test
        test_metrics: Métriques de performance du modèle
        sequence_patterns: Analyse des séquences prolongées (optionnel)

    Returns:
        Rapport d'analyse complet
    """
    # Calculer des métriques avancées
    sync_stats = detailed_stats['sync_states']

    # Différence d'impact entre SYNC et DESYNC
    sync_s_rate = sync_stats['SYNC']['S_rate']
    sync_o_rate = sync_stats['SYNC']['O_rate']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    desync_o_rate = sync_stats['DESYNC']['O_rate']

    # Calcul du biais de désynchronisation
    desync_bias = {
        'S_bias': desync_s_rate - sync_s_rate,
        'O_bias': desync_o_rate - sync_o_rate,
        'total_bias': abs(desync_s_rate - sync_s_rate) + abs(desync_o_rate - sync_o_rate)
    }

    # Analyse des séquences
    consecutive_sync = detailed_stats['sequence_analysis']['consecutive_sync']
    consecutive_desync = detailed_stats['sequence_analysis']['consecutive_desync']

    sequence_stats = {
        'avg_sync_length': sum(consecutive_sync) / len(consecutive_sync) if consecutive_sync else 0,
        'avg_desync_length': sum(consecutive_desync) / len(consecutive_desync) if consecutive_desync else 0,
        'max_sync_length': max(consecutive_sync) if consecutive_sync else 0,
        'max_desync_length': max(consecutive_desync) if consecutive_desync else 0
    }

    # Analyse par partie
    game_stats = detailed_stats['game_statistics']
    avg_desync_impact = sum(g['desync_impact'] for g in game_stats) / len(game_stats) if game_stats else 0

    # Recommandations d'ajustement des paramètres
    recommendations = generate_parameter_recommendations(detailed_stats, test_metrics)

    report = {
        'metadata': {
            'timestamp': datetime.now().isoformat(),
            'total_games_analyzed': len(test_games),
            'total_hands_analyzed': detailed_stats['total_hands'],
            'analysis_version': '1.0'
        },
        'model_performance': {
            'test_accuracy': test_metrics['test_accuracy'],
            'accuracy_std': test_metrics['accuracy_std'],
            'min_accuracy': test_metrics['min_game_accuracy'],
            'max_accuracy': test_metrics['max_game_accuracy']
        },
        'sync_desync_analysis': {
            'sync_state_distribution': {
                'SYNC_percentage': sync_stats['SYNC']['total'] / detailed_stats['total_hands'] * 100,
                'DESYNC_percentage': sync_stats['DESYNC']['total'] / detailed_stats['total_hands'] * 100
            },
            'conversion_rates': {
                'SYNC': {'S_rate': sync_s_rate, 'O_rate': sync_o_rate},
                'DESYNC': {'S_rate': desync_s_rate, 'O_rate': desync_o_rate}
            },
            'desync_bias': desync_bias,
            'transition_patterns': detailed_stats['transition_patterns']
        },
        'sequence_analysis': sequence_stats,
        'parity_impact': detailed_stats['parity_analysis'],
        'desync_triggers': detailed_stats['desync_triggers'],
        'game_level_stats': {
            'avg_desync_impact': avg_desync_impact,
            'games_with_high_desync': len([g for g in game_stats if g['desync_impact'] > 0.4]),
            'avg_s_conversions_per_game': sum(g['S_conversions'] for g in game_stats) / len(game_stats),
            'avg_o_conversions_per_game': sum(g['O_conversions'] for g in game_stats) / len(game_stats)
        },
        'parameter_recommendations': recommendations,
        'raw_statistics': detailed_stats
    }

    # Ajouter l'analyse des séquences prolongées si disponible
    if sequence_patterns:
        report['sequence_patterns_analysis'] = {
            'long_sequences_summary': {
                'sync_sequences_count': len(sequence_patterns['long_sync_sequences']),
                'desync_sequences_count': len(sequence_patterns['long_desync_sequences']),
                'longest_sync': sequence_patterns['long_sync_sequences'][0]['length'] if sequence_patterns['long_sync_sequences'] else 0,
                'longest_desync': sequence_patterns['long_desync_sequences'][0]['length'] if sequence_patterns['long_desync_sequences'] else 0
            },
            'prolonged_patterns': {
                'sync_prolonged_S_rate': sequence_patterns['sync_patterns']['S_rate'],
                'sync_prolonged_O_rate': sequence_patterns['sync_patterns']['O_rate'],
                'desync_prolonged_S_rate': sequence_patterns['desync_patterns']['S_rate'],
                'desync_prolonged_O_rate': sequence_patterns['desync_patterns']['O_rate']
            },
            'exploitable_patterns': sequence_patterns['exploitable_patterns'],
            'detailed_sequences': {
                'top_sync_sequences': sequence_patterns['long_sync_sequences'][:10],
                'top_desync_sequences': sequence_patterns['long_desync_sequences'][:10]
            }
        }

    return report
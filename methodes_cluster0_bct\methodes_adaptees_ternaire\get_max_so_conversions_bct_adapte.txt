MÉTHODE : get_max_so_conversions
LIGNE DÉBUT : 10343
SIGNATURE : def get_max_so_conversions(self, mode: str = "real") -> int:
================================================================================

    def get_max_so_conversions(self, mode: str = "real") -> int:
"""
    ADAPTATION BCT - get_max_so_conversions.txt
    
    TRANSFORMATIONS APPLIQUÉES:
    - Système binaire PAIR/IMPAIR → Système ternaire pair_4/impair_5/pair_6
    - PAIR ancien → pair_4 (4 cartes) + pair_6 (6 cartes)
    - IMPAIR ancien → impair_5 (5 cartes)
    - Asymétrie: impair_5 = 30x plus significatif que pair_4/pair_6
    - États combinés: 6 états au lieu de 4
    - Corrélations adaptées au système ternaire
    
    CONFIGURATION BCT:
    - Cluster 0 uniquement (3 rollouts)
    - Timing optimisé ≤ 170ms total
    - Paramètres centralisés dans AZRConfig
    """
        """
        Retourne la limite de conversions S/O selon le mode

        Args:
            mode: "real" pour mode réel (59 S/O), "training" pour entraînement (cut card - 1)

        Returns:
            Limite maximale de conversions S/O pour la partie
        """
        if mode == "training":
            return self.config.max_sequence_length_training - 1  # Cut card - 1
        else:  # mode == "real"
            return self.config.max_so_conversions_real           # 59 S/O max


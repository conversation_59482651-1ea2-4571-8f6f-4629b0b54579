# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 5513 à 5565
# Type: Méthode de la classe AZRCluster

    def _calculate_variations_impact(self, all_indices: Dict) -> Dict:
        """
        Calcule l'impact des variations temporelles sur les corrélations

        FOCUS : Évolution des patterns dans le temps
        """
        variations_impact = {
            'temporal_correlation_evolution': {},
            'pattern_stability': {},
            'optimal_prediction_windows': {}
        }

        # Analyser l'évolution temporelle des corrélations IMPAIR/PAIR
        impair_pair = all_indices.get('impair_pair', {})
        position_types = impair_pair.get('position_types', [])
        pbt_outcomes = impair_pair.get('pbt_outcomes', [])

        if len(position_types) == len(pbt_outcomes) and len(position_types) > 10:
            # Diviser en phases temporelles
            total_hands = len(position_types)
            phase_size = max(5, total_hands // 3)  # Au moins 5 mains par phase

            phases_correlation = []
            for phase_start in range(0, total_hands - phase_size + 1, phase_size):
                phase_end = min(phase_start + phase_size, total_hands)

                phase_positions = position_types[phase_start:phase_end]
                phase_outcomes = pbt_outcomes[phase_start:phase_end]

                # Calculer corrélation pour cette phase
                impair_p = sum(1 for i, pos in enumerate(phase_positions)
                              if pos == 'IMPAIR' and i < len(phase_outcomes) and phase_outcomes[i] == 'P')
                impair_total = sum(1 for pos in phase_positions if pos == 'IMPAIR')

                if impair_total > 0:
                    phase_correlation = impair_p / impair_total
                    phases_correlation.append(phase_correlation)

            # Analyser la stabilité
            if len(phases_correlation) >= 2:
                correlation_variance = sum((c - 0.5) ** 2 for c in phases_correlation) / len(phases_correlation)
                correlation_stability = max(0, 1 - (correlation_variance * 4))  # Normalisation

                variations_impact['temporal_correlation_evolution'] = {
                    'phases_analyzed': len(phases_correlation),
                    'correlation_stability': correlation_stability,
                    'optimal_prediction_phases': {
                        'best_overall_phase': 'middle' if correlation_stability > self.config.stability_threshold_medium else 'early',
                        'stability_score': correlation_stability
                    }
                }

        return variations_impact
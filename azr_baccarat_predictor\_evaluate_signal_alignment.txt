# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\A\Nouveau dossier\azr_baccarat_predictor.py
# Lignes: 5937 à 5991
# Type: Méthode de la classe AZRCluster

    def _evaluate_signal_alignment(self, sequence: Dict, analyzer_report: Dict) -> float:
        """
        Évalue l'alignement de la séquence avec les signaux du Rollout 1

        FOCUS : Vérifier que la séquence exploite bien les découvertes du Rollout 1
        """
        alignment_score = 0.0

        # Récupérer les signaux optimisés du Rollout 1
        signals_summary = analyzer_report.get('signals_summary', {})
        top_signals = signals_summary.get('top_signals', [])

        if not top_signals:
            # Pas de signaux optimisés, utiliser les données détaillées
            return self._evaluate_fallback_alignment(sequence, analyzer_report)

        # Analyser l'alignement avec chaque signal
        signal_alignments = []

        for signal in top_signals:
            signal_name = signal.get('signal_name', '')
            signal_strength = signal.get('strength', 0.0)
            signal_confidence = signal.get('confidence', 0.0)

            # Vérifier si la séquence exploite ce signal
            sequence_strategy = sequence.get('strategy', '')

            alignment = 0.0

            # Alignement IMPAIR/PAIR
            if 'IMPAIR' in signal_name and 'impair' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence
            elif 'PAIR' in signal_name and 'pair' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence

            # Alignement SYNC/DESYNC
            elif 'SYNC' in signal_name and 'sync' in sequence_strategy.lower():
                alignment = signal_strength * signal_confidence

            # Alignement S/O
            elif 'SAME' in signal_name or 'OPPOSITE' in signal_name:
                if 'so' in sequence_strategy.lower():
                    alignment = signal_strength * signal_confidence

            # Alignement générique basé sur la stratégie
            elif signal.get('strategy', '') == sequence_strategy:
                alignment = signal_strength * signal_confidence * self.config.rollout2_base_confidence_high  # Bonus pour correspondance exacte

            signal_alignments.append(alignment)

        # Score d'alignement global (moyenne pondérée)
        if signal_alignments:
            alignment_score = sum(signal_alignments) / len(signal_alignments)

        return min(1.0, alignment_score)